"""
数据源模块

提供统一的数据访问接口，支持多种数据源：
- AkShare: A股数据
- 数据库: 本地数据存储
- Mock: 模拟数据（用于测试）
- 文件: CSV/Excel等文件数据源
"""

from .base import DataSource, DataAdapter
from .akshare_source import AkShareDataSource
from .database_source import DatabaseDataSource
from .mock_source import MockDataSource
from .file_source import FileDataSource
from .adapter import StandardDataAdapter
from .factory import DataSourceFactory

__all__ = [
    "DataSource",
    "DataAdapter",
    "AkShareDataSource",
    "DatabaseDataSource", 
    "MockDataSource",
    "FileDataSource",
    "StandardDataAdapter",
    "DataSourceFactory",
]
