"""
AkShare数据源

基于AkShare的A股数据源实现。
"""

from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd
import numpy as np
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from dataseed.base import DataSource
from utils.logger import get_logger

logger = get_logger(__name__)

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    logger.warning("AkShare未安装，AkShare数据源不可用")


class AkShareDataSource(DataSource):
    """AkShare数据源

    基于AkShare库获取A股实时数据。

    支持的数据类型：
    - A股日线数据
    - 指数数据
    - 基金数据
    """

    def __init__(self, name: str = "akshare", **kwargs):
        """
        初始化AkShare数据源

        Args:
            name: 数据源名称
            **kwargs: 其他参数
        """
        if not AKSHARE_AVAILABLE:
            raise ImportError("AkShare未安装，请先安装: pip install akshare")

        super().__init__(name, **kwargs)

        # AkShare特定配置
        self.symbol_cache = {}
        self.market_info = {}

        logger.info("AkShare数据源初始化完成")

    def _fetch_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """
        从AkShare获取数据

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            **kwargs: 其他参数

        Returns:
            原始数据DataFrame
        """
        try:
            # 转换日期格式
            start_str = self._format_date(start_date)
            end_str = self._format_date(end_date)

            # 标准化股票代码
            normalized_symbol = self._normalize_symbol(symbol)

            # 根据频率获取数据
            if frequency == "1d":
                data = self._fetch_daily_data(normalized_symbol, start_str, end_str)
            elif frequency in ["1m", "5m", "15m", "30m", "60m"]:
                data = self._fetch_intraday_data(normalized_symbol, start_str, end_str, frequency)
            else:
                raise ValueError(f"不支持的数据频率: {frequency}")

            if data.empty:
                logger.warning(f"未获取到数据: {symbol}")
                return pd.DataFrame()

            # 数据清洗和标准化
            data = self._clean_data(data)

            logger.debug(f"AkShare数据获取成功: {symbol}, {len(data)}条记录")

            return data

        except Exception as e:
            logger.error(f"AkShare数据获取失败: {symbol}, 错误: {e}")
            raise

    def _fetch_daily_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取日线数据"""
        try:
            # 尝试获取A股数据
            data = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust="qfq"  # 前复权
            )

            if data.empty:
                # 尝试获取指数数据
                data = ak.stock_zh_index_daily(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date
                )

            return data

        except Exception as e:
            logger.error(f"获取日线数据失败: {symbol}, 错误: {e}")
            return pd.DataFrame()

    def _fetch_intraday_data(
        self,
        symbol: str,
        start_date: str,
        end_date: str,
        frequency: str
    ) -> pd.DataFrame:
        """获取分钟数据"""
        try:
            # AkShare分钟数据接口
            period_map = {
                "1m": "1",
                "5m": "5",
                "15m": "15",
                "30m": "30",
                "60m": "60"
            }

            period = period_map.get(frequency, "5")

            data = ak.stock_zh_a_hist_min_em(
                symbol=symbol,
                period=period,
                start_date=start_date,
                end_date=end_date,
                adjust="qfq"
            )

            return data

        except Exception as e:
            logger.error(f"获取分钟数据失败: {symbol}, 错误: {e}")
            return pd.DataFrame()

    def _normalize_symbol(self, symbol: str) -> str:
        """标准化股票代码"""
        # 移除可能的前缀和后缀
        symbol = symbol.upper().strip()

        # 移除市场后缀
        if '.' in symbol:
            symbol = symbol.split('.')[0]

        # 确保6位数字格式
        if symbol.isdigit() and len(symbol) <= 6:
            symbol = symbol.zfill(6)

        return symbol

    def _format_date(self, date_obj: Union[str, date, datetime]) -> str:
        """格式化日期为AkShare需要的格式"""
        if isinstance(date_obj, str):
            # 假设已经是正确格式
            return date_obj.replace('-', '')
        elif isinstance(date_obj, (date, datetime)):
            return date_obj.strftime('%Y%m%d')
        else:
            raise ValueError(f"不支持的日期格式: {type(date_obj)}")

    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清洗和标准化数据"""
        if data.empty:
            return data

        # 标准化列名
        column_mapping = {
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'pct_change',
            '涨跌额': 'change',
            '换手率': 'turnover'
        }

        # 重命名列
        data = data.rename(columns=column_mapping)

        # 确保必要列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in data.columns:
                logger.warning(f"缺少必要列: {col}")
                return pd.DataFrame()

        # 设置日期索引
        if 'date' in data.columns:
            data['date'] = pd.to_datetime(data['date'])
            data.set_index('date', inplace=True)

        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')

        # 移除无效数据
        data = data.dropna(subset=required_columns)

        # 按日期排序
        data = data.sort_index()

        return data

    def get_available_symbols(self) -> List[str]:
        """获取可用的股票代码列表"""
        try:
            # 获取A股列表
            stock_list = ak.stock_info_a_code_name()

            if not stock_list.empty:
                symbols = stock_list['code'].tolist()
                logger.info(f"获取到{len(symbols)}个A股代码")
                return symbols
            else:
                logger.warning("未获取到股票列表")
                return []

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        try:
            normalized_symbol = self._normalize_symbol(symbol)

            # 获取股票基本信息
            info = ak.stock_individual_info_em(symbol=normalized_symbol)

            if info.empty:
                return {'symbol': symbol, 'error': '未找到股票信息'}

            # 转换为字典格式
            result = {'symbol': symbol}

            for _, row in info.iterrows():
                key = row.get('item', '')
                value = row.get('value', '')

                if key and value:
                    result[key] = value

            return result

        except Exception as e:
            logger.error(f"获取股票信息失败: {symbol}, 错误: {e}")
            return {'symbol': symbol, 'error': str(e)}

    def get_market_calendar(self, year: int = None) -> pd.DataFrame:
        """获取交易日历"""
        try:
            if year is None:
                year = datetime.now().year

            calendar = ak.tool_trade_date_hist_sina()

            if not calendar.empty:
                calendar['trade_date'] = pd.to_datetime(calendar['trade_date'])
                calendar = calendar[calendar['trade_date'].dt.year == year]

            return calendar

        except Exception as e:
            logger.error(f"获取交易日历失败: {e}")
            return pd.DataFrame()

    def get_index_list(self) -> List[str]:
        """获取指数列表"""
        try:
            # 获取主要指数
            indices = [
                "000001",  # 上证指数
                "000300",  # 沪深300
                "399001",  # 深证成指
                "399006",  # 创业板指
                "000016",  # 上证50
                "000905",  # 中证500
                "399905",  # 中证500
            ]

            return indices

        except Exception as e:
            logger.error(f"获取指数列表失败: {e}")
            return []

    def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 尝试获取一个简单的数据
            test_data = ak.stock_zh_a_hist(
                symbol="000001",
                period="daily",
                start_date="20231201",
                end_date="20231201"
            )

            return not test_data.empty

        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
