2025-05-27 21:12:03 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:12:03 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:12:05 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:12:05 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:12:05 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:12:05 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:12:05 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:12:05 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:12:05 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:12:05 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:12:05 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: akshare
2025-05-27 21:12:05 | INFO | dataseed.akshare_source:__init__:56 | AkShare数据源初始化完成
2025-05-27 21:12:05 | INFO | __main__:_setup_data_source:180 | 数据源已设置: akshare
2025-05-27 21:12:05 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:12:05 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:12:05 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Fast (None)
2025-05-27 21:12:05 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:12:05 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Slow (None)
2025-05-27 21:12:05 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:12:05 | INFO | __main__:_register_default_strategies:196 | 默认策略已注册
2025-05-27 21:12:05 | INFO | __main__:__init__:159 | Quant_01 快速开始初始化完成
2025-05-27 21:12:05 | INFO | __main__:run_simple_backtest:217 | 开始简单回测: macd on 000001
2025-05-27 21:12:05 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD
2025-05-27 21:12:05 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 21:12:05 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:12:05 | INFO | __main__:run_simple_backtest:242 | 简单回测完成
2025-05-27 21:12:05 | INFO | __main__:compare_strategies:271 | 开始策略对比: ['macd', 'macd_fast', 'macd_slow']
2025-05-27 21:12:05 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:12:05 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD_Fast
2025-05-27 21:12:05 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:12:05 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD_Fast, 耗时: 0.04秒
2025-05-27 21:12:05 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD_Slow
2025-05-27 21:12:05 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=7, 卖出信号=6
2025-05-27 21:12:05 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD_Slow, 耗时: 0.05秒
2025-05-27 21:12:05 | INFO | __main__:compare_strategies:298 | 策略对比完成
2025-05-27 21:12:05 | INFO | __main__:batch_backtest:328 | 开始批量回测: 3个标的
2025-05-27 21:12:05 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:12:05 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:12:05 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:12:05 | INFO | __main__:batch_backtest:356 | 批量回测完成
2025-05-27 21:16:04 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:16:04 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:16:05 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:16:05 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:16:05 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:16:05 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:16:05 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:16:05 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:16:05 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:16:05 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:16:05 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: akshare
2025-05-27 21:16:05 | INFO | dataseed.akshare_source:__init__:56 | AkShare数据源初始化完成
2025-05-27 21:16:05 | INFO | __main__:_setup_data_source:180 | 数据源已设置: akshare
2025-05-27 21:16:05 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:16:05 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:16:05 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Fast (None)
2025-05-27 21:16:05 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:16:05 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Slow (None)
2025-05-27 21:16:05 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:16:05 | INFO | __main__:_register_default_strategies:196 | 默认策略已注册
2025-05-27 21:16:05 | INFO | __main__:__init__:159 | Quant_01 快速开始初始化完成
2025-05-27 21:16:05 | INFO | __main__:run_simple_backtest:217 | 开始简单回测: macd on 000001
2025-05-27 21:16:06 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD
2025-05-27 21:16:06 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 21:16:06 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:16:06 | INFO | __main__:run_simple_backtest:242 | 简单回测完成
2025-05-27 21:16:06 | INFO | __main__:compare_strategies:271 | 开始策略对比: ['macd', 'macd_fast', 'macd_slow']
2025-05-27 21:16:07 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:16:07 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD_Fast
2025-05-27 21:16:07 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:16:07 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD_Fast, 耗时: 0.04秒
2025-05-27 21:16:07 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD_Slow
2025-05-27 21:16:07 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=7, 卖出信号=6
2025-05-27 21:16:07 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD_Slow, 耗时: 0.05秒
2025-05-27 21:16:07 | INFO | __main__:compare_strategies:298 | 策略对比完成
2025-05-27 21:16:07 | INFO | __main__:batch_backtest:328 | 开始批量回测: 3个标的
2025-05-27 21:16:07 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:16:07 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:16:07 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:16:07 | INFO | __main__:batch_backtest:356 | 批量回测完成
2025-05-27 21:17:16 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:17:16 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:17:17 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:17:17 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:17:17 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:17:17 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:17:17 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:17:17 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:17:17 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:17:17 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:17:17 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: akshare
2025-05-27 21:17:17 | INFO | dataseed.akshare_source:__init__:56 | AkShare数据源初始化完成
2025-05-27 21:17:17 | INFO | __main__:_setup_data_source:164 | 数据源已设置: akshare
2025-05-27 21:17:17 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:17:17 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:17:17 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Fast (None)
2025-05-27 21:17:17 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:17:17 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Slow (None)
2025-05-27 21:17:17 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:17:17 | INFO | __main__:_register_default_strategies:180 | 默认策略已注册
2025-05-27 21:17:17 | INFO | __main__:__init__:143 | Quant_01 快速开始初始化完成
2025-05-27 21:17:17 | INFO | __main__:run_simple_backtest:201 | 开始简单回测: macd on 000001
2025-05-27 21:17:17 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD
2025-05-27 21:17:17 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 21:17:17 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:17:17 | INFO | __main__:run_simple_backtest:226 | 简单回测完成
2025-05-27 21:17:17 | INFO | __main__:compare_strategies:255 | 开始策略对比: ['macd', 'macd_fast', 'macd_slow']
2025-05-27 21:17:17 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:17:17 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD_Fast
2025-05-27 21:17:17 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:17:17 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD_Fast, 耗时: 0.03秒
2025-05-27 21:17:17 | INFO | strategies.base.strategy:initialize:220 | 策略初始化完成: MACD_Slow
2025-05-27 21:17:17 | INFO | strategies.single.macd:generate_signals:168 | MACD信号生成完成: 买入信号=7, 卖出信号=6
2025-05-27 21:17:17 | INFO | strategies.base.strategy:run:255 | 策略运行完成: MACD_Slow, 耗时: 0.04秒
2025-05-27 21:17:17 | INFO | __main__:compare_strategies:282 | 策略对比完成
2025-05-27 21:17:17 | INFO | __main__:batch_backtest:312 | 开始批量回测: 3个标的
2025-05-27 21:17:17 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:17:17 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:17:18 | ERROR | strategies.base.strategy:run:260 | 策略运行失败: MACD, 错误: Can only compare identically-labeled Series objects
2025-05-27 21:17:18 | INFO | __main__:batch_backtest:340 | 批量回测完成
