"""
参数优化演示

展示如何使用不同的优化器进行策略参数优化。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from datetime import datetime

from quant_01 import (
    optimization_manager,
    GridSearchOptimizer,
    RandomSearchOptimizer,
    BayesianOptimizer,
    GeneticOptimizer,
    MultiObjectiveOptimizer
)
from quant_01.strategies.single.macd import MACDStrategy
from quant_01.dataseed.factory import DataSourceFactory
from quant_01.utils.logger import get_logger

logger = get_logger(__name__)


def load_sample_data():
    """加载示例数据"""
    logger.info("加载示例数据...")
    
    # 使用数据源工厂获取数据
    factory = DataSourceFactory()
    data_source = factory.create('akshare')
    
    # 获取股票数据
    data = data_source.get_stock_data(
        symbol='000001',
        start_date='2023-01-01',
        end_date='2024-12-31'
    )
    
    logger.info(f"数据加载完成: {len(data)} 条记录")
    return data


def demo_grid_search():
    """网格搜索演示"""
    logger.info("=" * 60)
    logger.info("网格搜索优化演示")
    logger.info("=" * 60)
    
    # 定义参数空间
    param_space = {
        'fast_period': [8, 12, 16],
        'slow_period': [21, 26, 35],
        'signal_period': [5, 9, 12]
    }
    
    # 加载数据
    data = load_sample_data()
    
    # 执行优化
    result = optimization_manager.optimize(
        strategy_class=MACDStrategy,
        param_space=param_space,
        data=data,
        method='grid',
        objective='sharpe_ratio',
        direction='maximize',
        validation_split=0.2
    )
    
    # 输出结果
    logger.info(f"最佳参数: {result.best_params}")
    logger.info(f"最佳夏普比率: {result.best_score:.4f}")
    logger.info(f"优化时间: {result.optimization_time:.2f}秒")
    logger.info(f"试验次数: {result.n_trials}")
    
    return result


def demo_random_search():
    """随机搜索演示"""
    logger.info("=" * 60)
    logger.info("随机搜索优化演示")
    logger.info("=" * 60)
    
    # 定义参数空间（支持连续分布）
    param_space = {
        'fast_period': {
            'type': 'uniform',
            'low': 5,
            'high': 20,
            'dtype': 'int'
        },
        'slow_period': {
            'type': 'uniform',
            'low': 20,
            'high': 50,
            'dtype': 'int'
        },
        'signal_period': {
            'type': 'uniform',
            'low': 3,
            'high': 15,
            'dtype': 'int'
        }
    }
    
    # 加载数据
    data = load_sample_data()
    
    # 执行优化
    result = optimization_manager.optimize(
        strategy_class=MACDStrategy,
        param_space=param_space,
        data=data,
        method='random',
        objective='sharpe_ratio',
        direction='maximize',
        validation_split=0.2,
        n_trials=50
    )
    
    # 输出结果
    logger.info(f"最佳参数: {result.best_params}")
    logger.info(f"最佳夏普比率: {result.best_score:.4f}")
    logger.info(f"优化时间: {result.optimization_time:.2f}秒")
    logger.info(f"试验次数: {result.n_trials}")
    
    return result


def demo_bayesian_optimization():
    """贝叶斯优化演示"""
    logger.info("=" * 60)
    logger.info("贝叶斯优化演示")
    logger.info("=" * 60)
    
    # 定义参数空间
    param_space = {
        'fast_period': {
            'type': 'uniform',
            'low': 5,
            'high': 20,
            'dtype': 'int'
        },
        'slow_period': {
            'type': 'uniform',
            'low': 20,
            'high': 50,
            'dtype': 'int'
        },
        'signal_period': {
            'type': 'uniform',
            'low': 3,
            'high': 15,
            'dtype': 'int'
        }
    }
    
    # 加载数据
    data = load_sample_data()
    
    # 执行优化
    result = optimization_manager.optimize(
        strategy_class=MACDStrategy,
        param_space=param_space,
        data=data,
        method='bayesian',
        objective='sharpe_ratio',
        direction='maximize',
        validation_split=0.2,
        n_trials=30,
        n_initial_points=10
    )
    
    # 输出结果
    logger.info(f"最佳参数: {result.best_params}")
    logger.info(f"最佳夏普比率: {result.best_score:.4f}")
    logger.info(f"优化时间: {result.optimization_time:.2f}秒")
    logger.info(f"试验次数: {result.n_trials}")
    
    return result


def demo_genetic_algorithm():
    """遗传算法演示"""
    logger.info("=" * 60)
    logger.info("遗传算法优化演示")
    logger.info("=" * 60)
    
    # 定义参数空间
    param_space = {
        'fast_period': {
            'type': 'uniform',
            'low': 5,
            'high': 20,
            'dtype': 'int'
        },
        'slow_period': {
            'type': 'uniform',
            'low': 20,
            'high': 50,
            'dtype': 'int'
        },
        'signal_period': {
            'type': 'uniform',
            'low': 3,
            'high': 15,
            'dtype': 'int'
        }
    }
    
    # 加载数据
    data = load_sample_data()
    
    # 执行优化
    result = optimization_manager.optimize(
        strategy_class=MACDStrategy,
        param_space=param_space,
        data=data,
        method='genetic',
        objective='sharpe_ratio',
        direction='maximize',
        validation_split=0.2,
        n_generations=20,
        population_size=30,
        mutation_rate=0.1,
        crossover_rate=0.8
    )
    
    # 输出结果
    logger.info(f"最佳参数: {result.best_params}")
    logger.info(f"最佳夏普比率: {result.best_score:.4f}")
    logger.info(f"优化时间: {result.optimization_time:.2f}秒")
    logger.info(f"试验次数: {result.n_trials}")
    
    return result


def demo_multi_objective():
    """多目标优化演示"""
    logger.info("=" * 60)
    logger.info("多目标优化演示")
    logger.info("=" * 60)
    
    # 定义参数空间
    param_space = {
        'fast_period': [8, 12, 16],
        'slow_period': [21, 26, 35],
        'signal_period': [5, 9, 12]
    }
    
    # 加载数据
    data = load_sample_data()
    
    # 执行多目标优化
    result = optimization_manager.optimize(
        strategy_class=MACDStrategy,
        param_space=param_space,
        data=data,
        method='multi_objective',
        objectives=['sharpe_ratio', 'total_return', 'max_drawdown'],
        directions={
            'sharpe_ratio': 'maximize',
            'total_return': 'maximize', 
            'max_drawdown': 'minimize'
        },
        validation_split=0.2,
        n_generations=15,
        population_size=50
    )
    
    # 输出结果
    logger.info(f"最佳参数: {result.best_params}")
    logger.info(f"最佳夏普比率: {result.best_score:.4f}")
    logger.info(f"优化时间: {result.optimization_time:.2f}秒")
    
    if hasattr(result, 'pareto_front'):
        logger.info(f"帕累托前沿大小: {len(result.pareto_front)}")
    
    return result


def demo_method_comparison():
    """优化方法比较演示"""
    logger.info("=" * 60)
    logger.info("优化方法比较演示")
    logger.info("=" * 60)
    
    # 定义参数空间
    param_space = {
        'fast_period': [8, 12, 16],
        'slow_period': [21, 26, 35],
        'signal_period': [5, 9, 12]
    }
    
    # 加载数据
    data = load_sample_data()
    
    # 比较不同方法
    results = optimization_manager.compare_methods(
        strategy_class=MACDStrategy,
        param_space=param_space,
        data=data,
        methods=['grid', 'random', 'bayesian'],
        objective='sharpe_ratio',
        direction='maximize',
        n_trials=30
    )
    
    return results


def main():
    """主函数"""
    logger.info("参数优化演示开始")
    
    try:
        # 1. 网格搜索演示
        demo_grid_search()
        
        # 2. 随机搜索演示
        demo_random_search()
        
        # 3. 贝叶斯优化演示
        demo_bayesian_optimization()
        
        # 4. 遗传算法演示
        demo_genetic_algorithm()
        
        # 5. 多目标优化演示
        demo_multi_objective()
        
        # 6. 方法比较演示
        demo_method_comparison()
        
        # 显示优化历史
        history = optimization_manager.get_optimization_history()
        logger.info(f"总共执行了 {len(history)} 次优化")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        raise
    
    logger.info("参数优化演示完成")


if __name__ == "__main__":
    main()
