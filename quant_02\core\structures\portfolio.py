"""
投资组合数据结构模块

定义量化交易系统中使用的投资组合相关数据结构。
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np

from .trade import Order, Position, Trade, PositionSide


@dataclass
class Portfolio:
    """投资组合数据结构
    
    管理多个标的的持仓和交易记录。
    """
    
    portfolio_id: str
    name: str
    initial_capital: float
    timestamp: datetime
    
    # 持仓信息
    positions: Dict[str, Position] = field(default_factory=dict)
    cash: float = 0.0
    
    # 交易记录
    trades: List[Trade] = field(default_factory=list)
    orders: List[Order] = field(default_factory=list)
    
    # 历史数据
    value_history: pd.Series = field(default_factory=pd.Series)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if self.cash == 0.0:
            self.cash = self.initial_capital
    
    @property
    def total_value(self) -> float:
        """总价值（需要当前价格）"""
        position_value = sum(pos.market_value for pos in self.positions.values())
        return self.cash + position_value
    
    @property
    def positions_value(self) -> float:
        """持仓总价值"""
        return sum(pos.market_value for pos in self.positions.values())
    
    @property
    def total_unrealized_pnl(self) -> float:
        """总未实现盈亏"""
        return sum(pos.unrealized_pnl for pos in self.positions.values())
    
    @property
    def total_realized_pnl(self) -> float:
        """总已实现盈亏"""
        return sum(pos.realized_pnl for pos in self.positions.values())
    
    @property
    def total_pnl(self) -> float:
        """总盈亏"""
        return self.total_unrealized_pnl + self.total_realized_pnl
    
    @property
    def total_return(self) -> float:
        """总收益率"""
        if self.initial_capital == 0:
            return 0.0
        return (self.total_value - self.initial_capital) / self.initial_capital
    
    @property
    def leverage(self) -> float:
        """杠杆率"""
        if self.total_value == 0:
            return 0.0
        return self.positions_value / self.total_value
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取持仓"""
        return self.positions.get(symbol)
    
    def add_position(self, position: Position):
        """添加持仓"""
        self.positions[position.symbol] = position
    
    def remove_position(self, symbol: str):
        """移除持仓"""
        if symbol in self.positions:
            del self.positions[symbol]
    
    def add_trade(self, trade: Trade):
        """添加交易记录"""
        self.trades.append(trade)
        
        # 更新持仓
        self._update_position_from_trade(trade)
        
        # 更新现金
        if trade.is_buy:
            self.cash -= trade.total_cost
        else:
            self.cash += trade.total_value - trade.commission - trade.slippage
    
    def add_order(self, order: Order):
        """添加订单记录"""
        self.orders.append(order)
    
    def _update_position_from_trade(self, trade: Trade):
        """根据交易更新持仓"""
        symbol = trade.symbol
        
        if symbol not in self.positions:
            # 新建持仓
            side = PositionSide.LONG if trade.is_buy else PositionSide.SHORT
            quantity = trade.quantity if trade.is_buy else -trade.quantity
            
            self.positions[symbol] = Position(
                symbol=symbol,
                side=side,
                quantity=quantity,
                avg_price=trade.price,
                timestamp=trade.timestamp,
                total_cost=trade.total_cost,
                commission=trade.commission,
                strategy_id=trade.strategy_id
            )
        else:
            # 更新现有持仓
            position = self.positions[symbol]
            trade_quantity = trade.quantity if trade.is_buy else -trade.quantity
            position.add_trade(trade_quantity, trade.price, trade.commission)
            
            # 如果持仓为0，移除持仓
            if position.is_flat:
                del self.positions[symbol]
    
    def update_value_history(self, timestamp: datetime, value: float):
        """更新价值历史"""
        self.value_history.loc[timestamp] = value
    
    def get_symbols(self) -> List[str]:
        """获取所有持仓标的"""
        return list(self.positions.keys())
    
    def get_long_positions(self) -> Dict[str, Position]:
        """获取多头持仓"""
        return {symbol: pos for symbol, pos in self.positions.items() if pos.is_long}
    
    def get_short_positions(self) -> Dict[str, Position]:
        """获取空头持仓"""
        return {symbol: pos for symbol, pos in self.positions.items() if pos.is_short}
    
    def get_position_count(self) -> int:
        """获取持仓数量"""
        return len([pos for pos in self.positions.values() if not pos.is_flat])
    
    def get_trades_by_symbol(self, symbol: str) -> List[Trade]:
        """获取指定标的的交易记录"""
        return [trade for trade in self.trades if trade.symbol == symbol]
    
    def get_trades_by_strategy(self, strategy_id: str) -> List[Trade]:
        """获取指定策略的交易记录"""
        return [trade for trade in self.trades if trade.strategy_id == strategy_id]
    
    def calculate_metrics(self, current_prices: Dict[str, float] = None) -> Dict[str, float]:
        """计算投资组合指标"""
        if current_prices:
            # 更新未实现盈亏
            for symbol, position in self.positions.items():
                if symbol in current_prices:
                    position.update_unrealized_pnl(current_prices[symbol])
        
        # 计算基本指标
        metrics = {
            'total_value': self.total_value,
            'cash': self.cash,
            'positions_value': self.positions_value,
            'total_pnl': self.total_pnl,
            'total_return': self.total_return,
            'leverage': self.leverage,
            'position_count': self.get_position_count(),
        }
        
        # 计算风险指标
        if len(self.value_history) > 1:
            returns = self.value_history.pct_change().dropna()
            
            if len(returns) > 0:
                metrics.update({
                    'volatility': returns.std() * np.sqrt(252),  # 年化波动率
                    'sharpe_ratio': self._calculate_sharpe_ratio(returns),
                    'max_drawdown': self._calculate_max_drawdown(),
                    'win_rate': self._calculate_win_rate(),
                })
        
        return metrics
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.03) -> float:
        """计算夏普比率"""
        if returns.std() == 0:
            return 0.0
        
        excess_returns = returns.mean() * 252 - risk_free_rate
        return excess_returns / (returns.std() * np.sqrt(252))
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        if len(self.value_history) < 2:
            return 0.0
        
        cumulative = self.value_history.cummax()
        drawdown = (self.value_history - cumulative) / cumulative
        return abs(drawdown.min())
    
    def _calculate_win_rate(self) -> float:
        """计算胜率"""
        if not self.trades:
            return 0.0
        
        # 简化计算：基于交易的盈亏
        profitable_trades = 0
        total_trades = 0
        
        for i in range(1, len(self.trades)):
            prev_trade = self.trades[i-1]
            curr_trade = self.trades[i]
            
            # 如果是一买一卖的配对
            if (prev_trade.is_buy and curr_trade.is_sell and 
                prev_trade.symbol == curr_trade.symbol):
                total_trades += 1
                pnl = (curr_trade.price - prev_trade.price) * curr_trade.quantity
                if pnl > 0:
                    profitable_trades += 1
        
        return profitable_trades / total_trades if total_trades > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'portfolio_id': self.portfolio_id,
            'name': self.name,
            'initial_capital': self.initial_capital,
            'timestamp': self.timestamp,
            'cash': self.cash,
            'total_value': self.total_value,
            'positions_value': self.positions_value,
            'total_pnl': self.total_pnl,
            'total_return': self.total_return,
            'leverage': self.leverage,
            'position_count': self.get_position_count(),
            'symbols': self.get_symbols(),
        }


@dataclass
class StrategyResult:
    """策略回测结果数据结构"""
    strategy_id: str
    symbol: str
    start_date: datetime
    end_date: datetime
    
    # 基本指标
    total_return: float = 0.0
    annual_return: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    
    # 交易指标
    total_trades: int = 0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_trade_return: float = 0.0
    
    # 详细数据
    portfolio: Optional[Portfolio] = None
    equity_curve: Optional[pd.Series] = None
    trades: List[Trade] = field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'strategy_id': self.strategy_id,
            'symbol': self.symbol,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'total_return': self.total_return,
            'annual_return': self.annual_return,
            'volatility': self.volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'total_trades': self.total_trades,
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'avg_trade_return': self.avg_trade_return,
        }
