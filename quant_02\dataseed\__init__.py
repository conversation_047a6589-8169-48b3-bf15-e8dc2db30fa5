"""
数据源模块

提供统一的数据访问接口，支持多种数据源：
- Mock: 模拟数据（用于测试）
- AkShare: A股数据
- 数据库: 本地数据存储
- 文件: CSV/Excel等文件数据源
"""

from .base import DataSource
from .mock import MockDataSource
from .factory import (
    DataSourceFactory, data_source_factory,
    create_data_source, list_data_sources, register_data_source
)

# 尝试导入可选的数据源
try:
    from .akshare import AkShareDataSource
    _AKSHARE_AVAILABLE = True
except ImportError:
    AkShareDataSource = None
    _AKSHARE_AVAILABLE = False

try:
    from .database import DatabaseDataSource
    _DATABASE_AVAILABLE = True
except ImportError:
    DatabaseDataSource = None
    _DATABASE_AVAILABLE = False

__all__ = [
    # 基础类
    "DataSource",
    
    # 具体实现
    "MockDataSource",
    
    # 工厂相关
    "DataSourceFactory",
    "data_source_factory",
    "create_data_source",
    "list_data_sources", 
    "register_data_source",
]

# 动态添加可用的数据源到__all__
if _AKSHARE_AVAILABLE:
    __all__.append("AkShareDataSource")

if _DATABASE_AVAILABLE:
    __all__.append("DatabaseDataSource")


def get_available_sources():
    """获取可用的数据源列表"""
    sources = ["mock"]
    
    if _AKSHARE_AVAILABLE:
        sources.append("akshare")
    
    if _DATABASE_AVAILABLE:
        sources.append("database")
    
    return sources


def check_dependencies():
    """检查依赖项"""
    status = {
        "mock": True,  # 总是可用
        "akshare": _AKSHARE_AVAILABLE,
        "database": _DATABASE_AVAILABLE,
    }
    
    return status
