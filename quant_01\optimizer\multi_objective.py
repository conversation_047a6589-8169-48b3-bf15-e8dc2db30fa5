"""
多目标优化器

支持同时优化多个目标函数，使用帕累托前沿分析。
"""

from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import pandas as pd
import numpy as np

from .base import BaseOptimizer, OptimizationResult
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ParetoSolution:
    """帕累托解"""
    
    def __init__(self, params: Dict[str, Any], objectives: Dict[str, float]):
        self.params = params
        self.objectives = objectives
        self.dominates_count = 0
        self.dominated_by = []
        self.rank = 0
        self.crowding_distance = 0.0
    
    def dominates(self, other: 'ParetoSolution', directions: Dict[str, str]) -> bool:
        """判断是否支配另一个解"""
        better_in_any = False
        
        for obj_name, obj_value in self.objectives.items():
            other_value = other.objectives[obj_name]
            direction = directions[obj_name]
            
            if direction == 'maximize':
                if obj_value < other_value:
                    return False
                elif obj_value > other_value:
                    better_in_any = True
            else:  # minimize
                if obj_value > other_value:
                    return False
                elif obj_value < other_value:
                    better_in_any = True
        
        return better_in_any
    
    def __repr__(self):
        return f"ParetoSolution(objectives={self.objectives}, rank={self.rank})"


class MultiObjectiveOptimizer(BaseOptimizer):
    """多目标优化器"""
    
    def __init__(
        self,
        strategy_class: type,
        param_space: Dict[str, Union[List[Any], Dict[str, Any]]],
        objectives: List[str],
        directions: Dict[str, str],
        n_jobs: int = 1,
        random_state: Optional[int] = None,
        population_size: int = 100
    ):
        """
        初始化多目标优化器
        
        Args:
            strategy_class: 策略类
            param_space: 参数空间
            objectives: 目标函数列表
            directions: 每个目标的优化方向
            n_jobs: 并行任务数
            random_state: 随机种子
            population_size: 种群大小
        """
        # 使用第一个目标作为主要目标
        super().__init__(strategy_class, param_space, objectives[0], directions[objectives[0]], n_jobs, random_state)
        
        self.objectives = objectives
        self.directions = directions
        self.population_size = population_size
        
        # 验证目标和方向
        for obj in objectives:
            if obj not in directions:
                raise ValueError(f"目标 {obj} 缺少优化方向")
        
        logger.info(f"多目标优化器初始化完成: 目标={objectives}, 种群大小={population_size}")
    
    def optimize(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame] = None,
        n_generations: int = 50,
        **kwargs
    ) -> OptimizationResult:
        """
        执行多目标优化
        
        Args:
            data: 训练数据
            validation_data: 验证数据
            n_generations: 进化代数
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        start_time = datetime.now()
        logger.info(f"开始多目标优化: {n_generations} 代, 种群大小 {self.population_size}")
        
        # 初始化种群
        population = self._initialize_population(data, validation_data)
        
        all_results = []
        pareto_fronts_history = []
        
        for generation in range(n_generations):
            # 非支配排序
            fronts = self._non_dominated_sort(population)
            
            # 记录帕累托前沿
            pareto_fronts_history.append([sol.objectives for sol in fronts[0]])
            
            # 记录所有解
            for solution in population:
                result = {
                    'params': solution.params.copy(),
                    'objectives': solution.objectives.copy(),
                    'rank': solution.rank,
                    'crowding_distance': solution.crowding_distance,
                    'generation': generation
                }
                # 使用主要目标作为分数
                result['score'] = solution.objectives[self.objective]
                all_results.append(result)
            
            # 生成新种群
            if generation < n_generations - 1:
                population = self._generate_new_population(population, data, validation_data)
            
            # 进度报告
            if (generation + 1) % 10 == 0:
                logger.info(f"多目标优化进度: 第 {generation + 1}/{n_generations} 代, 帕累托前沿大小: {len(fronts[0])}")
        
        # 选择最佳解（帕累托前沿中主要目标最优的解）
        pareto_front = fronts[0]
        best_solution = self._select_best_from_pareto(pareto_front)
        
        # 构建优化结果
        optimization_result = OptimizationResult(
            best_params=best_solution.params,
            best_score=best_solution.objectives[self.objective],
            objective=self.objective,
            direction=self.direction,
            n_trials=len(all_results),
            optimization_time=0,  # 将在外部设置
            all_results=all_results
        )
        
        # 添加多目标特有信息
        optimization_result.pareto_front = [sol.objectives for sol in pareto_front]
        optimization_result.pareto_solutions = [sol.params for sol in pareto_front]
        
        # 计算优化时间
        optimization_time = (datetime.now() - start_time).total_seconds()
        optimization_result.optimization_time = optimization_time
        
        logger.info(f"多目标优化完成: 帕累托前沿大小 {len(pareto_front)}, 耗时: {optimization_time:.2f}秒")
        
        return optimization_result
    
    def _initialize_population(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> List[ParetoSolution]:
        """初始化种群"""
        population = []
        
        for _ in range(self.population_size):
            params = self._generate_random_params()
            objectives = self._evaluate_multi_objectives(params, data, validation_data)
            solution = ParetoSolution(params, objectives)
            population.append(solution)
        
        return population
    
    def _generate_random_params(self) -> Dict[str, Any]:
        """生成随机参数"""
        import random
        
        params = {}
        for param_name, param_config in self.param_space.items():
            if isinstance(param_config, list):
                params[param_name] = random.choice(param_config)
            elif isinstance(param_config, dict):
                # 简化处理，只支持uniform分布
                if param_config.get('type') == 'uniform':
                    low = param_config['low']
                    high = param_config['high']
                    if param_config.get('dtype') == 'int':
                        params[param_name] = random.randint(int(low), int(high))
                    else:
                        params[param_name] = random.uniform(low, high)
                else:
                    params[param_name] = random.choice(param_config.get('choices', [0, 1]))
        
        return params
    
    def _evaluate_multi_objectives(
        self,
        params: Dict[str, Any],
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> Dict[str, float]:
        """评估多个目标"""
        try:
            # 创建策略并运行
            config_class = getattr(self.strategy_class, '__init__').__annotations__.get('config', None)
            
            if config_class:
                config = config_class(**params)
                strategy = self.strategy_class(config)
            else:
                strategy = self.strategy_class(params)
            
            # 使用验证数据或训练数据
            eval_data = validation_data if validation_data is not None else data
            result = strategy.run(eval_data)
            
            # 提取所有目标值
            objectives = {}
            for obj_name in self.objectives:
                objectives[obj_name] = getattr(result, obj_name, 0.0)
            
            return objectives
            
        except Exception as e:
            logger.warning(f"多目标评估失败: {e}")
            # 返回最差值
            objectives = {}
            for obj_name in self.objectives:
                direction = self.directions[obj_name]
                objectives[obj_name] = float('-inf') if direction == 'maximize' else float('inf')
            return objectives
    
    def _non_dominated_sort(self, population: List[ParetoSolution]) -> List[List[ParetoSolution]]:
        """非支配排序"""
        # 重置排序信息
        for solution in population:
            solution.dominates_count = 0
            solution.dominated_by = []
        
        # 计算支配关系
        for i, sol1 in enumerate(population):
            for j, sol2 in enumerate(population):
                if i != j:
                    if sol1.dominates(sol2, self.directions):
                        sol1.dominates_count += 1
                    elif sol2.dominates(sol1, self.directions):
                        sol1.dominated_by.append(sol2)
        
        # 分层
        fronts = []
        current_front = []
        
        # 第一层：非支配解
        for solution in population:
            if len(solution.dominated_by) == 0:
                solution.rank = 0
                current_front.append(solution)
        
        fronts.append(current_front)
        
        # 后续层
        rank = 0
        while len(fronts[rank]) > 0:
            next_front = []
            for solution in fronts[rank]:
                for dominated_sol in population:
                    if solution in dominated_sol.dominated_by:
                        dominated_sol.dominated_by.remove(solution)
                        if len(dominated_sol.dominated_by) == 0:
                            dominated_sol.rank = rank + 1
                            next_front.append(dominated_sol)
            
            if next_front:
                fronts.append(next_front)
            rank += 1
        
        # 计算拥挤距离
        for front in fronts:
            self._calculate_crowding_distance(front)
        
        return fronts
    
    def _calculate_crowding_distance(self, front: List[ParetoSolution]):
        """计算拥挤距离"""
        if len(front) <= 2:
            for solution in front:
                solution.crowding_distance = float('inf')
            return
        
        # 初始化
        for solution in front:
            solution.crowding_distance = 0.0
        
        # 对每个目标计算拥挤距离
        for obj_name in self.objectives:
            # 按目标值排序
            front.sort(key=lambda x: x.objectives[obj_name])
            
            # 边界解设为无穷大
            front[0].crowding_distance = float('inf')
            front[-1].crowding_distance = float('inf')
            
            # 计算目标值范围
            obj_range = front[-1].objectives[obj_name] - front[0].objectives[obj_name]
            
            if obj_range > 0:
                # 计算中间解的拥挤距离
                for i in range(1, len(front) - 1):
                    distance = (front[i + 1].objectives[obj_name] - front[i - 1].objectives[obj_name]) / obj_range
                    front[i].crowding_distance += distance
    
    def _generate_new_population(
        self,
        population: List[ParetoSolution],
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> List[ParetoSolution]:
        """生成新种群（简化实现）"""
        # 选择前一半作为父代
        population.sort(key=lambda x: (x.rank, -x.crowding_distance))
        parents = population[:self.population_size // 2]
        
        # 生成子代（简单变异）
        import random
        new_population = parents.copy()
        
        while len(new_population) < self.population_size:
            parent = random.choice(parents)
            child_params = self._mutate_params(parent.params)
            child_objectives = self._evaluate_multi_objectives(child_params, data, validation_data)
            child = ParetoSolution(child_params, child_objectives)
            new_population.append(child)
        
        return new_population
    
    def _mutate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """参数变异"""
        import random
        
        mutated_params = params.copy()
        
        # 随机选择一个参数进行变异
        param_name = random.choice(list(self.param_space.keys()))
        param_config = self.param_space[param_name]
        
        if isinstance(param_config, list):
            mutated_params[param_name] = random.choice(param_config)
        elif isinstance(param_config, dict) and param_config.get('type') == 'uniform':
            low = param_config['low']
            high = param_config['high']
            if param_config.get('dtype') == 'int':
                mutated_params[param_name] = random.randint(int(low), int(high))
            else:
                mutated_params[param_name] = random.uniform(low, high)
        
        return mutated_params
    
    def _select_best_from_pareto(self, pareto_front: List[ParetoSolution]) -> ParetoSolution:
        """从帕累托前沿选择最佳解"""
        # 选择主要目标最优的解
        direction = self.directions[self.objective]
        
        if direction == 'maximize':
            return max(pareto_front, key=lambda x: x.objectives[self.objective])
        else:
            return min(pareto_front, key=lambda x: x.objectives[self.objective])
    
    def get_optimization_summary(self, result: OptimizationResult) -> Dict[str, Any]:
        """获取优化摘要"""
        summary = {
            'method': 'Multi-Objective Optimization',
            'objectives': self.objectives,
            'directions': self.directions,
            'population_size': self.population_size,
            'n_trials': result.n_trials,
            'best_params': result.best_params,
            'best_objectives': {obj: result.best_score if obj == self.objective else 0 for obj in self.objectives},
            'optimization_time': result.optimization_time
        }
        
        if hasattr(result, 'pareto_front'):
            summary['pareto_front_size'] = len(result.pareto_front)
            summary['pareto_front'] = result.pareto_front
        
        return summary
