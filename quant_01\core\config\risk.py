"""
风险管理配置

定义风险管理相关的配置参数。
"""

from typing import Optional, List
from pydantic import Field, validator
from .base import BaseConfig


class RiskConfig(BaseConfig):
    """风险管理配置"""
    
    config_name: str = Field(default="risk", description="配置名称")
    
    # 基本风险参数
    max_position_size: float = Field(default=0.1, description="单个标的最大仓位", ge=0, le=1)
    max_total_position: float = Field(default=0.95, description="总仓位上限", ge=0, le=1)
    
    # 止损止盈
    enable_stop_loss: bool = Field(default=False, description="是否启用止损")
    stop_loss_rate: float = Field(default=0.05, description="止损比例", ge=0, le=1)
    
    enable_take_profit: bool = Field(default=False, description="是否启用止盈")
    take_profit_rate: float = Field(default=0.10, description="止盈比例", ge=0, le=1)
    
    # 回撤控制
    max_drawdown: float = Field(default=0.20, description="最大回撤限制", ge=0, le=1)
    
    # 集中度控制
    max_sector_exposure: float = Field(default=0.30, description="单个行业最大敞口", ge=0, le=1)
    
    @validator('stop_loss_rate')
    def validate_stop_loss(cls, v, values):
        if values.get('enable_stop_loss', False) and v <= 0:
            raise ValueError('启用止损时，止损比例必须大于0')
        return v
