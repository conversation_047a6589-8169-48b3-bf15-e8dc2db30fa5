"""
配置基类

定义配置系统的基础功能和接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union
from pathlib import Path
import json
import yaml
from pydantic import BaseModel, Field, validator
from datetime import datetime
import os


class BaseConfig(BaseModel, ABC):
    """配置基类
    
    提供配置的基础功能：
    - 数据验证
    - 序列化/反序列化
    - 配置合并
    - 环境变量支持
    """
    
    # 配置元信息
    config_name: str = Field(default="", description="配置名称")
    config_version: str = Field(default="1.0.0", description="配置版本")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    class Config:
        """Pydantic配置"""
        # 允许额外字段
        extra = "allow"
        # 使用枚举值
        use_enum_values = True
        # 验证赋值
        validate_assignment = True
        # JSON编码器
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def merge_config(self, other: 'BaseConfig') -> 'BaseConfig':
        """合并配置
        
        Args:
            other: 另一个配置对象
            
        Returns:
            合并后的新配置对象
        """
        # 获取当前配置的字典
        current_dict = self.dict()
        # 获取其他配置的字典
        other_dict = other.dict()
        
        # 递归合并字典
        merged_dict = self._deep_merge_dict(current_dict, other_dict)
        
        # 创建新的配置对象
        return self.__class__(**merged_dict)
    
    def _deep_merge_dict(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge_dict(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.dict()
    
    def to_json(self, indent: int = 2) -> str:
        """转换为JSON字符串"""
        return self.json(indent=indent, ensure_ascii=False)
    
    def to_yaml(self) -> str:
        """转换为YAML字符串"""
        return yaml.dump(self.dict(), default_flow_style=False, allow_unicode=True)
    
    def save_to_file(self, file_path: Union[str, Path], format: str = "auto"):
        """保存到文件
        
        Args:
            file_path: 文件路径
            format: 文件格式 (json, yaml, auto)
        """
        file_path = Path(file_path)
        
        # 自动检测格式
        if format == "auto":
            format = file_path.suffix.lower().lstrip('.')
            if format not in ['json', 'yaml', 'yml']:
                format = 'json'
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        if format == 'json':
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.to_json())
        elif format in ['yaml', 'yml']:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.to_yaml())
        else:
            raise ValueError(f"不支持的文件格式: {format}")
    
    @classmethod
    def load_from_file(cls, file_path: Union[str, Path], format: str = "auto") -> 'BaseConfig':
        """从文件加载配置
        
        Args:
            file_path: 文件路径
            format: 文件格式 (json, yaml, auto)
            
        Returns:
            配置对象
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        # 自动检测格式
        if format == "auto":
            format = file_path.suffix.lower().lstrip('.')
            if format not in ['json', 'yaml', 'yml']:
                format = 'json'
        
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析内容
        if format == 'json':
            data = json.loads(content)
        elif format in ['yaml', 'yml']:
            data = yaml.safe_load(content)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        
        return cls(**data)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseConfig':
        """从字典创建配置"""
        return cls(**data)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'BaseConfig':
        """从JSON字符串创建配置"""
        data = json.loads(json_str)
        return cls(**data)
    
    @classmethod
    def from_yaml(cls, yaml_str: str) -> 'BaseConfig':
        """从YAML字符串创建配置"""
        data = yaml.safe_load(yaml_str)
        return cls(**data)
    
    @classmethod
    def from_env(cls, prefix: str = "") -> 'BaseConfig':
        """从环境变量创建配置
        
        Args:
            prefix: 环境变量前缀
            
        Returns:
            配置对象
        """
        env_data = {}
        
        # 获取所有环境变量
        for key, value in os.environ.items():
            if prefix and not key.startswith(prefix):
                continue
            
            # 移除前缀
            config_key = key[len(prefix):].lower() if prefix else key.lower()
            
            # 尝试转换类型
            try:
                # 尝试解析为JSON
                env_data[config_key] = json.loads(value)
            except (json.JSONDecodeError, ValueError):
                # 如果不是JSON，保持字符串
                env_data[config_key] = value
        
        return cls(**env_data)
    
    def get_env_var(self, key: str, default: Any = None, prefix: str = "") -> Any:
        """获取环境变量值
        
        Args:
            key: 配置键
            default: 默认值
            prefix: 环境变量前缀
            
        Returns:
            环境变量值
        """
        env_key = f"{prefix}{key.upper()}" if prefix else key.upper()
        value = os.getenv(env_key, default)
        
        # 尝试转换类型
        if isinstance(value, str):
            try:
                return json.loads(value)
            except (json.JSONDecodeError, ValueError):
                return value
        
        return value
    
    def validate_config(self) -> bool:
        """验证配置
        
        Returns:
            是否验证通过
        """
        try:
            # Pydantic会自动验证
            self.__class__(**self.dict())
            return True
        except Exception:
            return False
    
    def get_config_diff(self, other: 'BaseConfig') -> Dict[str, Any]:
        """获取配置差异
        
        Args:
            other: 另一个配置对象
            
        Returns:
            配置差异字典
        """
        current_dict = self.dict()
        other_dict = other.dict()
        
        diff = {}
        
        # 检查当前配置中的键
        for key, value in current_dict.items():
            if key not in other_dict:
                diff[key] = {"current": value, "other": None, "status": "removed"}
            elif value != other_dict[key]:
                diff[key] = {"current": value, "other": other_dict[key], "status": "changed"}
        
        # 检查其他配置中的新键
        for key, value in other_dict.items():
            if key not in current_dict:
                diff[key] = {"current": None, "other": value, "status": "added"}
        
        return diff
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}({self.config_name})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"{self.__class__.__name__}(name='{self.config_name}', version='{self.config_version}')"


class ConfigManager:
    """配置管理器
    
    管理多个配置对象，支持配置的加载、保存、合并等操作。
    """
    
    def __init__(self):
        self._configs: Dict[str, BaseConfig] = {}
        self._config_files: Dict[str, Path] = {}
    
    def register_config(self, name: str, config: BaseConfig, file_path: Optional[Path] = None):
        """注册配置
        
        Args:
            name: 配置名称
            config: 配置对象
            file_path: 配置文件路径
        """
        self._configs[name] = config
        if file_path:
            self._config_files[name] = Path(file_path)
    
    def get_config(self, name: str) -> Optional[BaseConfig]:
        """获取配置"""
        return self._configs.get(name)
    
    def update_config(self, name: str, config: BaseConfig):
        """更新配置"""
        if name in self._configs:
            self._configs[name] = config
            config.update_timestamp()
    
    def save_config(self, name: str, file_path: Optional[Path] = None):
        """保存配置到文件"""
        if name not in self._configs:
            raise ValueError(f"配置不存在: {name}")
        
        config = self._configs[name]
        target_path = file_path or self._config_files.get(name)
        
        if not target_path:
            raise ValueError(f"未指定配置文件路径: {name}")
        
        config.save_to_file(target_path)
    
    def load_config(self, name: str, file_path: Path, config_class: type):
        """从文件加载配置"""
        config = config_class.load_from_file(file_path)
        self.register_config(name, config, file_path)
        return config
    
    def reload_config(self, name: str):
        """重新加载配置"""
        if name not in self._config_files:
            raise ValueError(f"配置文件路径未知: {name}")
        
        file_path = self._config_files[name]
        config_class = type(self._configs[name])
        
        return self.load_config(name, file_path, config_class)
    
    def get_all_configs(self) -> Dict[str, BaseConfig]:
        """获取所有配置"""
        return self._configs.copy()
    
    def clear_configs(self):
        """清空所有配置"""
        self._configs.clear()
        self._config_files.clear()


# 全局配置管理器实例
config_manager = ConfigManager()
