"""
配置基类模块

提供配置管理的基础功能。
"""

from abc import ABC
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Union, Optional
import json
import yaml
from pydantic import BaseModel, Field, validator


class BaseConfig(BaseModel, ABC):
    """配置基类
    
    提供配置的基础功能：
    - 数据验证
    - 序列化/反序列化
    - 配置合并
    - 环境变量支持
    """
    
    # 配置元信息
    config_name: str = Field(default="", description="配置名称")
    config_version: str = Field(default="1.0.0", description="配置版本")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    class Config:
        """Pydantic配置"""
        # 允许额外字段
        extra = "allow"
        # 使用枚举值
        use_enum_values = True
        # 验证赋值
        validate_assignment = True
        # JSON编码器
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def merge_with(self, other: 'BaseConfig') -> 'BaseConfig':
        """与另一个配置合并
        
        Args:
            other: 另一个配置对象
            
        Returns:
            合并后的新配置对象
        """
        # 获取两个配置的字典表示
        self_dict = self.dict()
        other_dict = other.dict()
        
        # 深度合并
        merged_dict = self._deep_merge(self_dict, other_dict)
        
        # 创建新的配置对象
        return self.__class__(**merged_dict)
    
    def _deep_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并两个字典"""
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def save_to_file(self, file_path: Union[str, Path], format: str = "auto"):
        """保存配置到文件
        
        Args:
            file_path: 文件路径
            format: 文件格式 (json, yaml, auto)
        """
        file_path = Path(file_path)
        
        # 自动检测格式
        if format == "auto":
            format = file_path.suffix.lower().lstrip('.')
            if format not in ['json', 'yaml', 'yml']:
                format = 'json'
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 更新时间戳
        self.update_timestamp()
        
        # 序列化数据
        data = self.dict()
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            if format == 'json':
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            elif format in ['yaml', 'yml']:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"不支持的文件格式: {format}")
    
    @classmethod
    def load_from_file(cls, file_path: Union[str, Path], format: str = "auto") -> 'BaseConfig':
        """从文件加载配置
        
        Args:
            file_path: 文件路径
            format: 文件格式 (json, yaml, auto)
            
        Returns:
            配置对象
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        # 自动检测格式
        if format == "auto":
            format = file_path.suffix.lower().lstrip('.')
            if format not in ['json', 'yaml', 'yml']:
                format = 'json'
        
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析内容
        if format == 'json':
            data = json.loads(content)
        elif format in ['yaml', 'yml']:
            data = yaml.safe_load(content)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        
        return cls(**data)
    
    @classmethod
    def from_env(cls, prefix: str = "") -> 'BaseConfig':
        """从环境变量创建配置
        
        Args:
            prefix: 环境变量前缀
            
        Returns:
            配置对象
        """
        import os
        
        env_vars = {}
        for key, value in os.environ.items():
            if key.startswith(prefix):
                # 移除前缀并转换为小写
                config_key = key[len(prefix):].lower()
                
                # 尝试转换类型
                try:
                    # 尝试解析为JSON
                    env_vars[config_key] = json.loads(value)
                except (json.JSONDecodeError, ValueError):
                    # 如果不是JSON，保持字符串
                    env_vars[config_key] = value
        
        return cls(**env_vars)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.dict()
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return self.json(indent=2, ensure_ascii=False)
    
    def copy_with_updates(self, **updates) -> 'BaseConfig':
        """复制并更新配置
        
        Args:
            **updates: 要更新的字段
            
        Returns:
            新的配置对象
        """
        data = self.dict()
        data.update(updates)
        return self.__class__(**data)
    
    def validate_config(self) -> bool:
        """验证配置有效性
        
        Returns:
            是否有效
        """
        try:
            # 重新验证所有字段
            self.__class__(**self.dict())
            return True
        except Exception:
            return False
    
    def get_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'config_name': self.config_name,
            'config_version': self.config_version,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'field_count': len(self.__fields__),
        }


class ConfigManager:
    """配置管理器
    
    管理多个配置对象，支持配置的加载、保存、合并等操作。
    """
    
    def __init__(self):
        self._configs: Dict[str, BaseConfig] = {}
        self._config_files: Dict[str, Path] = {}
    
    def register_config(self, name: str, config: BaseConfig, file_path: Optional[Path] = None):
        """注册配置
        
        Args:
            name: 配置名称
            config: 配置对象
            file_path: 配置文件路径
        """
        self._configs[name] = config
        if file_path:
            self._config_files[name] = Path(file_path)
    
    def get_config(self, name: str) -> Optional[BaseConfig]:
        """获取配置"""
        return self._configs.get(name)
    
    def update_config(self, name: str, config: BaseConfig):
        """更新配置"""
        if name in self._configs:
            self._configs[name] = config
            config.update_timestamp()
    
    def save_config(self, name: str, file_path: Optional[Path] = None):
        """保存配置到文件"""
        if name not in self._configs:
            raise ValueError(f"配置不存在: {name}")
        
        config = self._configs[name]
        target_path = file_path or self._config_files.get(name)
        
        if not target_path:
            raise ValueError(f"未指定配置文件路径: {name}")
        
        config.save_to_file(target_path)
    
    def load_config(self, name: str, file_path: Path, config_class: type):
        """从文件加载配置"""
        config = config_class.load_from_file(file_path)
        self.register_config(name, config, file_path)
        return config
    
    def reload_config(self, name: str):
        """重新加载配置"""
        if name not in self._config_files:
            raise ValueError(f"配置文件路径未知: {name}")
        
        file_path = self._config_files[name]
        config_class = type(self._configs[name])
        
        return self.load_config(name, file_path, config_class)
    
    def get_all_configs(self) -> Dict[str, BaseConfig]:
        """获取所有配置"""
        return self._configs.copy()
    
    def clear_configs(self):
        """清空所有配置"""
        self._configs.clear()
        self._config_files.clear()
    
    def get_config_summary(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置的摘要"""
        return {name: config.get_summary() for name, config in self._configs.items()}


# 全局配置管理器实例
config_manager = ConfigManager()
