"""
回测配置模块

定义回测相关的所有配置参数，包括资金管理、手续费、滑点等。
"""

from typing import Dict, Any, Optional, Union
from datetime import datetime, date
from pydantic import BaseModel, Field, validator
from enum import Enum


class FreqType(str, Enum):
    """频率类型"""
    DAILY = "1d"
    HOURLY = "1h"
    MINUTE_30 = "30min"
    MINUTE_15 = "15min"
    MINUTE_5 = "5min"
    MINUTE_1 = "1min"


class AssetType(str, Enum):
    """资产类型"""
    STOCK = "stock"
    FUND = "fund"
    BOND = "bond"
    CONVERTIBLE_BOND = "convertible_bond"
    INDEX = "index"


class OrderType(str, Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class BacktestConfig(BaseModel):
    """回测配置"""
    
    # 基础参数
    start_date: Union[str, date, datetime] = Field(..., description="回测开始日期")
    end_date: Union[str, date, datetime] = Field(..., description="回测结束日期")
    initial_cash: float = Field(default=100000.0, description="初始资金")
    freq: FreqType = Field(default=FreqType.DAILY, description="数据频率")
    
    # 交易成本
    commission: float = Field(default=0.0005, description="手续费率")
    slippage: float = Field(default=0.001, description="滑点率")
    min_commission: float = Field(default=5.0, description="最小手续费")
    
    # 资金管理
    max_position_size: float = Field(default=1.0, description="最大仓位比例")
    min_trade_amount: float = Field(default=1000.0, description="最小交易金额")
    cash_reserve_ratio: float = Field(default=0.05, description="现金保留比例")
    
    # 风险控制
    max_drawdown_limit: Optional[float] = Field(default=None, description="最大回撤限制")
    stop_loss_ratio: Optional[float] = Field(default=None, description="止损比例")
    take_profit_ratio: Optional[float] = Field(default=None, description="止盈比例")
    
    # 其他设置
    benchmark: Optional[str] = Field(default=None, description="基准指数")
    asset_type: AssetType = Field(default=AssetType.STOCK, description="资产类型")
    order_type: OrderType = Field(default=OrderType.MARKET, description="默认订单类型")
    
    # 高级设置
    allow_short: bool = Field(default=False, description="是否允许做空")
    margin_ratio: float = Field(default=1.0, description="保证金比例")
    interest_rate: float = Field(default=0.03, description="无风险利率")
    
    @validator('start_date', 'end_date', pre=True)
    def parse_date(cls, v):
        """解析日期"""
        if isinstance(v, str):
            try:
                return datetime.strptime(v, "%Y-%m-%d").date()
            except ValueError:
                try:
                    return datetime.strptime(v, "%Y%m%d").date()
                except ValueError:
                    raise ValueError(f"无效的日期格式: {v}")
        elif isinstance(v, datetime):
            return v.date()
        return v
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        """验证日期范围"""
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError("结束日期必须大于开始日期")
        return v
    
    @validator('initial_cash')
    def validate_initial_cash(cls, v):
        """验证初始资金"""
        if v <= 0:
            raise ValueError("初始资金必须大于0")
        return v
    
    @validator('commission', 'slippage')
    def validate_rates(cls, v):
        """验证费率"""
        if v < 0 or v > 1:
            raise ValueError("费率必须在0-1之间")
        return v
    
    @validator('max_position_size', 'cash_reserve_ratio')
    def validate_ratios(cls, v):
        """验证比例参数"""
        if v < 0 or v > 1:
            raise ValueError("比例参数必须在0-1之间")
        return v
    
    def to_vectorbt_params(self) -> Dict[str, Any]:
        """转换为VectorBT参数格式"""
        return {
            'init_cash': self.initial_cash,
            'fees': self.commission,
            'slippage': self.slippage,
            'min_size': self.min_trade_amount,
            'max_size': self.initial_cash * self.max_position_size,
            'size_type': 'amount',
            'direction': 'both' if self.allow_short else 'longonly',
            'freq': self.freq.value,
        }


class OptimizationConfig(BaseModel):
    """参数优化配置"""
    
    # 优化方法
    method: str = Field(default="grid", description="优化方法: grid, bayesian, genetic")
    
    # 网格搜索参数
    grid_params: Dict[str, Any] = Field(default_factory=dict, description="网格搜索参数空间")
    
    # 贝叶斯优化参数
    n_trials: int = Field(default=100, description="贝叶斯优化试验次数")
    n_startup_trials: int = Field(default=10, description="随机试验次数")
    
    # 目标函数
    objective: str = Field(default="sharpe_ratio", description="优化目标")
    direction: str = Field(default="maximize", description="优化方向")
    
    # 并行设置
    n_jobs: int = Field(default=1, description="并行任务数")
    
    # 验证设置
    cv_folds: int = Field(default=5, description="交叉验证折数")
    test_size: float = Field(default=0.2, description="测试集比例")
    
    @validator('method')
    def validate_method(cls, v):
        """验证优化方法"""
        valid_methods = ['grid', 'bayesian', 'genetic', 'random']
        if v not in valid_methods:
            raise ValueError(f"优化方法必须是: {valid_methods}")
        return v
    
    @validator('objective')
    def validate_objective(cls, v):
        """验证优化目标"""
        valid_objectives = [
            'total_return', 'sharpe_ratio', 'calmar_ratio', 
            'max_drawdown', 'win_rate', 'profit_factor'
        ]
        if v not in valid_objectives:
            raise ValueError(f"优化目标必须是: {valid_objectives}")
        return v
    
    @validator('direction')
    def validate_direction(cls, v):
        """验证优化方向"""
        if v not in ['maximize', 'minimize']:
            raise ValueError("优化方向必须是: maximize 或 minimize")
        return v


__all__ = [
    "BacktestConfig",
    "OptimizationConfig", 
    "FreqType",
    "AssetType",
    "OrderType"
]
