"""
文件数据源

支持从CSV、Excel等文件读取数据。
"""

from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd
from pathlib import Path
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from dataseed.base import DataSource
from dataseed.adapter import StandardDataAdapter
from utils.logger import get_logger

logger = get_logger(__name__)


class FileDataSource(DataSource):
    """文件数据源

    支持从本地文件读取数据：
    - CSV文件
    - Excel文件
    - Parquet文件
    """

    def __init__(
        self,
        name: str = "file",
        data_dir: Union[str, Path] = "data",
        file_pattern: str = "{symbol}.csv",
        **kwargs
    ):
        """
        初始化文件数据源

        Args:
            name: 数据源名称
            data_dir: 数据目录
            file_pattern: 文件名模式，{symbol}会被替换为股票代码
            **kwargs: 其他参数
        """
        # 使用标准数据适配器
        if 'adapter' not in kwargs:
            kwargs['adapter'] = StandardDataAdapter()

        super().__init__(name, **kwargs)

        self.data_dir = Path(data_dir)
        self.file_pattern = file_pattern

        # 确保数据目录存在
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # 缓存文件列表
        self._file_cache = {}
        self._refresh_file_cache()

        logger.info(f"文件数据源初始化完成: {data_dir}")

    def _refresh_file_cache(self):
        """刷新文件缓存"""
        self._file_cache.clear()

        # 扫描数据目录
        for file_path in self.data_dir.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in ['.csv', '.xlsx', '.xls', '.parquet']:
                # 尝试从文件名提取股票代码
                symbol = self._extract_symbol_from_filename(file_path.name)
                if symbol:
                    self._file_cache[symbol] = file_path

        logger.debug(f"文件缓存已刷新: {len(self._file_cache)}个文件")

    def _extract_symbol_from_filename(self, filename: str) -> Optional[str]:
        """从文件名提取股票代码"""
        # 移除扩展名
        name_without_ext = Path(filename).stem

        # 简单的提取逻辑，可以根据需要扩展
        if name_without_ext.isdigit() and len(name_without_ext) == 6:
            return name_without_ext

        # 尝试其他模式
        import re

        # 匹配6位数字
        match = re.search(r'\b(\d{6})\b', name_without_ext)
        if match:
            return match.group(1)

        # 如果没有找到，返回文件名本身
        return name_without_ext

    def _get_file_path(self, symbol: str) -> Optional[Path]:
        """获取股票对应的文件路径"""
        # 先检查缓存
        if symbol in self._file_cache:
            return self._file_cache[symbol]

        # 根据模式生成文件路径
        filename = self.file_pattern.format(symbol=symbol)
        file_path = self.data_dir / filename

        if file_path.exists():
            return file_path

        # 尝试其他扩展名
        for ext in ['.csv', '.xlsx', '.xls', '.parquet']:
            alt_path = file_path.with_suffix(ext)
            if alt_path.exists():
                return alt_path

        return None

    def _fetch_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """
        从文件读取数据

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            **kwargs: 其他参数

        Returns:
            原始数据DataFrame
        """
        # 获取文件路径
        file_path = self._get_file_path(symbol)

        if file_path is None:
            logger.warning(f"未找到股票数据文件: {symbol}")
            return pd.DataFrame()

        try:
            # 根据文件类型读取数据
            if file_path.suffix.lower() == '.csv':
                data = self._read_csv(file_path, **kwargs)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                data = self._read_excel(file_path, **kwargs)
            elif file_path.suffix.lower() == '.parquet':
                data = self._read_parquet(file_path, **kwargs)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")

            if data.empty:
                logger.warning(f"文件数据为空: {file_path}")
                return data

            # 过滤日期范围
            data = self._filter_date_range(data, start_date, end_date)

            logger.debug(f"文件数据读取成功: {symbol}, {len(data)}条记录")

            return data

        except Exception as e:
            logger.error(f"文件数据读取失败: {file_path}, 错误: {e}")
            raise

    def _read_csv(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """读取CSV文件"""
        # 默认参数
        csv_kwargs = {
            'encoding': 'utf-8',
            'parse_dates': True,
            'index_col': 0,
        }
        csv_kwargs.update(kwargs.get('csv_kwargs', {}))

        try:
            return pd.read_csv(file_path, **csv_kwargs)
        except UnicodeDecodeError:
            # 尝试其他编码
            csv_kwargs['encoding'] = 'gbk'
            return pd.read_csv(file_path, **csv_kwargs)

    def _read_excel(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """读取Excel文件"""
        excel_kwargs = {
            'index_col': 0,
            'parse_dates': True,
        }
        excel_kwargs.update(kwargs.get('excel_kwargs', {}))

        return pd.read_excel(file_path, **excel_kwargs)

    def _read_parquet(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """读取Parquet文件"""
        return pd.read_parquet(file_path)

    def _filter_date_range(
        self,
        data: pd.DataFrame,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime]
    ) -> pd.DataFrame:
        """过滤日期范围"""
        if data.empty:
            return data

        # 确保索引是日期时间类型
        if not isinstance(data.index, pd.DatetimeIndex):
            # 尝试转换索引
            try:
                data.index = pd.to_datetime(data.index)
            except Exception:
                logger.warning("无法将索引转换为日期时间类型")
                return data

        # 转换日期
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)

        # 过滤数据
        mask = (data.index >= start_date) & (data.index <= end_date)
        return data[mask]

    def get_available_symbols(self) -> List[str]:
        """获取可用的股票代码列表"""
        self._refresh_file_cache()
        return list(self._file_cache.keys())

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        file_path = self._get_file_path(symbol)

        if file_path is None:
            return {'symbol': symbol, 'error': '文件不存在'}

        try:
            # 读取文件基本信息
            stat = file_path.stat()

            # 尝试读取数据获取更多信息
            data = self._fetch_data(symbol, "1900-01-01", "2100-01-01")

            info = {
                'symbol': symbol,
                'file_path': str(file_path),
                'file_size': stat.st_size,
                'file_modified': datetime.fromtimestamp(stat.st_mtime),
                'data_records': len(data) if not data.empty else 0,
            }

            if not data.empty:
                info.update({
                    'date_range': f"{data.index.min()} to {data.index.max()}",
                    'columns': list(data.columns),
                })

            return info

        except Exception as e:
            return {'symbol': symbol, 'error': str(e)}

    def add_data_file(
        self,
        symbol: str,
        data: pd.DataFrame,
        file_format: str = "csv"
    ):
        """
        添加数据文件

        Args:
            symbol: 股票代码
            data: 数据DataFrame
            file_format: 文件格式
        """
        filename = self.file_pattern.format(symbol=symbol)

        if file_format.lower() == "csv":
            file_path = self.data_dir / f"{Path(filename).stem}.csv"
            data.to_csv(file_path, encoding='utf-8')
        elif file_format.lower() in ["xlsx", "excel"]:
            file_path = self.data_dir / f"{Path(filename).stem}.xlsx"
            data.to_excel(file_path)
        elif file_format.lower() == "parquet":
            file_path = self.data_dir / f"{Path(filename).stem}.parquet"
            data.to_parquet(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_format}")

        # 更新缓存
        self._file_cache[symbol] = file_path

        logger.info(f"数据文件已添加: {symbol} -> {file_path}")

    def remove_data_file(self, symbol: str):
        """删除数据文件"""
        file_path = self._get_file_path(symbol)

        if file_path and file_path.exists():
            file_path.unlink()

            # 更新缓存
            if symbol in self._file_cache:
                del self._file_cache[symbol]

            logger.info(f"数据文件已删除: {symbol}")

    def scan_directory(self, directory: Union[str, Path] = None):
        """扫描目录中的数据文件"""
        if directory:
            self.data_dir = Path(directory)

        self._refresh_file_cache()

        logger.info(f"目录扫描完成: {len(self._file_cache)}个文件")
