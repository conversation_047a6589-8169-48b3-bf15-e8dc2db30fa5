"""
持仓风险规则模块

定义与持仓相关的风险控制规则。
"""

from typing import Dict, Any
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from .base import BaseRiskRule, RiskRuleResult, RiskLevel
from core.structures import Order, Position, Portfolio, OrderSide


class PositionSizeRule(BaseRiskRule):
    """持仓大小规则
    
    控制单个持仓的最大大小。
    """
    
    def __init__(self, max_position_ratio: float = 0.1, **kwargs):
        """
        初始化持仓大小规则
        
        Args:
            max_position_ratio: 最大持仓比例
        """
        super().__init__("position_size", max_position_ratio=max_position_ratio, **kwargs)
        self.max_position_ratio = max_position_ratio
    
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskRuleResult:
        """检查订单的持仓大小"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        if order.side == OrderSide.SELL:
            return RiskRuleResult.pass_result(self.name, "卖出订单无需检查持仓大小")
        
        # 计算订单后的持仓价值
        order_value = order.quantity * (order.price or 0)
        current_position = portfolio.get_position(order.symbol)
        current_value = current_position.market_value if current_position else 0
        
        new_position_value = current_value + order_value
        position_ratio = new_position_value / portfolio.total_value if portfolio.total_value > 0 else 0
        
        if position_ratio > self.max_position_ratio:
            return RiskRuleResult.fail_result(
                self.name,
                f"持仓比例({position_ratio:.2%})超过限制({self.max_position_ratio:.2%})",
                RiskLevel.HIGH,
                {
                    'current_ratio': position_ratio,
                    'max_ratio': self.max_position_ratio,
                    'position_value': new_position_value,
                    'portfolio_value': portfolio.total_value
                }
            )
        
        return RiskRuleResult.pass_result(
            self.name, 
            f"持仓比例({position_ratio:.2%})在限制内"
        )
    
    def check_position(self, position: Position, portfolio: Portfolio) -> RiskRuleResult:
        """检查现有持仓大小"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        position_ratio = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
        
        if position_ratio > self.max_position_ratio:
            return RiskRuleResult.fail_result(
                self.name,
                f"持仓比例({position_ratio:.2%})超过限制({self.max_position_ratio:.2%})",
                RiskLevel.MEDIUM,
                {
                    'current_ratio': position_ratio,
                    'max_ratio': self.max_position_ratio,
                    'symbol': position.symbol
                }
            )
        
        return RiskRuleResult.pass_result(self.name)
    
    def check_portfolio(self, portfolio: Portfolio) -> RiskRuleResult:
        """检查投资组合的持仓分布"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        violations = []
        for symbol, position in portfolio.positions.items():
            result = self.check_position(position, portfolio)
            if not result.passed:
                violations.append(symbol)
        
        if violations:
            return RiskRuleResult.fail_result(
                self.name,
                f"以下持仓超过大小限制: {', '.join(violations)}",
                RiskLevel.MEDIUM,
                {'violations': violations}
            )
        
        return RiskRuleResult.pass_result(self.name, "所有持仓大小在限制内")


class ConcentrationRule(BaseRiskRule):
    """集中度风险规则
    
    控制投资组合的集中度风险。
    """
    
    def __init__(self, max_concentration: float = 0.3, **kwargs):
        """
        初始化集中度规则
        
        Args:
            max_concentration: 最大集中度（赫芬达尔指数）
        """
        super().__init__("concentration", max_concentration=max_concentration, **kwargs)
        self.max_concentration = max_concentration
    
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskRuleResult:
        """检查订单对集中度的影响"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        # 模拟订单执行后的持仓
        simulated_positions = portfolio.positions.copy()
        
        if order.side == OrderSide.BUY:
            order_value = order.quantity * (order.price or 0)
            if order.symbol in simulated_positions:
                # 增加现有持仓
                pos = simulated_positions[order.symbol]
                pos.quantity += order.quantity
            else:
                # 新建持仓
                from core.structures.trade import PositionSide
                new_pos = Position(
                    symbol=order.symbol,
                    side=PositionSide.LONG,
                    quantity=order.quantity,
                    avg_price=order.price or 0,
                    timestamp=order.timestamp
                )
                simulated_positions[order.symbol] = new_pos
        
        # 计算集中度
        concentration = self._calculate_concentration(simulated_positions, portfolio.total_value)
        
        if concentration > self.max_concentration:
            return RiskRuleResult.fail_result(
                self.name,
                f"集中度({concentration:.3f})超过限制({self.max_concentration:.3f})",
                RiskLevel.HIGH,
                {
                    'current_concentration': concentration,
                    'max_concentration': self.max_concentration
                }
            )
        
        return RiskRuleResult.pass_result(self.name, f"集中度({concentration:.3f})在限制内")
    
    def check_position(self, position: Position, portfolio: Portfolio) -> RiskRuleResult:
        """检查单个持仓对集中度的贡献"""
        return RiskRuleResult.pass_result(self.name, "单个持仓检查不适用于集中度规则")
    
    def check_portfolio(self, portfolio: Portfolio) -> RiskRuleResult:
        """检查投资组合集中度"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        concentration = self._calculate_concentration(portfolio.positions, portfolio.total_value)
        
        if concentration > self.max_concentration:
            return RiskRuleResult.fail_result(
                self.name,
                f"投资组合集中度({concentration:.3f})超过限制({self.max_concentration:.3f})",
                RiskLevel.MEDIUM,
                {
                    'current_concentration': concentration,
                    'max_concentration': self.max_concentration
                }
            )
        
        return RiskRuleResult.pass_result(self.name, f"集中度({concentration:.3f})在限制内")
    
    def _calculate_concentration(self, positions: Dict[str, Position], total_value: float) -> float:
        """计算集中度（赫芬达尔指数）"""
        if not positions or total_value <= 0:
            return 0.0
        
        weights = []
        for position in positions.values():
            weight = position.market_value / total_value
            weights.append(weight)
        
        # 赫芬达尔指数
        return sum(w**2 for w in weights)


class LeverageRule(BaseRiskRule):
    """杠杆规则
    
    控制投资组合的杠杆水平。
    """
    
    def __init__(self, max_leverage: float = 2.0, **kwargs):
        """
        初始化杠杆规则
        
        Args:
            max_leverage: 最大杠杆倍数
        """
        super().__init__("leverage", max_leverage=max_leverage, **kwargs)
        self.max_leverage = max_leverage
    
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskRuleResult:
        """检查订单对杠杆的影响"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        if order.side == OrderSide.SELL:
            return RiskRuleResult.pass_result(self.name, "卖出订单降低杠杆")
        
        # 计算新的杠杆水平
        order_value = order.quantity * (order.price or 0)
        new_exposure = portfolio.positions_value + order_value
        new_leverage = new_exposure / portfolio.total_value if portfolio.total_value > 0 else 1.0
        
        if new_leverage > self.max_leverage:
            return RiskRuleResult.fail_result(
                self.name,
                f"杠杆({new_leverage:.2f})超过限制({self.max_leverage:.2f})",
                RiskLevel.HIGH,
                {
                    'current_leverage': new_leverage,
                    'max_leverage': self.max_leverage,
                    'new_exposure': new_exposure,
                    'portfolio_value': portfolio.total_value
                }
            )
        
        return RiskRuleResult.pass_result(self.name, f"杠杆({new_leverage:.2f})在限制内")
    
    def check_position(self, position: Position, portfolio: Portfolio) -> RiskRuleResult:
        """检查单个持仓对杠杆的贡献"""
        return RiskRuleResult.pass_result(self.name, "单个持仓检查不适用于杠杆规则")
    
    def check_portfolio(self, portfolio: Portfolio) -> RiskRuleResult:
        """检查投资组合杠杆"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        current_leverage = portfolio.leverage
        
        if current_leverage > self.max_leverage:
            return RiskRuleResult.fail_result(
                self.name,
                f"当前杠杆({current_leverage:.2f})超过限制({self.max_leverage:.2f})",
                RiskLevel.HIGH,
                {
                    'current_leverage': current_leverage,
                    'max_leverage': self.max_leverage
                }
            )
        
        return RiskRuleResult.pass_result(self.name, f"杠杆({current_leverage:.2f})在限制内")
