"""
回测引擎模块

提供量化交易策略的回测功能，支持数据缓存、风险管理和并行回测。
"""

import pandas as pd
import numpy as np
import vectorbt as vbt
import logging
import time
from datetime import datetime, timedelta
import concurrent.futures
from typing import List, Dict, Union, Optional, Any, Tuple
from pathlib import Path

from dataseed.base import BaseDataSeed
from strategies.base import BaseStrategy
from utils.cache import DataCache
from core.config import ConfigManager
from core.risk import RiskManager, RiskLimitType


class QuantEngine:
    """增强版回测引擎，支持数据缓存、风险管理和并行回测"""

    def __init__(self, dataseed: BaseDataSeed, strategy: BaseStrategy,
                 initial_capital: float = 100000.0, commission: float = 0.0003,
                 slippage: float = 0.001, position_size: float = 1.0,
                 config: Optional[ConfigManager] = None):
        """
        初始化回测引擎

        参数:
            dataseed (BaseDataSeed): 数据源实例
            strategy (BaseStrategy): 策略实例
            initial_capital (float): 初始资金
            commission (float): 手续费率
            slippage (float): 滑点率
            position_size (float): 仓位比例，1.0表示满仓
            config (ConfigManager): 配置管理器
        """
        self.dataseed = dataseed
        self.strategy = strategy
        self.initial_capital = initial_capital
        self.commission = commission
        self.slippage = slippage
        self.position_size = position_size

        # 配置管理
        self.config = config or ConfigManager()

        # 数据缓存
        cache_enabled = self.config.get('dataseed.cache.enabled', True)
        if cache_enabled:
            self.cache = DataCache()
        else:
            self.cache = None

        # 风险管理
        self.risk_manager = RiskManager()
        self._setup_risk_limits()

        # 日志
        self.logger = logging.getLogger('quant.engine')

    def _setup_risk_limits(self):
        """设置风险限制"""
        risk_config = self.config.get_section('risk')

        if 'max_drawdown' in risk_config:
            self.risk_manager.set_risk_limit(RiskLimitType.MAX_DRAWDOWN, risk_config['max_drawdown'])
        if 'max_position_size' in risk_config:
            self.risk_manager.set_risk_limit(RiskLimitType.MAX_POSITION_SIZE, risk_config['max_position_size'])
        if 'stop_loss' in risk_config:
            self.risk_manager.set_risk_limit(RiskLimitType.STOP_LOSS, risk_config['stop_loss'])
        if 'take_profit' in risk_config:
            self.risk_manager.set_risk_limit(RiskLimitType.TAKE_PROFIT, risk_config['take_profit'])

    def _get_cached_data(self, symbol: str, start_date: str, end_date: str, timeframe: str) -> Optional[pd.DataFrame]:
        """从缓存获取数据"""
        if not self.cache:
            return None

        cache_key = {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date,
            'timeframe': timeframe,
            'dataseed': self.dataseed.__class__.__name__
        }

        max_age_hours = self.config.get('dataseed.cache.max_age_hours', 24)
        max_age = timedelta(hours=max_age_hours)

        return self.cache.get(cache_key, max_age)

    def _cache_data(self, data: pd.DataFrame, symbol: str, start_date: str, end_date: str, timeframe: str):
        """缓存数据"""
        if not self.cache:
            return

        cache_key = {
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date,
            'timeframe': timeframe,
            'dataseed': self.dataseed.__class__.__name__
        }

        self.cache.set(cache_key, data)

    def run(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d',
            apply_risk_management: bool = True) -> Tuple[vbt.Portfolio, Dict[str, Any]]:
        """
        运行单资产回测

        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            timeframe (str): 时间周期
            apply_risk_management (bool): 是否应用风险管理

        返回:
            Tuple[vbt.Portfolio, Dict[str, Any]]: 回测结果和性能指标
        """
        start_time = time.time()

        self.logger.info(f"开始回测: {symbol}, {start_date} 到 {end_date}")

        # 获取数据
        data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)
        if data.empty:
            raise ValueError(f"无法获取数据: {symbol}")

        # 运行策略获取信号
        self.logger.info("执行策略计算...")
        entries, exits = self.strategy.run(data)

        # 应用风险管理
        if apply_risk_management:
            entries, exits = self._apply_risk_management(data, entries, exits)

        # 执行回测
        self.logger.info("执行回测计算...")
        portfolio = self._run_backtest(data, entries, exits, timeframe)

        # 计算性能指标
        metrics = self._calculate_metrics(portfolio, data)

        execution_time = time.time() - start_time
        self.logger.info(f"回测完成，耗时: {execution_time:.2f}秒")

        return portfolio, metrics

    def _get_data_with_cache(self, symbol: str, start_date: str, end_date: str, timeframe: str) -> pd.DataFrame:
        """获取数据（支持缓存）"""
        # 尝试从缓存获取
        data = self._get_cached_data(symbol, start_date, end_date, timeframe)
        if data is not None:
            self.logger.info(f"从缓存加载数据: {symbol}")
            return data

        # 从数据源获取
        self.logger.info(f"从数据源获取数据: {symbol}")
        data = self.dataseed.get_data(symbol, start_date, end_date, timeframe)

        if not data.empty:
            # 缓存数据
            self._cache_data(data, symbol, start_date, end_date, timeframe)

        return data

    def _apply_risk_management(self, data: pd.DataFrame, entries: np.ndarray, exits: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """应用风险管理"""
        # 这里可以根据风险管理规则调整信号
        # 例如：在高波动期间减少交易频率，或者在回撤过大时停止交易

        # 简单示例：计算波动率，在高波动期间减少信号
        returns = data['close'].pct_change()
        volatility = returns.rolling(window=20).std()
        high_vol_threshold = volatility.quantile(0.8)

        # 在高波动期间减少信号强度
        high_vol_mask = volatility > high_vol_threshold
        entries = entries & ~high_vol_mask

        return entries, exits

    def _run_backtest(self, data: pd.DataFrame, entries: np.ndarray, exits: np.ndarray, timeframe: str) -> vbt.Portfolio:
        """执行回测计算"""
        # 确保信号是布尔类型
        entries = entries.astype(bool)
        exits = exits.astype(bool)

        # 使用VectorBT进行回测
        portfolio = vbt.Portfolio.from_signals(
            data['close'],
            entries,
            exits,
            init_cash=self.initial_capital,
            fees=self.commission,
            slippage=self.slippage,
            size=self.position_size,
            size_type='percent',
            freq=timeframe
        )

        return portfolio

    def _calculate_metrics(self, portfolio: vbt.Portfolio, data: pd.DataFrame) -> Dict[str, Any]:
        """计算性能指标"""
        metrics = {}

        # 基本指标
        metrics['total_return'] = portfolio.total_return()
        metrics['annual_return'] = portfolio.annualized_return()
        metrics['max_drawdown'] = portfolio.max_drawdown()
        metrics['sharpe_ratio'] = portfolio.sharpe_ratio()

        # 交易统计
        trades = portfolio.trades
        metrics['total_trades'] = trades.count()
        metrics['win_rate'] = trades.win_rate() if trades.count() > 0 else 0
        metrics['avg_trade_return'] = trades.returns.mean() if trades.count() > 0 else 0

        # 风险指标
        returns = portfolio.returns()
        if len(returns) > 0:
            metrics['volatility'] = returns.std() * np.sqrt(252)
            metrics['var_95'] = np.percentile(returns, 5)
            metrics['var_99'] = np.percentile(returns, 1)

        # 卡尔玛比率（年化收益率/最大回撤）
        if metrics['max_drawdown'] != 0:
            metrics['calmar_ratio'] = metrics['annual_return'] / abs(metrics['max_drawdown'])
        else:
            metrics['calmar_ratio'] = np.nan

        return metrics

    def run_multiple(self, symbols: List[str], start_date: str, end_date: str, timeframe: str = '1d',
                    apply_risk_management: bool = True, parallel: bool = True) -> Tuple[vbt.Portfolio, Dict[str, Dict[str, Any]]]:
        """
        运行多资产回测

        参数:
            symbols (list): 证券代码列表
            start_date (str): 开始日期
            end_date (str): 结束日期
            timeframe (str): 时间周期
            apply_risk_management (bool): 是否应用风险管理
            parallel (bool): 是否并行处理

        返回:
            Tuple[vbt.Portfolio, Dict[str, Dict[str, Any]]]: 回测结果和每个资产的性能指标
        """
        start_time = time.time()

        # 获取多个资产的数据和信号
        all_data = {}
        all_entries = {}
        all_exits = {}
        all_metrics = {}

        if parallel and len(symbols) > 1:
            # 并行处理
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                future_to_symbol = {
                    executor.submit(self._process_single_symbol, symbol, start_date, end_date, timeframe, apply_risk_management): symbol
                    for symbol in symbols
                }

                for future in concurrent.futures.as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        data, entries, exits, metrics = future.result()
                        all_data[symbol] = data
                        all_entries[symbol] = entries
                        all_exits[symbol] = exits
                        all_metrics[symbol] = metrics
                    except Exception as e:
                        self.logger.error(f"处理{symbol}时出错: {str(e)}")
        else:
            # 串行处理
            for symbol in symbols:
                try:
                    data, entries, exits, metrics = self._process_single_symbol(symbol, start_date, end_date, timeframe, apply_risk_management)
                    all_data[symbol] = data
                    all_entries[symbol] = entries
                    all_exits[symbol] = exits
                    all_metrics[symbol] = metrics
                except Exception as e:
                    self.logger.error(f"处理{symbol}时出错: {str(e)}")

        # 合并数据进行组合回测
        if all_data:
            combined_portfolio = self._run_portfolio_backtest(all_data, all_entries, all_exits, timeframe)
        else:
            raise ValueError("没有成功处理任何资产")

        execution_time = time.time() - start_time
        self.logger.info(f"多资产回测完成，耗时: {execution_time:.2f}秒")

        return combined_portfolio, all_metrics

    def _process_single_symbol(self, symbol: str, start_date: str, end_date: str, timeframe: str, apply_risk_management: bool) -> Tuple[pd.DataFrame, np.ndarray, np.ndarray, Dict[str, Any]]:
        """处理单个资产"""
        # 获取数据
        data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)
        if data.empty:
            raise ValueError(f"无法获取数据: {symbol}")

        # 运行策略
        entries, exits = self.strategy.run(data)

        # 应用风险管理
        if apply_risk_management:
            entries, exits = self._apply_risk_management(data, entries, exits)

        # 计算单资产指标
        portfolio = self._run_backtest(data, entries, exits, timeframe)
        metrics = self._calculate_metrics(portfolio, data)

        return data, entries, exits, metrics

    def _run_portfolio_backtest(self, all_data: Dict[str, pd.DataFrame], all_entries: Dict[str, np.ndarray],
                               all_exits: Dict[str, np.ndarray], timeframe: str) -> vbt.Portfolio:
        """运行投资组合回测"""
        # 合并所有资产的价格数据
        price_data = {}
        entries_data = {}
        exits_data = {}

        for symbol in all_data.keys():
            price_data[symbol] = all_data[symbol]['close']
            entries_data[symbol] = all_entries[symbol]
            exits_data[symbol] = all_exits[symbol]

        # 创建DataFrame
        prices_df = pd.DataFrame(price_data)
        entries_df = pd.DataFrame(entries_data, index=prices_df.index)
        exits_df = pd.DataFrame(exits_data, index=prices_df.index)

        # 填充NaN值
        prices_df = prices_df.fillna(method='ffill').fillna(method='bfill')
        entries_df = entries_df.fillna(False)
        exits_df = exits_df.fillna(False)

        # 运行组合回测
        portfolio = vbt.Portfolio.from_signals(
            prices_df,
            entries_df,
            exits_df,
            init_cash=self.initial_capital,
            fees=self.commission,
            slippage=self.slippage,
            size=self.position_size / len(all_data),  # 平均分配资金
            size_type='percent',
            freq=timeframe
        )

        return portfolio

    def optimize_parameters(self, symbol: str, start_date: str, end_date: str,
                          param_ranges: Dict[str, List], timeframe: str = '1d',
                          metric: str = 'sharpe_ratio') -> Dict[str, Any]:
        """
        参数优化

        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            param_ranges (dict): 参数范围字典
            timeframe (str): 时间周期
            metric (str): 优化目标指标

        返回:
            dict: 最优参数和结果
        """
        self.logger.info(f"开始参数优化: {symbol}")

        # 获取数据
        data = self._get_data_with_cache(symbol, start_date, end_date, timeframe)
        if data.empty:
            raise ValueError(f"无法获取数据: {symbol}")

        best_params = None
        best_score = float('-inf')
        best_metrics = None

        # 生成参数组合
        param_combinations = self._generate_param_combinations(param_ranges)

        for params in param_combinations:
            try:
                # 设置策略参数
                self.strategy.set_params(params)

                # 运行策略
                entries, exits = self.strategy.run(data)

                # 执行回测
                portfolio = self._run_backtest(data, entries, exits, timeframe)
                metrics = self._calculate_metrics(portfolio, data)

                # 评估结果
                score = metrics.get(metric, float('-inf'))
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    best_metrics = metrics.copy()

            except Exception as e:
                self.logger.warning(f"参数组合失败: {params}, 错误: {str(e)}")
                continue

        self.logger.info(f"参数优化完成，最优{metric}: {best_score:.4f}")

        return {
            'best_params': best_params,
            'best_score': best_score,
            'best_metrics': best_metrics,
            'optimization_metric': metric
        }

    def _generate_param_combinations(self, param_ranges: Dict[str, List]) -> List[Dict[str, Any]]:
        """生成参数组合"""
        import itertools

        keys = list(param_ranges.keys())
        values = list(param_ranges.values())

        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)

        return combinations
