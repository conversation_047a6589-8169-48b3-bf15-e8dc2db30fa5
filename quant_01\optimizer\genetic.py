"""
遗传算法优化器

使用遗传算法进行参数优化，适合复杂的非线性优化问题。
"""

import random
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import pandas as pd
import numpy as np

from .base import BaseOptimizer, OptimizationResult
from ..utils.logger import get_logger

logger = get_logger(__name__)


class Individual:
    """个体类"""
    
    def __init__(self, params: Dict[str, Any], fitness: float = None):
        self.params = params
        self.fitness = fitness
    
    def __repr__(self):
        return f"Individual(fitness={self.fitness}, params={self.params})"


class GeneticOptimizer(BaseOptimizer):
    """遗传算法优化器"""
    
    def __init__(
        self,
        strategy_class: type,
        param_space: Dict[str, Union[List[Any], Dict[str, Any]]],
        objective: str = "sharpe_ratio",
        direction: str = "maximize",
        n_jobs: int = 1,
        random_state: Optional[int] = None,
        population_size: int = 50,
        mutation_rate: float = 0.1,
        crossover_rate: float = 0.8,
        elite_size: int = 5
    ):
        """
        初始化遗传算法优化器
        
        Args:
            strategy_class: 策略类
            param_space: 参数空间
            objective: 优化目标
            direction: 优化方向
            n_jobs: 并行任务数
            random_state: 随机种子
            population_size: 种群大小
            mutation_rate: 变异率
            crossover_rate: 交叉率
            elite_size: 精英个体数量
        """
        super().__init__(strategy_class, param_space, objective, direction, n_jobs, random_state)
        
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.elite_size = elite_size
        
        # 设置随机种子
        if random_state is not None:
            random.seed(random_state)
            np.random.seed(random_state)
        
        logger.info(f"遗传算法优化器初始化完成: 种群大小={population_size}, 变异率={mutation_rate}, 交叉率={crossover_rate}")
    
    def optimize(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame] = None,
        n_generations: int = 50,
        early_stopping: bool = True,
        patience: int = 10,
        **kwargs
    ) -> OptimizationResult:
        """
        执行遗传算法优化
        
        Args:
            data: 训练数据
            validation_data: 验证数据
            n_generations: 进化代数
            early_stopping: 是否启用早停
            patience: 早停耐心值
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        start_time = datetime.now()
        logger.info(f"开始遗传算法优化: {n_generations} 代, 种群大小 {self.population_size}")
        
        # 初始化种群
        population = self._initialize_population()
        
        # 评估初始种群
        self._evaluate_population(population, data, validation_data)
        
        best_fitness_history = []
        best_individual = None
        no_improvement_count = 0
        all_results = []
        
        for generation in range(n_generations):
            # 记录当前最佳个体
            current_best = max(population, key=lambda x: x.fitness) if self.direction == 'maximize' else min(population, key=lambda x: x.fitness)
            
            if best_individual is None or self._is_better_fitness(current_best.fitness, best_individual.fitness):
                best_individual = current_best
                no_improvement_count = 0
            else:
                no_improvement_count += 1
            
            best_fitness_history.append(current_best.fitness)
            
            # 记录所有个体结果
            for individual in population:
                all_results.append({
                    'params': individual.params.copy(),
                    'score': individual.fitness,
                    'generation': generation
                })
            
            # 早停检查
            if early_stopping and no_improvement_count >= patience:
                logger.info(f"早停触发: {patience} 代无改善")
                break
            
            # 选择、交叉、变异
            population = self._evolve_population(population, data, validation_data)
            
            # 进度报告
            if (generation + 1) % 10 == 0:
                logger.info(f"进化进度: 第 {generation + 1}/{n_generations} 代, 最佳适应度: {current_best.fitness:.4f}")
        
        # 构建优化结果
        optimization_result = OptimizationResult(
            best_params=best_individual.params,
            best_score=best_individual.fitness,
            objective=self.objective,
            direction=self.direction,
            n_trials=len(all_results),
            optimization_time=0,  # 将在外部设置
            all_results=all_results
        )
        
        # 计算优化时间
        optimization_time = (datetime.now() - start_time).total_seconds()
        optimization_result.optimization_time = optimization_time
        
        logger.info(f"遗传算法优化完成: 最佳{self.objective} = {optimization_result.best_score:.4f}, 耗时: {optimization_time:.2f}秒")
        
        return optimization_result
    
    def _initialize_population(self) -> List[Individual]:
        """初始化种群"""
        population = []
        
        for _ in range(self.population_size):
            params = self._generate_random_params()
            individual = Individual(params)
            population.append(individual)
        
        return population
    
    def _generate_random_params(self) -> Dict[str, Any]:
        """生成随机参数"""
        params = {}
        
        for param_name, param_config in self.param_space.items():
            if isinstance(param_config, list):
                params[param_name] = random.choice(param_config)
            elif isinstance(param_config, dict):
                params[param_name] = self._sample_continuous_param(param_config)
        
        return params
    
    def _sample_continuous_param(self, config: Dict[str, Any]) -> Any:
        """采样连续参数"""
        dist_type = config.get('type', 'uniform')
        
        if dist_type == 'uniform':
            low = config['low']
            high = config['high']
            if config.get('dtype') == 'int':
                return random.randint(int(low), int(high))
            else:
                return random.uniform(low, high)
        elif dist_type == 'choice':
            return random.choice(config['choices'])
        else:
            # 默认使用均匀分布
            return random.uniform(0, 1)
    
    def _evaluate_population(
        self,
        population: List[Individual],
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ):
        """评估种群"""
        for individual in population:
            if individual.fitness is None:
                try:
                    train_score = self._evaluate_strategy(individual.params, data)
                    
                    if validation_data is not None:
                        val_score = self._evaluate_strategy(individual.params, validation_data)
                        individual.fitness = val_score
                    else:
                        individual.fitness = train_score
                        
                except Exception as e:
                    logger.warning(f"个体评估失败: {e}")
                    individual.fitness = self._get_initial_best_score()
    
    def _evolve_population(
        self,
        population: List[Individual],
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> List[Individual]:
        """进化种群"""
        # 排序种群
        population.sort(key=lambda x: x.fitness, reverse=(self.direction == 'maximize'))
        
        # 保留精英
        new_population = population[:self.elite_size]
        
        # 生成新个体
        while len(new_population) < self.population_size:
            # 选择父母
            parent1 = self._tournament_selection(population)
            parent2 = self._tournament_selection(population)
            
            # 交叉
            if random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1, parent2
            
            # 变异
            if random.random() < self.mutation_rate:
                child1 = self._mutate(child1)
            if random.random() < self.mutation_rate:
                child2 = self._mutate(child2)
            
            new_population.extend([child1, child2])
        
        # 截断到指定大小
        new_population = new_population[:self.population_size]
        
        # 评估新个体
        self._evaluate_population(new_population, data, validation_data)
        
        return new_population
    
    def _tournament_selection(self, population: List[Individual], tournament_size: int = 3) -> Individual:
        """锦标赛选择"""
        tournament = random.sample(population, min(tournament_size, len(population)))
        return max(tournament, key=lambda x: x.fitness) if self.direction == 'maximize' else min(tournament, key=lambda x: x.fitness)
    
    def _crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """交叉操作"""
        child1_params = {}
        child2_params = {}
        
        for param_name in self.param_space.keys():
            if random.random() < 0.5:
                child1_params[param_name] = parent1.params[param_name]
                child2_params[param_name] = parent2.params[param_name]
            else:
                child1_params[param_name] = parent2.params[param_name]
                child2_params[param_name] = parent1.params[param_name]
        
        return Individual(child1_params), Individual(child2_params)
    
    def _mutate(self, individual: Individual) -> Individual:
        """变异操作"""
        mutated_params = individual.params.copy()
        
        # 随机选择一个参数进行变异
        param_name = random.choice(list(self.param_space.keys()))
        param_config = self.param_space[param_name]
        
        if isinstance(param_config, list):
            mutated_params[param_name] = random.choice(param_config)
        elif isinstance(param_config, dict):
            mutated_params[param_name] = self._sample_continuous_param(param_config)
        
        return Individual(mutated_params)
    
    def _is_better_fitness(self, new_fitness: float, best_fitness: float) -> bool:
        """判断新适应度是否更好"""
        if self.direction == 'maximize':
            return new_fitness > best_fitness
        else:
            return new_fitness < best_fitness
    
    def get_optimization_summary(self, result: OptimizationResult) -> Dict[str, Any]:
        """获取优化摘要"""
        summary = {
            'method': 'Genetic Algorithm',
            'population_size': self.population_size,
            'mutation_rate': self.mutation_rate,
            'crossover_rate': self.crossover_rate,
            'elite_size': self.elite_size,
            'n_trials': result.n_trials,
            'best_params': result.best_params,
            'best_score': result.best_score,
            'objective': result.objective,
            'optimization_time': result.optimization_time
        }
        
        return summary
