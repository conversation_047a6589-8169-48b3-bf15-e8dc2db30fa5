"""
数据验证器模块

提供各种数据验证功能，确保数据质量。
"""

from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime
from dataclasses import dataclass

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from core.exceptions import DataValidationException
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    summary: Dict[str, Any]
    
    def add_error(self, message: str):
        """添加错误"""
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str):
        """添加警告"""
        self.warnings.append(message)
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0


class DataValidator:
    """数据验证器
    
    提供全面的数据验证功能：
    - OHLCV数据验证
    - 数据完整性检查
    - 异常值检测
    - 数据质量评估
    """
    
    def __init__(self, strict_mode: bool = False):
        """
        初始化数据验证器
        
        Args:
            strict_mode: 严格模式，将警告视为错误
        """
        self.strict_mode = strict_mode
        
        logger.debug("数据验证器初始化完成")
    
    def validate_ohlcv(self, data: pd.DataFrame, symbol: str = "") -> ValidationResult:
        """
        验证OHLCV数据
        
        Args:
            data: OHLCV数据
            symbol: 股票代码
            
        Returns:
            验证结果
        """
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            summary={}
        )
        
        try:
            # 基本结构验证
            self._validate_basic_structure(data, result)
            
            # 列名验证
            self._validate_columns(data, result)
            
            # 数据类型验证
            self._validate_data_types(data, result)
            
            # 数值范围验证
            self._validate_value_ranges(data, result)
            
            # OHLC逻辑验证
            self._validate_ohlc_logic(data, result)
            
            # 缺失值检查
            self._check_missing_values(data, result)
            
            # 异常值检测
            self._detect_outliers(data, result)
            
            # 数据连续性检查
            self._check_data_continuity(data, result)
            
            # 生成摘要
            self._generate_summary(data, result, symbol)
            
            # 严格模式处理
            if self.strict_mode and result.has_warnings():
                for warning in result.warnings:
                    result.add_error(f"严格模式错误: {warning}")
            
            logger.debug(f"OHLCV数据验证完成: {symbol}, 有效: {result.is_valid}")
            
            return result
            
        except Exception as e:
            result.add_error(f"验证过程异常: {e}")
            logger.error(f"数据验证失败: {e}")
            return result
    
    def _validate_basic_structure(self, data: pd.DataFrame, result: ValidationResult):
        """验证基本结构"""
        if data is None:
            result.add_error("数据为None")
            return
        
        if not isinstance(data, pd.DataFrame):
            result.add_error("数据必须是pandas DataFrame")
            return
        
        if data.empty:
            result.add_error("数据为空")
            return
        
        if len(data) < 2:
            result.add_warning("数据行数过少（少于2行）")
    
    def _validate_columns(self, data: pd.DataFrame, result: ValidationResult):
        """验证列名"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            result.add_error(f"缺少必需列: {missing_columns}")
        
        # 检查额外列
        extra_columns = [col for col in data.columns if col not in required_columns]
        if extra_columns:
            result.add_warning(f"包含额外列: {extra_columns}")
    
    def _validate_data_types(self, data: pd.DataFrame, result: ValidationResult):
        """验证数据类型"""
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        
        for col in numeric_columns:
            if col in data.columns:
                if not pd.api.types.is_numeric_dtype(data[col]):
                    result.add_error(f"列 {col} 必须是数值类型")
                
                # 检查无穷值
                if np.isinf(data[col]).any():
                    result.add_error(f"列 {col} 包含无穷值")
    
    def _validate_value_ranges(self, data: pd.DataFrame, result: ValidationResult):
        """验证数值范围"""
        price_columns = ['open', 'high', 'low', 'close']
        
        for col in price_columns:
            if col in data.columns:
                # 检查负值
                if (data[col] <= 0).any():
                    result.add_error(f"列 {col} 包含非正值")
                
                # 检查异常大的值
                if (data[col] > 1e6).any():
                    result.add_warning(f"列 {col} 包含异常大的值")
        
        # 检查成交量
        if 'volume' in data.columns:
            if (data['volume'] < 0).any():
                result.add_error("成交量不能为负值")
    
    def _validate_ohlc_logic(self, data: pd.DataFrame, result: ValidationResult):
        """验证OHLC逻辑"""
        required_cols = ['open', 'high', 'low', 'close']
        if not all(col in data.columns for col in required_cols):
            return
        
        # 检查 high >= max(open, close) 和 low <= min(open, close)
        high_valid = data['high'] >= data[['open', 'close']].max(axis=1)
        low_valid = data['low'] <= data[['open', 'close']].min(axis=1)
        
        if not high_valid.all():
            invalid_count = (~high_valid).sum()
            result.add_error(f"有 {invalid_count} 行的最高价小于开盘价或收盘价")
        
        if not low_valid.all():
            invalid_count = (~low_valid).sum()
            result.add_error(f"有 {invalid_count} 行的最低价大于开盘价或收盘价")
        
        # 检查价格跳跃
        price_changes = data['close'].pct_change().abs()
        extreme_changes = price_changes > 0.5  # 50%的价格变化
        
        if extreme_changes.any():
            extreme_count = extreme_changes.sum()
            result.add_warning(f"有 {extreme_count} 行存在极端价格变化（>50%）")
    
    def _check_missing_values(self, data: pd.DataFrame, result: ValidationResult):
        """检查缺失值"""
        missing_counts = data.isnull().sum()
        
        for col, count in missing_counts.items():
            if count > 0:
                ratio = count / len(data)
                if ratio > 0.1:  # 超过10%
                    result.add_error(f"列 {col} 缺失值过多: {count}个 ({ratio:.1%})")
                else:
                    result.add_warning(f"列 {col} 有 {count} 个缺失值")
    
    def _detect_outliers(self, data: pd.DataFrame, result: ValidationResult):
        """检测异常值"""
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        
        for col in numeric_columns:
            if col not in data.columns:
                continue
            
            # 使用IQR方法检测异常值
            Q1 = data[col].quantile(0.25)
            Q3 = data[col].quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = (data[col] < lower_bound) | (data[col] > upper_bound)
            outlier_count = outliers.sum()
            
            if outlier_count > 0:
                outlier_ratio = outlier_count / len(data)
                if outlier_ratio > 0.05:  # 超过5%
                    result.add_warning(f"列 {col} 异常值较多: {outlier_count}个 ({outlier_ratio:.1%})")
                else:
                    result.add_warning(f"列 {col} 有 {outlier_count} 个异常值")
    
    def _check_data_continuity(self, data: pd.DataFrame, result: ValidationResult):
        """检查数据连续性"""
        if not isinstance(data.index, pd.DatetimeIndex):
            result.add_warning("索引不是日期时间类型，无法检查连续性")
            return
        
        # 检查日期排序
        if not data.index.is_monotonic_increasing:
            result.add_error("日期索引未按升序排列")
        
        # 检查重复日期
        duplicates = data.index.duplicated()
        if duplicates.any():
            dup_count = duplicates.sum()
            result.add_error(f"存在 {dup_count} 个重复日期")
        
        # 检查日期间隔
        if len(data) > 1:
            date_diffs = data.index.to_series().diff().dropna()
            
            # 检查是否有异常大的间隔（超过7天）
            large_gaps = date_diffs > pd.Timedelta(days=7)
            if large_gaps.any():
                gap_count = large_gaps.sum()
                result.add_warning(f"存在 {gap_count} 个大的日期间隔（>7天）")
    
    def _generate_summary(self, data: pd.DataFrame, result: ValidationResult, symbol: str):
        """生成验证摘要"""
        summary = {
            'symbol': symbol,
            'total_rows': len(data),
            'date_range': {
                'start': data.index[0] if len(data) > 0 else None,
                'end': data.index[-1] if len(data) > 0 else None
            },
            'columns': list(data.columns),
            'missing_values': data.isnull().sum().to_dict(),
            'data_types': data.dtypes.astype(str).to_dict()
        }
        
        # 价格统计
        if 'close' in data.columns:
            summary['price_stats'] = {
                'min': float(data['close'].min()),
                'max': float(data['close'].max()),
                'mean': float(data['close'].mean()),
                'std': float(data['close'].std())
            }
        
        # 成交量统计
        if 'volume' in data.columns:
            summary['volume_stats'] = {
                'min': float(data['volume'].min()),
                'max': float(data['volume'].max()),
                'mean': float(data['volume'].mean()),
                'total': float(data['volume'].sum())
            }
        
        result.summary = summary


def validate_ohlcv(data: pd.DataFrame, symbol: str = "", strict: bool = False) -> ValidationResult:
    """
    验证OHLCV数据的便捷函数
    
    Args:
        data: OHLCV数据
        symbol: 股票代码
        strict: 严格模式
        
    Returns:
        验证结果
    """
    validator = DataValidator(strict_mode=strict)
    return validator.validate_ohlcv(data, symbol)


def validate_price_data(
    data: pd.DataFrame,
    required_columns: Optional[List[str]] = None,
    allow_missing: float = 0.05
) -> ValidationResult:
    """
    验证价格数据的便捷函数
    
    Args:
        data: 价格数据
        required_columns: 必需列
        allow_missing: 允许的缺失值比例
        
    Returns:
        验证结果
    """
    if required_columns is None:
        required_columns = ['close']
    
    result = ValidationResult(True, [], [], {})
    
    # 检查必需列
    missing_cols = [col for col in required_columns if col not in data.columns]
    if missing_cols:
        result.add_error(f"缺少必需列: {missing_cols}")
    
    # 检查缺失值
    for col in required_columns:
        if col in data.columns:
            missing_ratio = data[col].isnull().sum() / len(data)
            if missing_ratio > allow_missing:
                result.add_error(f"列 {col} 缺失值比例过高: {missing_ratio:.1%}")
    
    return result
