"""
回撤风险规则模块

定义与回撤相关的风险控制规则。
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from .base import BaseRiskRule, RiskRuleResult, RiskLevel
from core.structures import Order, Position, Portfolio


class DrawdownRule(BaseRiskRule):
    """回撤规则
    
    控制投资组合的最大回撤。
    """
    
    def __init__(self, max_drawdown: float = 0.2, **kwargs):
        """
        初始化回撤规则
        
        Args:
            max_drawdown: 最大回撤比例
        """
        super().__init__("drawdown", max_drawdown=max_drawdown, **kwargs)
        self.max_drawdown = max_drawdown
    
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskRuleResult:
        """检查订单对回撤的影响"""
        return RiskRuleResult.pass_result(self.name, "订单检查不适用于回撤规则")
    
    def check_position(self, position: Position, portfolio: Portfolio) -> RiskRuleResult:
        """检查持仓对回撤的影响"""
        return RiskRuleResult.pass_result(self.name, "单个持仓检查不适用于回撤规则")
    
    def check_portfolio(self, portfolio: Portfolio) -> RiskRuleResult:
        """检查投资组合回撤"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        if len(portfolio.value_history) < 2:
            return RiskRuleResult.pass_result(self.name, "历史数据不足")
        
        # 计算当前回撤
        peak = portfolio.value_history.cummax()
        current_value = portfolio.value_history.iloc[-1]
        current_peak = peak.iloc[-1]
        
        current_drawdown = (current_peak - current_value) / current_peak
        
        if current_drawdown > self.max_drawdown:
            return RiskRuleResult.fail_result(
                self.name,
                f"当前回撤({current_drawdown:.2%})超过限制({self.max_drawdown:.2%})",
                RiskLevel.HIGH,
                {
                    'current_drawdown': current_drawdown,
                    'max_drawdown': self.max_drawdown,
                    'current_value': current_value,
                    'peak_value': current_peak
                }
            )
        
        return RiskRuleResult.pass_result(self.name, f"回撤({current_drawdown:.2%})在限制内")


class DailyLossRule(BaseRiskRule):
    """日损失规则
    
    控制单日最大损失。
    """
    
    def __init__(self, max_daily_loss: float = 0.05, **kwargs):
        """
        初始化日损失规则
        
        Args:
            max_daily_loss: 最大日损失比例
        """
        super().__init__("daily_loss", max_daily_loss=max_daily_loss, **kwargs)
        self.max_daily_loss = max_daily_loss
    
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskRuleResult:
        """检查订单对日损失的影响"""
        return RiskRuleResult.pass_result(self.name, "订单检查不适用于日损失规则")
    
    def check_position(self, position: Position, portfolio: Portfolio) -> RiskRuleResult:
        """检查持仓对日损失的影响"""
        return RiskRuleResult.pass_result(self.name, "单个持仓检查不适用于日损失规则")
    
    def check_portfolio(self, portfolio: Portfolio) -> RiskRuleResult:
        """检查投资组合日损失"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        if len(portfolio.value_history) < 2:
            return RiskRuleResult.pass_result(self.name, "历史数据不足")
        
        # 计算今日收益率
        current_value = portfolio.value_history.iloc[-1]
        previous_value = portfolio.value_history.iloc[-2]
        
        daily_return = (current_value - previous_value) / previous_value
        
        if daily_return < -self.max_daily_loss:
            return RiskRuleResult.fail_result(
                self.name,
                f"日损失({abs(daily_return):.2%})超过限制({self.max_daily_loss:.2%})",
                RiskLevel.HIGH,
                {
                    'daily_return': daily_return,
                    'max_daily_loss': self.max_daily_loss,
                    'current_value': current_value,
                    'previous_value': previous_value
                }
            )
        
        return RiskRuleResult.pass_result(self.name, f"日损失({abs(daily_return):.2%})在限制内")
