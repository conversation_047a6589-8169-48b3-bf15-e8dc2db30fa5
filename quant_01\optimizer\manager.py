"""
优化管理器

统一管理和调度各种优化器，提供简化的优化接口。
"""

from typing import Dict, List, Any, Optional, Union, Type
from datetime import datetime
import pandas as pd

from .base import BaseOptimizer, OptimizationResult
from .grid_search import GridSearchOptimizer
from .random_search import RandomSearchOptimizer
from .bayesian import BayesianOptimizer
from .genetic import GeneticOptimizer
from .multi_objective import MultiObjectiveOptimizer
from ..utils.logger import get_logger

logger = get_logger(__name__)


class OptimizationManager:
    """优化管理器"""
    
    def __init__(self):
        """初始化优化管理器"""
        self.optimizers = {
            'grid': GridSearchOptimizer,
            'random': RandomSearchOptimizer,
            'bayesian': BayesianOptimizer,
            'genetic': GeneticOptimizer,
            'multi_objective': MultiObjectiveOptimizer
        }
        
        self.optimization_history = []
        
        logger.info("优化管理器初始化完成")
    
    def optimize(
        self,
        strategy_class: type,
        param_space: Dict[str, Union[List[Any], Dict[str, Any]]],
        data: pd.DataFrame,
        method: str = 'grid',
        objective: str = 'sharpe_ratio',
        direction: str = 'maximize',
        validation_split: float = 0.0,
        n_trials: int = 100,
        **kwargs
    ) -> OptimizationResult:
        """
        执行参数优化
        
        Args:
            strategy_class: 策略类
            param_space: 参数空间
            data: 数据
            method: 优化方法
            objective: 优化目标
            direction: 优化方向
            validation_split: 验证集比例
            n_trials: 试验次数
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        logger.info(f"开始参数优化: 方法={method}, 目标={objective}")
        
        # 数据分割
        validation_data = None
        if validation_split > 0:
            split_idx = int(len(data) * (1 - validation_split))
            train_data = data.iloc[:split_idx]
            validation_data = data.iloc[split_idx:]
        else:
            train_data = data
        
        # 创建优化器
        optimizer = self._create_optimizer(
            method=method,
            strategy_class=strategy_class,
            param_space=param_space,
            objective=objective,
            direction=direction,
            **kwargs
        )
        
        # 执行优化
        start_time = datetime.now()
        
        if method == 'grid':
            result = optimizer.optimize(
                data=train_data,
                validation_data=validation_data,
                **kwargs
            )
        elif method == 'random':
            result = optimizer.optimize(
                data=train_data,
                validation_data=validation_data,
                n_trials=n_trials,
                **kwargs
            )
        elif method == 'bayesian':
            result = optimizer.optimize(
                data=train_data,
                validation_data=validation_data,
                n_trials=n_trials,
                **kwargs
            )
        elif method == 'genetic':
            result = optimizer.optimize(
                data=train_data,
                validation_data=validation_data,
                **kwargs
            )
        elif method == 'multi_objective':
            result = optimizer.optimize(
                data=train_data,
                validation_data=validation_data,
                **kwargs
            )
        else:
            raise ValueError(f"不支持的优化方法: {method}")
        
        # 记录优化历史
        optimization_record = {
            'timestamp': start_time,
            'method': method,
            'strategy_class': strategy_class.__name__,
            'objective': objective,
            'direction': direction,
            'n_trials': result.n_trials,
            'best_score': result.best_score,
            'optimization_time': result.optimization_time,
            'best_params': result.best_params
        }
        
        self.optimization_history.append(optimization_record)
        
        logger.info(f"参数优化完成: 最佳{objective} = {result.best_score:.4f}")
        
        return result
    
    def _create_optimizer(
        self,
        method: str,
        strategy_class: type,
        param_space: Dict[str, Any],
        objective: str,
        direction: str,
        **kwargs
    ) -> BaseOptimizer:
        """创建优化器"""
        if method not in self.optimizers:
            raise ValueError(f"不支持的优化方法: {method}")
        
        optimizer_class = self.optimizers[method]
        
        # 提取优化器特定参数
        optimizer_kwargs = {}
        
        if method == 'grid':
            optimizer_kwargs.update({
                'max_combinations': kwargs.get('max_combinations', 10000),
                'n_jobs': kwargs.get('n_jobs', 1),
                'random_state': kwargs.get('random_state', None)
            })
        
        elif method == 'random':
            optimizer_kwargs.update({
                'n_jobs': kwargs.get('n_jobs', 1),
                'random_state': kwargs.get('random_state', None)
            })
        
        elif method == 'bayesian':
            optimizer_kwargs.update({
                'n_jobs': kwargs.get('n_jobs', 1),
                'random_state': kwargs.get('random_state', None),
                'acquisition_function': kwargs.get('acquisition_function', 'EI')
            })
        
        elif method == 'genetic':
            optimizer_kwargs.update({
                'n_jobs': kwargs.get('n_jobs', 1),
                'random_state': kwargs.get('random_state', None),
                'population_size': kwargs.get('population_size', 50),
                'mutation_rate': kwargs.get('mutation_rate', 0.1),
                'crossover_rate': kwargs.get('crossover_rate', 0.8),
                'elite_size': kwargs.get('elite_size', 5)
            })
        
        elif method == 'multi_objective':
            objectives = kwargs.get('objectives', [objective])
            directions = kwargs.get('directions', {obj: direction for obj in objectives})
            
            return MultiObjectiveOptimizer(
                strategy_class=strategy_class,
                param_space=param_space,
                objectives=objectives,
                directions=directions,
                n_jobs=kwargs.get('n_jobs', 1),
                random_state=kwargs.get('random_state', None),
                population_size=kwargs.get('population_size', 100)
            )
        
        return optimizer_class(
            strategy_class=strategy_class,
            param_space=param_space,
            objective=objective,
            direction=direction,
            **optimizer_kwargs
        )
    
    def compare_methods(
        self,
        strategy_class: type,
        param_space: Dict[str, Any],
        data: pd.DataFrame,
        methods: List[str] = None,
        objective: str = 'sharpe_ratio',
        direction: str = 'maximize',
        n_trials: int = 50,
        **kwargs
    ) -> Dict[str, OptimizationResult]:
        """
        比较不同优化方法
        
        Args:
            strategy_class: 策略类
            param_space: 参数空间
            data: 数据
            methods: 优化方法列表
            objective: 优化目标
            direction: 优化方向
            n_trials: 试验次数
            **kwargs: 其他参数
            
        Returns:
            各方法的优化结果
        """
        if methods is None:
            methods = ['grid', 'random', 'bayesian']
        
        logger.info(f"开始比较优化方法: {methods}")
        
        results = {}
        
        for method in methods:
            try:
                logger.info(f"运行 {method} 优化...")
                
                result = self.optimize(
                    strategy_class=strategy_class,
                    param_space=param_space,
                    data=data,
                    method=method,
                    objective=objective,
                    direction=direction,
                    n_trials=n_trials,
                    **kwargs
                )
                
                results[method] = result
                
            except Exception as e:
                logger.error(f"{method} 优化失败: {e}")
                continue
        
        # 输出比较结果
        self._print_comparison_results(results, objective)
        
        return results
    
    def _print_comparison_results(self, results: Dict[str, OptimizationResult], objective: str):
        """打印比较结果"""
        if not results:
            logger.warning("没有有效的优化结果")
            return
        
        logger.info("=" * 60)
        logger.info("优化方法比较结果")
        logger.info("=" * 60)
        
        # 按最佳分数排序
        sorted_results = sorted(
            results.items(),
            key=lambda x: x[1].best_score,
            reverse=(results[list(results.keys())[0]].direction == 'maximize')
        )
        
        for method, result in sorted_results:
            logger.info(f"{method:>12}: {objective} = {result.best_score:.4f}, "
                       f"时间 = {result.optimization_time:.2f}s, "
                       f"试验 = {result.n_trials}")
        
        logger.info("=" * 60)
    
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """获取优化历史"""
        return self.optimization_history.copy()
    
    def clear_history(self):
        """清空优化历史"""
        self.optimization_history.clear()
        logger.info("优化历史已清空")
    
    def get_available_methods(self) -> List[str]:
        """获取可用的优化方法"""
        return list(self.optimizers.keys())
    
    def register_optimizer(self, name: str, optimizer_class: Type[BaseOptimizer]):
        """注册新的优化器"""
        self.optimizers[name] = optimizer_class
        logger.info(f"优化器已注册: {name}")
    
    def get_method_info(self, method: str) -> Dict[str, Any]:
        """获取优化方法信息"""
        if method not in self.optimizers:
            raise ValueError(f"未知的优化方法: {method}")
        
        optimizer_class = self.optimizers[method]
        
        info = {
            'name': method,
            'class': optimizer_class.__name__,
            'description': optimizer_class.__doc__ or "无描述",
            'suitable_for': self._get_method_suitability(method)
        }
        
        return info
    
    def _get_method_suitability(self, method: str) -> str:
        """获取方法适用性描述"""
        suitability = {
            'grid': "小参数空间，需要全面搜索",
            'random': "大参数空间，快速探索",
            'bayesian': "昂贵的目标函数，智能搜索",
            'genetic': "复杂非线性问题，全局优化",
            'multi_objective': "多目标优化，帕累托前沿"
        }
        
        return suitability.get(method, "通用优化")


# 创建全局优化管理器实例
optimization_manager = OptimizationManager()
