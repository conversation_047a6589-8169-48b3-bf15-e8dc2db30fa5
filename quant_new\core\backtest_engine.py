from typing import List, Dict
from datetime import datetime
from .data_structures import OHLCV, Order, Position, Account

class BacktestEngine:
    """量化回测引擎核心类"""
    
    def __init__(self):
        self.data_feed = None
        self.strategy = None
        self.account = Account(balance=1000000, positions={}, orders={})
        self.current_time = None
        self.historical_data = {}
        
    def load_data(self, symbol: str, data: List[OHLCV]):
        """加载历史数据"""
        self.historical_data[symbol] = data
        
    def set_strategy(self, strategy):
        """设置交易策略"""
        self.strategy = strategy
        
    def run_backtest(self, start_date: datetime, end_date: datetime):
        """执行回测"""
        if not self.strategy or not self.historical_data:
            raise ValueError("Strategy and data must be set before backtest")
            
        print(f"Running backtest from {start_date} to {end_date}")
        self.strategy.initialize()
        
        # 按时间顺序处理数据
        for symbol, data in self.historical_data.items():
            for bar in sorted(data, key=lambda x: x.timestamp):
                if start_date <= bar.timestamp <= end_date:
                    self.current_time = bar.timestamp
                    orders = self.strategy.on_bar(bar, self.account)
                    for order in orders:
                        self.execute_order(order)
        
        print("Backtest completed")
        return self._generate_report()
        
    def execute_order(self, order: Order):
        """执行订单"""
        print(f"Executing order: {order}")
        # 简化版订单执行逻辑
        if order.symbol not in self.account.positions:
            self.account.positions[order.symbol] = Position(
                symbol=order.symbol,
                quantity=0,
                avg_price=0
            )
            
        position = self.account.positions[order.symbol]
        if order.direction == 'BUY':
            position.quantity += order.quantity
            position.avg_price = (
                (position.avg_price * (position.quantity - order.quantity) +
                order.price * order.quantity)
            ) / position.quantity
        else:
            position.quantity -= order.quantity
            
        self.account.balance -= order.price * order.quantity * (
            1 if order.direction == 'BUY' else -1)
        
    def get_account_status(self) -> Account:
        """获取当前账户状态"""
        return self.account
        
    def _generate_report(self) -> dict:
        """生成回测报告"""
        return {
            "start_balance": 1000000,
            "end_balance": self.account.balance,
            "total_return": (self.account.balance - 1000000) / 1000000,
            "positions": self.account.positions,
            "max_drawdown": self._calculate_max_drawdown(),
            "sharpe_ratio": self._calculate_sharpe_ratio()
        }
        
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        # TODO: 实现最大回撤计算
        return 0.0
        
    def _calculate_sharpe_ratio(self) -> float:
        """计算夏普比率"""
        # TODO: 实现夏普比率计算
        return 0.0;