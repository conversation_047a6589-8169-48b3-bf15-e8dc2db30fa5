"""
技术指标基类

定义技术指标的统一接口和基础功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, List
import pandas as pd
import numpy as np
from datetime import datetime
import time

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import get_logger

logger = get_logger(__name__)


class BaseIndicator(ABC):
    """技术指标基类

    所有技术指标都应该继承此类。

    优化特性：
    - 向量化计算
    - 结果缓存
    - 参数验证
    - 性能监控
    - 批量计算支持
    """

    def __init__(
        self,
        name: str,
        parameters: Optional[Dict[str, Any]] = None,
        cache_enabled: bool = True
    ):
        """
        初始化技术指标

        Args:
            name: 指标名称
            parameters: 指标参数
            cache_enabled: 是否启用缓存
        """
        self.name = name
        self.parameters = parameters or {}
        self.cache_enabled = cache_enabled

        # 缓存
        self._cache: Dict[str, Any] = {}

        # 统计信息
        self._stats = {
            'calculations': 0,
            'cache_hits': 0,
            'total_time': 0.0,
            'last_calculation': None
        }

        # 验证参数
        self._validate_parameters()

        logger.debug(f"技术指标初始化: {self.name}")

    @abstractmethod
    def _calculate(self, data: Union[pd.Series, pd.DataFrame]) -> Union[pd.Series, Dict[str, pd.Series]]:
        """
        计算指标的具体实现

        Args:
            data: 输入数据

        Returns:
            计算结果
        """
        pass

    def calculate(
        self,
        data: Union[pd.Series, pd.DataFrame],
        use_cache: bool = True
    ) -> Union[pd.Series, Dict[str, pd.Series]]:
        """
        计算技术指标

        Args:
            data: 输入数据
            use_cache: 是否使用缓存

        Returns:
            指标计算结果
        """
        start_time = time.time()
        self._stats['calculations'] += 1

        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(data)

            # 检查缓存
            if use_cache and self.cache_enabled and cache_key in self._cache:
                self._stats['cache_hits'] += 1
                logger.debug(f"指标缓存命中: {self.name}")
                return self._cache[cache_key]

            # 验证输入数据
            self._validate_data(data)

            # 计算指标
            result = self._calculate(data)

            # 更新缓存
            if self.cache_enabled:
                self._cache[cache_key] = result

            # 更新统计
            calculation_time = time.time() - start_time
            self._stats['total_time'] += calculation_time
            self._stats['last_calculation'] = datetime.now()

            logger.debug(f"指标计算完成: {self.name}, 耗时: {calculation_time:.4f}秒")

            return result

        except Exception as e:
            logger.error(f"指标计算失败: {self.name}, 错误: {e}")
            raise

    def batch_calculate(
        self,
        data_dict: Dict[str, Union[pd.Series, pd.DataFrame]],
        use_cache: bool = True
    ) -> Dict[str, Union[pd.Series, Dict[str, pd.Series]]]:
        """
        批量计算指标

        Args:
            data_dict: 数据字典，键为标识符，值为数据
            use_cache: 是否使用缓存

        Returns:
            计算结果字典
        """
        results = {}

        for key, data in data_dict.items():
            try:
                results[key] = self.calculate(data, use_cache)
            except Exception as e:
                logger.error(f"批量计算失败: {key}, 错误: {e}")
                results[key] = None

        return results

    def _validate_parameters(self):
        """验证参数"""
        # 子类可以重写此方法来验证特定参数
        pass

    def _validate_data(self, data: Union[pd.Series, pd.DataFrame]):
        """验证输入数据"""
        if isinstance(data, pd.Series):
            if data.empty:
                raise ValueError("输入数据为空")
            if not pd.api.types.is_numeric_dtype(data):
                raise ValueError("输入数据必须是数值类型")
        elif isinstance(data, pd.DataFrame):
            if data.empty:
                raise ValueError("输入数据为空")
            # 检查必要的列
            required_columns = self._get_required_columns()
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                raise ValueError(f"缺少必要列: {missing_columns}")
        else:
            raise ValueError("输入数据必须是pandas Series或DataFrame")

    def _get_required_columns(self) -> List[str]:
        """获取必要的列名"""
        # 子类可以重写此方法来指定必要的列
        return []

    def _generate_cache_key(self, data: Union[pd.Series, pd.DataFrame]) -> str:
        """生成缓存键"""
        # 基于数据哈希和参数生成缓存键
        if isinstance(data, pd.Series):
            data_hash = hash(tuple(data.values))
        else:
            data_hash = hash(tuple(data.values.flatten()))

        params_str = str(sorted(self.parameters.items()))

        return f"{self.name}_{data_hash}_{hash(params_str)}"

    def get_parameter(self, name: str, default: Any = None) -> Any:
        """获取参数值"""
        return self.parameters.get(name, default)

    def set_parameter(self, name: str, value: Any):
        """设置参数值"""
        self.parameters[name] = value
        # 清空缓存，因为参数改变了
        self.clear_cache()

    def update_parameters(self, parameters: Dict[str, Any]):
        """更新参数"""
        self.parameters.update(parameters)
        self._validate_parameters()
        self.clear_cache()

    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        logger.debug(f"指标缓存已清空: {self.name}")

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self._cache),
            'cache_enabled': self.cache_enabled,
        }

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self._stats.copy()
        if stats['calculations'] > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['calculations']
            stats['avg_time'] = stats['total_time'] / stats['calculations']
        else:
            stats['cache_hit_rate'] = 0.0
            stats['avg_time'] = 0.0

        return stats

    def get_info(self) -> Dict[str, Any]:
        """获取指标信息"""
        return {
            'name': self.name,
            'parameters': self.parameters,
            'cache_enabled': self.cache_enabled,
            'stats': self.get_stats(),
            'cache_info': self.get_cache_info(),
        }

    def __str__(self) -> str:
        return f"{self.name}({self.parameters})"

    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}: {self.name}>"


class MovingAverageBase(BaseIndicator):
    """移动平均指标基类"""

    def __init__(self, period: int, **kwargs):
        """
        初始化移动平均指标

        Args:
            period: 周期
            **kwargs: 其他参数
        """
        parameters = {'period': period}
        parameters.update(kwargs)

        super().__init__(self.__class__.__name__, parameters)
        self.period = period

    def _validate_parameters(self):
        """验证参数"""
        if self.period <= 0:
            raise ValueError("周期必须大于0")
        if not isinstance(self.period, int):
            raise ValueError("周期必须是整数")


class OscillatorBase(BaseIndicator):
    """震荡指标基类"""

    def __init__(self, period: int, **kwargs):
        """
        初始化震荡指标

        Args:
            period: 周期
            **kwargs: 其他参数
        """
        parameters = {'period': period}
        parameters.update(kwargs)

        super().__init__(self.__class__.__name__, parameters)
        self.period = period

    def _validate_parameters(self):
        """验证参数"""
        if self.period <= 0:
            raise ValueError("周期必须大于0")
        if not isinstance(self.period, int):
            raise ValueError("周期必须是整数")


class VolumeIndicatorBase(BaseIndicator):
    """成交量指标基类"""

    def _get_required_columns(self) -> List[str]:
        """获取必要的列名"""
        return ['close', 'volume']


class PriceIndicatorBase(BaseIndicator):
    """价格指标基类"""

    def _get_required_columns(self) -> List[str]:
        """获取必要的列名"""
        return ['open', 'high', 'low', 'close']
