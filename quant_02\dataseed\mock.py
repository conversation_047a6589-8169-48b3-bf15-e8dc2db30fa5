"""
模拟数据源模块

用于测试和开发的模拟数据源，生成符合真实市场特征的随机数据。
"""

from typing import List, Dict, Any, Union, Optional
from datetime import datetime, date, timedelta
import pandas as pd
import numpy as np

from .base import DataSource


class MockDataSource(DataSource):
    """模拟数据源
    
    生成符合真实市场特征的模拟数据，用于测试和开发。
    
    特性：
    - 可配置的价格波动率
    - 多种市场状态模拟
    - 真实的价格走势特征
    - 可重现的随机数据
    """
    
    def __init__(
        self,
        name: str = "mock",
        base_price: float = 100.0,
        volatility: float = 0.02,
        trend: float = 0.0001,
        volume_base: int = 1000000,
        volume_volatility: float = 0.3,
        market_state: str = "normal",
        seed: Optional[int] = None,
        **kwargs
    ):
        """
        初始化Mock数据源
        
        Args:
            name: 数据源名称
            base_price: 基础价格
            volatility: 价格波动率
            trend: 价格趋势（日收益率）
            volume_base: 基础成交量
            volume_volatility: 成交量波动率
            market_state: 市场状态 (normal, bull, bear, volatile)
            seed: 随机种子
            **kwargs: 其他参数
        """
        super().__init__(name, **kwargs)
        
        self.base_price = base_price
        self.volatility = volatility
        self.trend = trend
        self.volume_base = volume_base
        self.volume_volatility = volume_volatility
        self.market_state = market_state
        
        # 设置随机种子
        if seed is not None:
            np.random.seed(seed)
        
        # 根据市场状态调整参数
        self._adjust_market_params()
        
        # 生成股票代码列表
        self._symbol_list = self._generate_symbol_list()
        
        self.logger.info(f"Mock数据源初始化完成，市场状态: {market_state}")
    
    def _adjust_market_params(self):
        """根据市场状态调整参数"""
        if self.market_state == "bull":
            self.trend = 0.001  # 牛市上涨趋势
            self.volatility *= 0.8  # 降低波动率
        elif self.market_state == "bear":
            self.trend = -0.001  # 熊市下跌趋势
            self.volatility *= 1.2  # 增加波动率
        elif self.market_state == "volatile":
            self.trend = 0.0  # 无明显趋势
            self.volatility *= 2.0  # 大幅增加波动率
        # normal 状态保持默认参数
    
    def _generate_symbol_list(self) -> List[str]:
        """生成股票代码列表"""
        symbols = []
        
        # A股代码
        for i in range(1, 1000):
            symbols.append(f"{i:06d}")
        
        # 指数代码
        indices = ["000001", "000300", "399001", "399006"]
        symbols.extend(indices)
        
        return symbols
    
    def _fetch_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """
        生成模拟数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            **kwargs: 其他参数
            
        Returns:
            模拟的OHLCV数据
        """
        # 转换日期
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        # 生成日期范围
        if frequency == "1d":
            date_range = pd.date_range(start=start_dt, end=end_dt, freq='D')
            # 过滤掉周末
            date_range = date_range[date_range.weekday < 5]
        elif frequency == "1h":
            date_range = pd.date_range(start=start_dt, end=end_dt, freq='H')
        elif frequency == "1m":
            date_range = pd.date_range(start=start_dt, end=end_dt, freq='T')
        else:
            raise ValueError(f"不支持的频率: {frequency}")
        
        if len(date_range) == 0:
            return pd.DataFrame()
        
        # 生成价格数据
        prices = self._generate_price_series(symbol, len(date_range))
        volumes = self._generate_volume_series(len(date_range))
        
        # 构建DataFrame
        data = pd.DataFrame({
            'open': prices['open'],
            'high': prices['high'],
            'low': prices['low'],
            'close': prices['close'],
            'volume': volumes,
        }, index=date_range)
        
        # 添加扩展字段
        data['amount'] = data['volume'] * (data['high'] + data['low'] + data['close']) / 3
        data['vwap'] = data['amount'] / data['volume']
        data['adj_close'] = data['close']  # 简化处理，不考虑复权
        
        return data
    
    def _generate_price_series(self, symbol: str, length: int) -> Dict[str, np.ndarray]:
        """生成价格序列"""
        # 使用股票代码作为随机种子的一部分
        symbol_seed = hash(symbol) % 10000
        np.random.seed(symbol_seed)
        
        # 生成收盘价序列
        returns = np.random.normal(self.trend, self.volatility, length)
        
        # 添加一些自相关性，使价格走势更真实
        for i in range(1, length):
            returns[i] += 0.1 * returns[i-1]
        
        # 计算价格序列
        prices = [self.base_price]
        for i in range(1, length):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(max(new_price, 0.01))  # 确保价格为正
        
        close_prices = np.array(prices)
        
        # 生成开盘价（基于前一日收盘价加上小幅波动）
        open_prices = np.zeros(length)
        open_prices[0] = close_prices[0]
        for i in range(1, length):
            gap = np.random.normal(0, self.volatility * 0.5)
            open_prices[i] = close_prices[i-1] * (1 + gap)
        
        # 生成最高价和最低价
        high_prices = np.zeros(length)
        low_prices = np.zeros(length)
        
        for i in range(length):
            # 日内波动
            intraday_volatility = self.volatility * 0.5
            high_factor = 1 + abs(np.random.normal(0, intraday_volatility))
            low_factor = 1 - abs(np.random.normal(0, intraday_volatility))
            
            # 确保价格逻辑正确
            base_price = max(open_prices[i], close_prices[i])
            high_prices[i] = base_price * high_factor
            
            base_price = min(open_prices[i], close_prices[i])
            low_prices[i] = base_price * low_factor
            
            # 确保 high >= max(open, close) 和 low <= min(open, close)
            high_prices[i] = max(high_prices[i], open_prices[i], close_prices[i])
            low_prices[i] = min(low_prices[i], open_prices[i], close_prices[i])
        
        return {
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices
        }
    
    def _generate_volume_series(self, length: int) -> np.ndarray:
        """生成成交量序列"""
        # 生成基础成交量
        volumes = np.random.lognormal(
            mean=np.log(self.volume_base),
            sigma=self.volume_volatility,
            size=length
        )
        
        # 添加一些周期性特征（模拟交易活跃度的变化）
        for i in range(length):
            # 周内效应：周一和周五成交量通常较高
            day_of_week = i % 5
            if day_of_week in [0, 4]:  # 周一和周五
                volumes[i] *= 1.2
            elif day_of_week in [1, 3]:  # 周二和周四
                volumes[i] *= 0.9
        
        return volumes.astype(int)
    
    def get_available_symbols(self) -> List[str]:
        """获取可用标的列表"""
        return self._symbol_list.copy()
    
    def is_available(self, symbol: str) -> bool:
        """检查标的是否可用"""
        return symbol in self._symbol_list
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        if not self.is_available(symbol):
            return {'symbol': symbol, 'error': '股票代码不存在'}
        
        # 模拟股票信息
        symbol_hash = hash(symbol) % 1000
        
        # 生成模拟的股票信息
        info = {
            'symbol': symbol,
            'name': f'模拟股票{symbol}',
            'exchange': 'MOCK',
            'sector': ['科技', '金融', '消费', '医药', '工业'][symbol_hash % 5],
            'market_cap': (symbol_hash + 1) * 1000000000,  # 市值
            'pe_ratio': 10 + (symbol_hash % 50),  # 市盈率
            'pb_ratio': 1 + (symbol_hash % 10) * 0.5,  # 市净率
            'dividend_yield': (symbol_hash % 10) * 0.01,  # 股息率
            'beta': 0.5 + (symbol_hash % 20) * 0.1,  # Beta值
            'listing_date': '2010-01-01',  # 上市日期
            'description': f'这是一个模拟的股票代码 {symbol}，用于测试和演示。'
        }
        
        return info
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 生成一个简单的测试数据
            test_data = self._fetch_data("000001", "2023-01-01", "2023-01-02")
            return not test_data.empty
        except Exception:
            return False
    
    def set_market_state(self, state: str):
        """设置市场状态"""
        if state not in ["normal", "bull", "bear", "volatile"]:
            raise ValueError(f"不支持的市场状态: {state}")
        
        self.market_state = state
        self._adjust_market_params()
        self.logger.info(f"市场状态已更新为: {state}")
    
    def get_market_info(self) -> Dict[str, Any]:
        """获取市场信息"""
        return {
            'market_state': self.market_state,
            'base_price': self.base_price,
            'volatility': self.volatility,
            'trend': self.trend,
            'volume_base': self.volume_base,
            'available_symbols': len(self._symbol_list),
        }
