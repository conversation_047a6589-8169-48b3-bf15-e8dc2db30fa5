"""
配置管理模块

提供统一的配置管理功能，支持多种配置源和动态配置更新。
"""

from .base import BaseConfig, ConfigManager, config_manager
from .global_config import GlobalConfig, global_config
from .backtest import BacktestConfig
from .risk import RiskConfig

__all__ = [
    # 基础配置
    "BaseConfig",
    "ConfigManager",
    "config_manager",

    # 具体配置
    "GlobalConfig",
    "global_config",
    "BacktestConfig",
    "RiskConfig",
]
