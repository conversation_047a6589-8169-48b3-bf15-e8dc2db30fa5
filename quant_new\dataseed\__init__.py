"""
数据源模块

提供统一的数据访问接口，支持多种数据源：
- AkShare: A股数据
- 数据库: 本地数据存储
- Mock: 模拟数据（用于测试）
"""

from .base import DataSeed, DataAdapter
from .akshare import AkShareDataSeed
from .database import DatabaseDataSeed
from .mock import MockDataSeed
from .adapter import StandardDataAdapter

__all__ = [
    "DataSeed",
    "DataAdapter", 
    "AkShareDataSeed",
    "DatabaseDataSeed",
    "MockDataSeed",
    "StandardDataAdapter"
]
