"""
量化回测引擎基类

提供统一的回测引擎接口和核心功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd
import time

from ..config.global_config import GlobalConfig
from ..data_structures.market import OHLCV, MarketData
from ...dataseed.base import DataSource
from ...strategies.base.strategy import BaseStrategy, StrategyResult
from ...utils.logger import get_logger

logger = get_logger(__name__)


class QuantEngine:
    """量化回测引擎
    
    统一的回测引擎接口，支持单资产和多资产回测。
    
    优化特性：
    - 插件化架构
    - 高性能计算
    - 智能缓存
    - 并行处理
    - 完整监控
    """
    
    def __init__(
        self,
        data_source: Optional[DataSource] = None,
        config: Optional[GlobalConfig] = None
    ):
        """
        初始化量化引擎
        
        Args:
            data_source: 数据源
            config: 全局配置
        """
        self.config = config or GlobalConfig.create_default()
        self.data_source = data_source
        
        # 内部状态
        self._strategies: Dict[str, BaseStrategy] = {}
        self._results: Dict[str, StrategyResult] = {}
        self._market_data: Optional[MarketData] = None
        
        # 性能统计
        self._stats = {
            'total_backtests': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'last_run': None
        }
        
        logger.info("量化引擎初始化完成")
    
    def set_data_source(self, data_source: DataSource):
        """设置数据源"""
        self.data_source = data_source
        logger.info(f"数据源已设置: {data_source.name}")
    
    def register_strategy(self, strategy: BaseStrategy, name: Optional[str] = None):
        """注册策略"""
        strategy_name = name or strategy.name
        self._strategies[strategy_name] = strategy
        logger.info(f"策略已注册: {strategy_name}")
    
    def get_strategy(self, name: str) -> Optional[BaseStrategy]:
        """获取策略"""
        return self._strategies.get(name)
    
    def list_strategies(self) -> List[str]:
        """列出所有策略"""
        return list(self._strategies.keys())
    
    def run_strategy(
        self,
        strategy: Union[str, BaseStrategy],
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> StrategyResult:
        """
        运行单个策略回测
        
        Args:
            strategy: 策略名称或策略对象
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            **kwargs: 其他参数
            
        Returns:
            策略回测结果
        """
        start_time = time.time()
        
        try:
            # 获取策略对象
            if isinstance(strategy, str):
                strategy_obj = self.get_strategy(strategy)
                if strategy_obj is None:
                    raise ValueError(f"策略不存在: {strategy}")
            else:
                strategy_obj = strategy
            
            # 检查数据源
            if self.data_source is None:
                raise ValueError("未设置数据源")
            
            # 获取数据
            logger.info(f"获取数据: {symbol}, {start_date} 到 {end_date}")
            data = self.data_source.get_data(symbol, start_date, end_date, frequency, **kwargs)
            
            if data.empty:
                raise ValueError(f"无法获取数据: {symbol}")
            
            # 运行策略
            logger.info(f"运行策略: {strategy_obj.name}")
            result = strategy_obj.run(data)
            
            # 设置结果的基本信息
            result.symbol = symbol
            result.start_date = pd.to_datetime(start_date)
            result.end_date = pd.to_datetime(end_date)
            
            # 保存结果
            result_key = f"{strategy_obj.name}_{symbol}_{start_date}_{end_date}"
            self._results[result_key] = result
            
            # 更新统计
            execution_time = time.time() - start_time
            self._stats['total_backtests'] += 1
            self._stats['total_time'] += execution_time
            self._stats['avg_time'] = self._stats['total_time'] / self._stats['total_backtests']
            self._stats['last_run'] = datetime.now()
            
            logger.info(f"策略回测完成: {strategy_obj.name}, 耗时: {execution_time:.2f}秒")
            logger.info(f"总收益率: {result.total_return:.2%}, 夏普比率: {result.sharpe_ratio:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"策略回测失败: {e}")
            raise
    
    def run_batch_backtest(
        self,
        strategy: Union[str, BaseStrategy],
        symbols: List[str],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        parallel: bool = True,
        **kwargs
    ) -> Dict[str, StrategyResult]:
        """
        批量回测
        
        Args:
            strategy: 策略名称或策略对象
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            parallel: 是否并行处理
            **kwargs: 其他参数
            
        Returns:
            回测结果字典
        """
        logger.info(f"开始批量回测: {len(symbols)}个标的")
        
        results = {}
        
        if not parallel:
            # 串行处理
            for symbol in symbols:
                try:
                    result = self.run_strategy(
                        strategy, symbol, start_date, end_date, frequency, **kwargs
                    )
                    results[symbol] = result
                except Exception as e:
                    logger.error(f"回测失败: {symbol}, 错误: {e}")
        else:
            # 并行处理（简化实现）
            from concurrent.futures import ThreadPoolExecutor, as_completed
            
            max_workers = self.config.performance.max_workers
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交任务
                future_to_symbol = {
                    executor.submit(
                        self.run_strategy,
                        strategy, symbol, start_date, end_date, frequency, **kwargs
                    ): symbol
                    for symbol in symbols
                }
                
                # 收集结果
                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        result = future.result()
                        results[symbol] = result
                    except Exception as e:
                        logger.error(f"回测失败: {symbol}, 错误: {e}")
        
        logger.info(f"批量回测完成: 成功{len(results)}/{len(symbols)}个")
        
        return results
    
    def compare_strategies(
        self,
        strategies: List[Union[str, BaseStrategy]],
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> Dict[str, StrategyResult]:
        """
        策略对比
        
        Args:
            strategies: 策略列表
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            **kwargs: 其他参数
            
        Returns:
            策略对比结果
        """
        logger.info(f"开始策略对比: {len(strategies)}个策略")
        
        results = {}
        
        for strategy in strategies:
            try:
                result = self.run_strategy(
                    strategy, symbol, start_date, end_date, frequency, **kwargs
                )
                
                strategy_name = strategy if isinstance(strategy, str) else strategy.name
                results[strategy_name] = result
                
            except Exception as e:
                strategy_name = strategy if isinstance(strategy, str) else strategy.name
                logger.error(f"策略回测失败: {strategy_name}, 错误: {e}")
        
        logger.info(f"策略对比完成: 成功{len(results)}/{len(strategies)}个")
        
        return results
    
    def get_result(self, key: str) -> Optional[StrategyResult]:
        """获取回测结果"""
        return self._results.get(key)
    
    def list_results(self) -> List[str]:
        """列出所有结果"""
        return list(self._results.keys())
    
    def clear_results(self):
        """清空结果"""
        self._results.clear()
        logger.info("回测结果已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        stats = self._stats.copy()
        
        # 添加额外信息
        stats['registered_strategies'] = len(self._strategies)
        stats['cached_results'] = len(self._results)
        stats['data_source'] = self.data_source.name if self.data_source else None
        
        return stats
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息"""
        if self.data_source is None:
            return {'error': '未设置数据源'}
        
        try:
            return {
                'data_source': self.data_source.name,
                'available_symbols_count': len(self.data_source.get_available_symbols()),
                'cache_info': self.data_source.get_cache_info(),
                'stats': self.data_source.get_stats(),
            }
        except Exception as e:
            return {'error': str(e)}
    
    def optimize_strategy(
        self,
        strategy_class: type,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        param_space: Dict[str, List[Any]],
        objective: str = "sharpe_ratio",
        **kwargs
    ) -> Dict[str, Any]:
        """
        策略参数优化
        
        Args:
            strategy_class: 策略类
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            param_space: 参数空间
            objective: 优化目标
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        from ...optimizer.grid_search import GridSearchOptimizer
        
        # 创建优化器
        optimizer = GridSearchOptimizer(
            strategy_class=strategy_class,
            param_space=param_space,
            objective=objective
        )
        
        # 获取数据
        data = self.data_source.get_data(symbol, start_date, end_date, **kwargs)
        
        # 运行优化
        result = optimizer.optimize(data)
        
        return result
    
    def __str__(self) -> str:
        return f"QuantEngine(strategies={len(self._strategies)}, results={len(self._results)})"
    
    def __repr__(self) -> str:
        return f"<QuantEngine: {len(self._strategies)} strategies, {len(self._results)} results>"
