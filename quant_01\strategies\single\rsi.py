"""
RSI策略

基于RSI指标的均值回归策略。
"""

from typing import Dict, Any, Optional
import pandas as pd
import numpy as np
from pydantic import Field, validator

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from strategies.base.strategy import BaseStrategy, StrategyConfig
from indicators.momentum.rsi import RSI
from utils.logger import get_logger

logger = get_logger(__name__)


class RSIConfig(StrategyConfig):
    """RSI策略配置"""

    strategy_name: str = Field(default="RSI", description="策略名称")

    # RSI参数
    rsi_period: int = Field(default=14, description="RSI周期", ge=2, le=100)

    # 信号参数
    oversold_threshold: float = Field(default=30.0, description="超卖阈值", ge=0.0, le=50.0)
    overbought_threshold: float = Field(default=70.0, description="超买阈值", ge=50.0, le=100.0)

    # 过滤参数
    use_trend_filter: bool = Field(default=True, description="是否使用趋势过滤")
    trend_period: int = Field(default=50, description="趋势周期", ge=10, le=200)

    @validator('overbought_threshold')
    def validate_thresholds(cls, v, values):
        if 'oversold_threshold' in values and v <= values['oversold_threshold']:
            raise ValueError('超买阈值必须大于超卖阈值')
        return v


class RSIStrategy(BaseStrategy):
    """RSI策略

    策略逻辑：
    1. 计算RSI指标
    2. 当RSI < 超卖阈值时产生买入信号
    3. 当RSI > 超买阈值时产生卖出信号
    4. 可选的趋势过滤
    """

    def __init__(self, config: Optional[RSIConfig] = None):
        """
        初始化RSI策略

        Args:
            config: RSI策略配置
        """
        if config is None:
            config = RSIConfig()

        super().__init__(config)
        self.config: RSIConfig = config

        # 初始化RSI指标
        self.rsi_indicator = RSI(period=config.rsi_period)

        logger.info(f"RSI策略初始化完成: period={config.rsi_period}")

    def _precompute_indicators(self, data: pd.DataFrame):
        """预计算RSI指标"""
        # 计算RSI
        rsi_values = self.rsi_indicator.calculate(data['close'])
        self._indicators_cache['rsi'] = rsi_values

        # 计算趋势过滤（如果启用）
        if self.config.use_trend_filter:
            trend_ma = data['close'].rolling(window=self.config.trend_period).mean()
            self._indicators_cache['trend_ma'] = trend_ma

        logger.debug("RSI指标预计算完成")

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成RSI交易信号

        Args:
            data: 价格数据DataFrame

        Returns:
            信号DataFrame
        """
        # 获取预计算的指标
        rsi = self._indicators_cache['rsi']

        # 初始化信号DataFrame
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['strength'] = 0.0
        signals['price'] = data['close']
        signals['rsi'] = rsi

        # 生成基础信号
        oversold = rsi < self.config.oversold_threshold
        overbought = rsi > self.config.overbought_threshold

        signals.loc[oversold, 'signal'] = 1  # 买入信号
        signals.loc[overbought, 'signal'] = -1  # 卖出信号

        # 计算信号强度
        signals['strength'] = self._calculate_signal_strength(data, signals)

        # 应用趋势过滤
        if self.config.use_trend_filter:
            signals = self._apply_trend_filter(data, signals)

        # 统计信号
        buy_signals = (signals['signal'] == 1).sum()
        sell_signals = (signals['signal'] == -1).sum()

        logger.info(f"RSI信号生成完成: 买入信号={buy_signals}, 卖出信号={sell_signals}")

        return signals

    def _calculate_signal_strength(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.Series:
        """计算信号强度"""
        strength = pd.Series(0.0, index=data.index)
        rsi = signals['rsi']

        # 买入信号强度：RSI越低强度越高
        buy_mask = signals['signal'] == 1
        if buy_mask.any():
            buy_strength = (self.config.oversold_threshold - rsi[buy_mask]) / self.config.oversold_threshold
            strength.loc[buy_mask] = buy_strength.clip(0.1, 1.0)

        # 卖出信号强度：RSI越高强度越高
        sell_mask = signals['signal'] == -1
        if sell_mask.any():
            sell_strength = (rsi[sell_mask] - self.config.overbought_threshold) / (100 - self.config.overbought_threshold)
            strength.loc[sell_mask] = sell_strength.clip(0.1, 1.0)

        return strength

    def _apply_trend_filter(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.DataFrame:
        """应用趋势过滤"""
        if 'trend_ma' not in self._indicators_cache:
            return signals

        filtered_signals = signals.copy()
        trend_ma = self._indicators_cache['trend_ma']

        # 只在价格高于趋势线时买入
        buy_mask = (signals['signal'] == 1) & (data['close'] <= trend_ma)
        filtered_signals.loc[buy_mask, 'signal'] = 0
        filtered_signals.loc[buy_mask, 'strength'] = 0.0

        # 只在价格低于趋势线时卖出
        sell_mask = (signals['signal'] == -1) & (data['close'] >= trend_ma)
        filtered_signals.loc[sell_mask, 'signal'] = 0
        filtered_signals.loc[sell_mask, 'strength'] = 0.0

        return filtered_signals

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'mean_reversion',
            'indicators': ['RSI'],
            'parameters': {
                'rsi_period': self.config.rsi_period,
                'oversold_threshold': self.config.oversold_threshold,
                'overbought_threshold': self.config.overbought_threshold,
            },
            'filters': {
                'trend_filter': self.config.use_trend_filter,
                'trend_period': self.config.trend_period,
            }
        }

    @classmethod
    def create_default(cls) -> 'RSIStrategy':
        """创建默认配置的RSI策略"""
        return cls(RSIConfig())

    @classmethod
    def create_sensitive(cls) -> 'RSIStrategy':
        """创建敏感RSI策略"""
        config = RSIConfig(
            rsi_period=10,
            oversold_threshold=25,
            overbought_threshold=75,
            strategy_name="RSI_Sensitive"
        )
        return cls(config)

    @classmethod
    def create_conservative(cls) -> 'RSIStrategy':
        """创建保守RSI策略"""
        config = RSIConfig(
            rsi_period=21,
            oversold_threshold=20,
            overbought_threshold=80,
            strategy_name="RSI_Conservative"
        )
        return cls(config)
