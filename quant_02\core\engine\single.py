"""
单资产回测引擎模块

专门用于单个标的的回测，提供高性能的单资产回测功能。
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import pandas as pd
import numpy as np
import time

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from .base import QuantEngine
from ..config.global_config import GlobalConfig
from ..config.backtest import BacktestConfig
from ..structures.portfolio import Portfolio, StrategyResult
from ..structures.trade import Trade, Order, OrderSide, OrderType, OrderStatus
from ..interfaces import IDataSource, IStrategy
from ..exceptions import EngineException, BacktestException
from risk import RiskManager
from utils.logger import get_logger

logger = get_logger(__name__)


class SingleAssetEngine(QuantEngine):
    """单资产回测引擎
    
    专门针对单个标的进行回测优化：
    - 高性能向量化计算
    - 内存优化
    - 详细的交易记录
    - 完整的风险管理
    - 实时性能监控
    """
    
    def __init__(
        self,
        data_source: Optional[IDataSource] = None,
        config: Optional[GlobalConfig] = None,
        backtest_config: Optional[BacktestConfig] = None,
        risk_manager: Optional[RiskManager] = None
    ):
        """
        初始化单资产回测引擎
        
        Args:
            data_source: 数据源
            config: 全局配置
            backtest_config: 回测配置
            risk_manager: 风险管理器
        """
        super().__init__(data_source, config)
        
        self.backtest_config = backtest_config or BacktestConfig.create_default()
        self.risk_manager = risk_manager
        
        # 单资产特定状态
        self._current_symbol = None
        self._current_data = None
        self._current_signals = None
        
        # 性能优化缓存
        self._price_cache = {}
        self._signal_cache = {}
        
        logger.info("单资产回测引擎初始化完成")
    
    def run_single_backtest(
        self,
        strategy: Union[str, IStrategy],
        symbol: str,
        start_date: str,
        end_date: str,
        initial_capital: Optional[float] = None,
        **kwargs
    ) -> StrategyResult:
        """
        运行单资产回测
        
        Args:
            strategy: 策略名称或策略对象
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            initial_capital: 初始资金
            **kwargs: 其他参数
            
        Returns:
            回测结果
        """
        start_time = time.time()
        
        try:
            # 设置当前标的
            self._current_symbol = symbol
            
            # 获取策略对象
            strategy_obj = self._get_strategy_object(strategy)
            
            # 获取数据
            logger.info(f"获取数据: {symbol}, {start_date} 到 {end_date}")
            data = self._get_and_validate_data(symbol, start_date, end_date)
            self._current_data = data
            
            # 初始化投资组合
            capital = initial_capital or self.backtest_config.initial_capital
            portfolio = self._initialize_portfolio(strategy_obj, symbol, capital)
            
            # 初始化策略
            strategy_obj.initialize()
            
            # 生成交易信号
            logger.info("生成交易信号")
            signals = strategy_obj.generate_signals(data)
            self._current_signals = signals
            
            # 执行回测
            logger.info("执行回测")
            self._execute_single_backtest(portfolio, data, signals, strategy_obj)
            
            # 计算结果
            result = self._calculate_backtest_result(portfolio, data, signals, strategy_obj, symbol)
            
            # 更新统计信息
            execution_time = time.time() - start_time
            self._update_stats(execution_time)
            
            logger.info(f"单资产回测完成: {symbol}, 耗时: {execution_time:.2f}秒")
            logger.info(f"总收益率: {result.total_return:.2%}, 夏普比率: {result.sharpe_ratio:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"单资产回测失败: {e}")
            raise BacktestException(f"单资产回测执行失败: {e}")
        
        finally:
            # 清理缓存
            self._cleanup_cache()
    
    def _get_strategy_object(self, strategy: Union[str, IStrategy]) -> IStrategy:
        """获取策略对象"""
        if isinstance(strategy, str):
            strategy_obj = self.get_strategy(strategy)
            if strategy_obj is None:
                raise EngineException(f"策略未注册: {strategy}")
        else:
            strategy_obj = strategy
        
        return strategy_obj
    
    def _get_and_validate_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取并验证数据"""
        if self.data_source is None:
            raise EngineException("未设置数据源")
        
        # 检查缓存
        cache_key = f"{symbol}_{start_date}_{end_date}"
        if cache_key in self._price_cache:
            logger.debug(f"使用缓存数据: {cache_key}")
            return self._price_cache[cache_key]
        
        # 获取数据
        data = self.data_source.get_data(symbol, start_date, end_date)
        
        # 验证数据
        self._validate_data(data)
        
        # 缓存数据
        self._price_cache[cache_key] = data
        
        return data
    
    def _initialize_portfolio(self, strategy: IStrategy, symbol: str, capital: float) -> Portfolio:
        """初始化投资组合"""
        portfolio = Portfolio(
            portfolio_id=f"single_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            name=f"{strategy.get_name()}_{symbol}",
            initial_capital=capital,
            timestamp=datetime.now()
        )
        
        return portfolio
    
    def _execute_single_backtest(
        self,
        portfolio: Portfolio,
        data: pd.DataFrame,
        signals: pd.DataFrame,
        strategy: IStrategy
    ):
        """执行单资产回测"""
        # 向量化回测实现
        for i, (timestamp, row) in enumerate(data.iterrows()):
            if i == 0:
                continue  # 跳过第一行
            
            current_price = row['close']
            
            # 更新投资组合价值
            portfolio.update_positions_value({self._current_symbol: current_price})
            
            # 检查信号
            if timestamp in signals.index:
                signal_row = signals.loc[timestamp]
                
                if signal_row.get('signal', 0) != 0:
                    self._process_signal(portfolio, signal_row, current_price, timestamp, strategy)
            
            # 更新价值历史
            portfolio.update_value_history(timestamp, portfolio.total_value)
            
            # 风险检查
            if self.risk_manager:
                self._check_portfolio_risk(portfolio, current_price)
    
    def _process_signal(
        self,
        portfolio: Portfolio,
        signal_row: pd.Series,
        current_price: float,
        timestamp: datetime,
        strategy: IStrategy
    ):
        """处理交易信号"""
        signal = signal_row.get('signal', 0)
        strength = signal_row.get('strength', 1.0)
        
        if signal == 1:  # 买入信号
            self._execute_buy_signal(portfolio, current_price, timestamp, strength, strategy)
        elif signal == -1:  # 卖出信号
            self._execute_sell_signal(portfolio, current_price, timestamp, strength, strategy)
    
    def _execute_buy_signal(
        self,
        portfolio: Portfolio,
        price: float,
        timestamp: datetime,
        strength: float,
        strategy: IStrategy
    ):
        """执行买入信号"""
        # 计算仓位大小
        if self.risk_manager:
            position_size = self.risk_manager.get_position_size(
                self._current_symbol, strength, portfolio
            )
        else:
            # 简化计算
            available_cash = portfolio.cash * self.backtest_config.max_position_size
            position_size = available_cash * strength
        
        if position_size < self.backtest_config.min_trade_amount:
            return
        
        # 计算股数
        shares = int(position_size / price)
        if shares <= 0:
            return
        
        # 创建订单
        order = Order(
            order_id=f"buy_{self._current_symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
            symbol=self._current_symbol,
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=shares,
            price=price,
            timestamp=timestamp
        )
        
        # 风险检查
        if self.risk_manager and not self.risk_manager.check_order(order, portfolio):
            logger.debug(f"买入订单被风险管理器拒绝: {order.order_id}")
            return
        
        # 执行交易
        trade_value = shares * price
        commission = self.backtest_config.calculate_commission(trade_value)
        slippage = self.backtest_config.calculate_slippage(trade_value)
        
        total_cost = trade_value + commission + slippage
        
        if portfolio.cash >= total_cost:
            # 创建交易记录
            trade = Trade(
                trade_id=f"trade_{order.order_id}",
                symbol=self._current_symbol,
                side=OrderSide.BUY,
                quantity=shares,
                price=price,
                timestamp=timestamp,
                commission=commission,
                slippage=slippage
            )
            
            # 更新投资组合
            portfolio.add_trade(trade)
            
            logger.debug(f"买入执行: {shares}股 @ {price:.2f}, 总成本: {total_cost:.2f}")
    
    def _execute_sell_signal(
        self,
        portfolio: Portfolio,
        price: float,
        timestamp: datetime,
        strength: float,
        strategy: IStrategy
    ):
        """执行卖出信号"""
        position = portfolio.get_position(self._current_symbol)
        if position is None or position.is_flat:
            return
        
        # 计算卖出数量
        sell_quantity = int(position.quantity * strength)
        if sell_quantity <= 0:
            return
        
        # 创建订单
        order = Order(
            order_id=f"sell_{self._current_symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
            symbol=self._current_symbol,
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=sell_quantity,
            price=price,
            timestamp=timestamp
        )
        
        # 执行交易
        trade_value = sell_quantity * price
        commission = self.backtest_config.calculate_commission(trade_value)
        slippage = self.backtest_config.calculate_slippage(trade_value)
        
        # 创建交易记录
        trade = Trade(
            trade_id=f"trade_{order.order_id}",
            symbol=self._current_symbol,
            side=OrderSide.SELL,
            quantity=sell_quantity,
            price=price,
            timestamp=timestamp,
            commission=commission,
            slippage=slippage
        )
        
        # 更新投资组合
        portfolio.add_trade(trade)
        
        logger.debug(f"卖出执行: {sell_quantity}股 @ {price:.2f}, 总收入: {trade_value - commission - slippage:.2f}")
    
    def _check_portfolio_risk(self, portfolio: Portfolio, current_price: float):
        """检查投资组合风险"""
        if not self.risk_manager:
            return
        
        # 更新风险指标
        self.risk_manager.update_risk_metrics(portfolio, {self._current_symbol: current_price})
        
        # 检查持仓风险
        position = portfolio.get_position(self._current_symbol)
        if position:
            self.risk_manager.check_position(position, current_price)
    
    def _calculate_backtest_result(
        self,
        portfolio: Portfolio,
        data: pd.DataFrame,
        signals: pd.DataFrame,
        strategy: IStrategy,
        symbol: str
    ) -> StrategyResult:
        """计算回测结果"""
        # 基本指标
        total_return = portfolio.total_return
        
        # 计算年化收益率
        days = len(data)
        annual_return = (1 + total_return) ** (252 / days) - 1 if days > 0 else 0
        
        # 计算其他指标
        metrics = portfolio.calculate_metrics()
        
        # 创建结果对象
        result = StrategyResult(
            strategy_id=strategy.get_name(),
            symbol=symbol,
            start_date=data.index[0],
            end_date=data.index[-1],
            total_return=total_return,
            annual_return=annual_return,
            volatility=metrics.get('volatility', 0),
            sharpe_ratio=metrics.get('sharpe_ratio', 0),
            max_drawdown=metrics.get('max_drawdown', 0),
            total_trades=len(portfolio.trades),
            win_rate=metrics.get('win_rate', 0),
            portfolio=portfolio,
            equity_curve=portfolio.value_history,
            trades=portfolio.trades,
            signals=signals
        )
        
        return result
    
    def _cleanup_cache(self):
        """清理缓存"""
        # 保持缓存在合理大小
        if len(self._price_cache) > 10:
            # 清理最旧的缓存
            keys = list(self._price_cache.keys())
            for key in keys[:5]:
                del self._price_cache[key]
        
        if len(self._signal_cache) > 10:
            keys = list(self._signal_cache.keys())
            for key in keys[:5]:
                del self._signal_cache[key]
    
    def get_current_symbol(self) -> Optional[str]:
        """获取当前回测标的"""
        return self._current_symbol
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前数据"""
        return self._current_data
    
    def get_current_signals(self) -> Optional[pd.DataFrame]:
        """获取当前信号"""
        return self._current_signals
    
    def clear_cache(self):
        """清空所有缓存"""
        self._price_cache.clear()
        self._signal_cache.clear()
        logger.info("单资产引擎缓存已清空")
