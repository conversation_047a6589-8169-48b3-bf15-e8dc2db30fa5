"""
网格搜索优化器

基于网格搜索的参数优化实现。
"""

import itertools
from typing import Dict, List, Any, Optional, Callable, Tuple
import pandas as pd
import numpy as np
from concurrent.futures import ProcessPoolExecutor, as_completed

from ...strategies.base import BaseStrategy
from ...utils.logger import get_logger, log_execution_time

logger = get_logger(__name__)


class GridSearchOptimizer:
    """网格搜索优化器"""
    
    def __init__(
        self,
        strategy_class: type,
        param_space: Dict[str, List],
        objective: str = 'sharpe_ratio',
        direction: str = 'maximize',
        n_jobs: int = 1
    ):
        """
        初始化网格搜索优化器
        
        Args:
            strategy_class: 策略类
            param_space: 参数空间字典
            objective: 优化目标
            direction: 优化方向 ('maximize' 或 'minimize')
            n_jobs: 并行任务数
        """
        self.strategy_class = strategy_class
        self.param_space = param_space
        self.objective = objective
        self.direction = direction
        self.n_jobs = n_jobs
        
        # 验证参数空间
        self._validate_param_space()
        
        # 生成参数组合
        self.param_combinations = self._generate_combinations()
        
        logger.info(f"网格搜索优化器初始化完成: {len(self.param_combinations)} 个参数组合")
    
    def _validate_param_space(self):
        """验证参数空间"""
        if not self.param_space:
            raise ValueError("参数空间不能为空")
        
        for param, values in self.param_space.items():
            if not isinstance(values, list) or len(values) == 0:
                raise ValueError(f"参数 {param} 的值必须是非空列表")
        
        # 检查参数组合数量
        total_combinations = 1
        for values in self.param_space.values():
            total_combinations *= len(values)
        
        if total_combinations > 10000:
            logger.warning(f"参数组合数量较大: {total_combinations}，可能需要较长时间")
    
    def _generate_combinations(self) -> List[Dict[str, Any]]:
        """生成所有参数组合"""
        keys = list(self.param_space.keys())
        values = list(self.param_space.values())
        
        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        return combinations
    
    @log_execution_time()
    def optimize(
        self,
        data: pd.DataFrame,
        backtest_config: Optional[Any] = None,
        validation_split: float = 0.0,
        early_stopping: bool = False,
        patience: int = 50
    ) -> Dict[str, Any]:
        """
        执行网格搜索优化
        
        Args:
            data: 训练数据
            backtest_config: 回测配置
            validation_split: 验证集比例
            early_stopping: 是否启用早停
            patience: 早停耐心值
            
        Returns:
            优化结果
        """
        logger.info(f"开始网格搜索优化: {len(self.param_combinations)} 个组合")
        
        # 数据分割
        if validation_split > 0:
            split_idx = int(len(data) * (1 - validation_split))
            train_data = data.iloc[:split_idx]
            val_data = data.iloc[split_idx:]
        else:
            train_data = data
            val_data = None
        
        # 执行优化
        if self.n_jobs == 1:
            results = self._optimize_sequential(train_data, val_data, backtest_config, early_stopping, patience)
        else:
            results = self._optimize_parallel(train_data, val_data, backtest_config)
        
        # 分析结果
        optimization_result = self._analyze_results(results, data)
        
        logger.info(f"网格搜索完成: 最佳{self.objective} = {optimization_result['best_score']:.4f}")
        
        return optimization_result
    
    def _optimize_sequential(
        self,
        train_data: pd.DataFrame,
        val_data: Optional[pd.DataFrame],
        backtest_config: Any,
        early_stopping: bool,
        patience: int
    ) -> List[Dict[str, Any]]:
        """顺序优化"""
        results = []
        best_score = float('-inf') if self.direction == 'maximize' else float('inf')
        no_improvement_count = 0
        
        for i, params in enumerate(self.param_combinations):
            try:
                # 训练
                train_result = self._evaluate_params(params, train_data, backtest_config)
                
                # 验证
                val_result = None
                if val_data is not None:
                    val_result = self._evaluate_params(params, val_data, backtest_config)
                
                # 记录结果
                result = {
                    'params': params,
                    'train_score': getattr(train_result, self.objective, 0),
                    'val_score': getattr(val_result, self.objective, 0) if val_result else None,
                    'train_result': train_result,
                    'val_result': val_result
                }
                
                results.append(result)
                
                # 早停检查
                if early_stopping and val_result:
                    current_score = getattr(val_result, self.objective, 0)
                    
                    is_better = (
                        (self.direction == 'maximize' and current_score > best_score) or
                        (self.direction == 'minimize' and current_score < best_score)
                    )
                    
                    if is_better:
                        best_score = current_score
                        no_improvement_count = 0
                    else:
                        no_improvement_count += 1
                    
                    if no_improvement_count >= patience:
                        logger.info(f"早停触发: {patience} 次无改善")
                        break
                
                # 进度报告
                if (i + 1) % 50 == 0:
                    logger.info(f"优化进度: {i + 1}/{len(self.param_combinations)}")
                
            except Exception as e:
                logger.warning(f"参数组合 {params} 评估失败: {e}")
                continue
        
        return results
    
    def _optimize_parallel(
        self,
        train_data: pd.DataFrame,
        val_data: Optional[pd.DataFrame],
        backtest_config: Any
    ) -> List[Dict[str, Any]]:
        """并行优化"""
        results = []
        
        with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
            # 提交任务
            future_to_params = {
                executor.submit(
                    self._evaluate_params_wrapper,
                    params, train_data, val_data, backtest_config
                ): params
                for params in self.param_combinations
            }
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                completed += 1
                
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                    
                    if completed % 50 == 0:
                        logger.info(f"并行优化进度: {completed}/{len(self.param_combinations)}")
                        
                except Exception as e:
                    logger.warning(f"参数组合 {params} 并行评估失败: {e}")
        
        return results
    
    def _evaluate_params_wrapper(
        self,
        params: Dict[str, Any],
        train_data: pd.DataFrame,
        val_data: Optional[pd.DataFrame],
        backtest_config: Any
    ) -> Optional[Dict[str, Any]]:
        """参数评估包装器（用于并行处理）"""
        try:
            # 训练
            train_result = self._evaluate_params(params, train_data, backtest_config)
            
            # 验证
            val_result = None
            if val_data is not None:
                val_result = self._evaluate_params(params, val_data, backtest_config)
            
            return {
                'params': params,
                'train_score': getattr(train_result, self.objective, 0),
                'val_score': getattr(val_result, self.objective, 0) if val_result else None,
                'train_result': train_result,
                'val_result': val_result
            }
            
        except Exception as e:
            logger.warning(f"参数评估失败: {e}")
            return None
    
    def _evaluate_params(
        self,
        params: Dict[str, Any],
        data: pd.DataFrame,
        backtest_config: Any
    ):
        """评估单个参数组合"""
        # 创建策略配置
        config_class = getattr(self.strategy_class, '__init__').__annotations__.get('config', None)
        
        if config_class:
            config = config_class(**params)
            strategy = self.strategy_class(config)
        else:
            # 如果没有配置类，直接传递参数
            strategy = self.strategy_class(params)
        
        # 运行回测
        result = strategy.run(data, backtest_config)
        
        return result
    
    def _analyze_results(
        self,
        results: List[Dict[str, Any]],
        data: pd.DataFrame
    ) -> Dict[str, Any]:
        """分析优化结果"""
        if not results:
            raise ValueError("没有有效的优化结果")
        
        # 根据目标排序
        score_key = 'val_score' if results[0]['val_score'] is not None else 'train_score'
        reverse = self.direction == 'maximize'
        
        sorted_results = sorted(
            [r for r in results if r[score_key] is not None],
            key=lambda x: x[score_key],
            reverse=reverse
        )
        
        if not sorted_results:
            raise ValueError("没有有效的评分结果")
        
        best_result = sorted_results[0]
        
        # 统计分析
        scores = [r[score_key] for r in sorted_results]
        
        analysis = {
            'best_params': best_result['params'],
            'best_score': best_result[score_key],
            'best_result': best_result['val_result'] if best_result['val_result'] else best_result['train_result'],
            'n_trials': len(results),
            'n_successful': len(sorted_results),
            'score_statistics': {
                'mean': np.mean(scores),
                'std': np.std(scores),
                'min': np.min(scores),
                'max': np.max(scores),
                'median': np.median(scores)
            },
            'top_10_results': sorted_results[:10],
            'param_importance': self._calculate_param_importance(sorted_results)
        }
        
        return analysis
    
    def _calculate_param_importance(
        self,
        results: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """计算参数重要性"""
        if len(results) < 10:
            return {}
        
        param_importance = {}
        score_key = 'val_score' if results[0]['val_score'] is not None else 'train_score'
        
        # 取前20%的结果
        top_n = max(1, len(results) // 5)
        top_results = results[:top_n]
        
        # 统计每个参数值在top结果中的出现频率
        for param_name in self.param_space.keys():
            param_values = {}
            
            for result in top_results:
                value = result['params'][param_name]
                if value not in param_values:
                    param_values[value] = 0
                param_values[value] += 1
            
            # 计算重要性（最频繁值的出现比例）
            if param_values:
                max_count = max(param_values.values())
                importance = max_count / len(top_results)
                param_importance[param_name] = importance
        
        return param_importance


__all__ = [
    "GridSearchOptimizer"
]
