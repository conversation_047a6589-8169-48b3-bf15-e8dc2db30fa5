"""
数据库数据源

支持从数据库读取数据。
"""

from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from dataseed.base import DataSource
from utils.logger import get_logger

logger = get_logger(__name__)

try:
    import sqlalchemy as sa
    from sqlalchemy import create_engine, text
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    logger.warning("SQLAlchemy未安装，数据库数据源不可用")


class DatabaseDataSource(DataSource):
    """数据库数据源

    支持从关系型数据库读取数据。
    """

    def __init__(
        self,
        name: str = "database",
        connection_string: str = None,
        table_name: str = "stock_data",
        **kwargs
    ):
        """
        初始化数据库数据源

        Args:
            name: 数据源名称
            connection_string: 数据库连接字符串
            table_name: 数据表名
            **kwargs: 其他参数
        """
        if not SQLALCHEMY_AVAILABLE:
            raise ImportError("SQLAlchemy未安装，请先安装: pip install sqlalchemy")

        super().__init__(name, **kwargs)

        self.connection_string = connection_string or "sqlite:///data/stock_data.db"
        self.table_name = table_name
        self.engine = None

        # 初始化数据库连接
        self._init_connection()

        logger.info(f"数据库数据源初始化完成: {self.connection_string}")

    def _init_connection(self):
        """初始化数据库连接"""
        try:
            self.engine = create_engine(self.connection_string)

            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            logger.info("数据库连接成功")

        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def _fetch_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """
        从数据库读取数据

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            **kwargs: 其他参数

        Returns:
            原始数据DataFrame
        """
        try:
            # 构建SQL查询
            query = self._build_query(symbol, start_date, end_date, frequency)

            # 执行查询
            data = pd.read_sql(query, self.engine, parse_dates=['date'])

            if not data.empty:
                # 设置日期索引
                data.set_index('date', inplace=True)
                data.sort_index(inplace=True)

            logger.debug(f"数据库数据读取成功: {symbol}, {len(data)}条记录")

            return data

        except Exception as e:
            logger.error(f"数据库数据读取失败: {symbol}, 错误: {e}")
            raise

    def _build_query(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str
    ) -> str:
        """构建SQL查询语句"""
        # 转换日期格式
        start_str = pd.to_datetime(start_date).strftime('%Y-%m-%d')
        end_str = pd.to_datetime(end_date).strftime('%Y-%m-%d')

        # 基础查询
        query = f"""
        SELECT
            date,
            open,
            high,
            low,
            close,
            volume,
            amount
        FROM {self.table_name}
        WHERE symbol = '{symbol}'
        AND date >= '{start_str}'
        AND date <= '{end_str}'
        ORDER BY date
        """

        return query

    def get_available_symbols(self) -> List[str]:
        """获取可用的股票代码列表"""
        try:
            query = f"SELECT DISTINCT symbol FROM {self.table_name}"
            result = pd.read_sql(query, self.engine)

            return result['symbol'].tolist()

        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        try:
            # 获取基本统计信息
            query = f"""
            SELECT
                COUNT(*) as record_count,
                MIN(date) as start_date,
                MAX(date) as end_date,
                MIN(close) as min_price,
                MAX(close) as max_price,
                AVG(close) as avg_price
            FROM {self.table_name}
            WHERE symbol = '{symbol}'
            """

            result = pd.read_sql(query, self.engine)

            if not result.empty:
                info = result.iloc[0].to_dict()
                info['symbol'] = symbol
                return info
            else:
                return {'symbol': symbol, 'error': '未找到数据'}

        except Exception as e:
            return {'symbol': symbol, 'error': str(e)}

    def insert_data(self, symbol: str, data: pd.DataFrame):
        """
        插入数据到数据库

        Args:
            symbol: 股票代码
            data: 数据DataFrame
        """
        try:
            # 准备数据
            insert_data = data.copy()
            insert_data['symbol'] = symbol

            # 重置索引，确保date列存在
            if isinstance(insert_data.index, pd.DatetimeIndex):
                insert_data.reset_index(inplace=True)

            # 插入数据
            insert_data.to_sql(
                self.table_name,
                self.engine,
                if_exists='append',
                index=False,
                method='multi'
            )

            logger.info(f"数据已插入数据库: {symbol}, {len(insert_data)}条记录")

        except Exception as e:
            logger.error(f"数据插入失败: {symbol}, 错误: {e}")
            raise

    def create_table(self):
        """创建数据表"""
        try:
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.table_name} (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol VARCHAR(10) NOT NULL,
                date DATE NOT NULL,
                open DECIMAL(10,2),
                high DECIMAL(10,2),
                low DECIMAL(10,2),
                close DECIMAL(10,2),
                volume BIGINT,
                amount DECIMAL(15,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, date)
            )
            """

            with self.engine.connect() as conn:
                conn.execute(text(create_sql))
                conn.commit()

            logger.info(f"数据表已创建: {self.table_name}")

        except Exception as e:
            logger.error(f"数据表创建失败: {e}")
            raise

    def delete_symbol_data(self, symbol: str):
        """删除指定股票的数据"""
        try:
            delete_sql = f"DELETE FROM {self.table_name} WHERE symbol = '{symbol}'"

            with self.engine.connect() as conn:
                result = conn.execute(text(delete_sql))
                conn.commit()

            logger.info(f"股票数据已删除: {symbol}, 影响行数: {result.rowcount}")

        except Exception as e:
            logger.error(f"数据删除失败: {symbol}, 错误: {e}")
            raise

    def get_table_info(self) -> Dict[str, Any]:
        """获取数据表信息"""
        try:
            # 获取表结构
            query = f"PRAGMA table_info({self.table_name})"
            columns_info = pd.read_sql(query, self.engine)

            # 获取记录统计
            query = f"SELECT COUNT(*) as total_records, COUNT(DISTINCT symbol) as symbol_count FROM {self.table_name}"
            stats = pd.read_sql(query, self.engine)

            return {
                'table_name': self.table_name,
                'columns': columns_info.to_dict('records'),
                'total_records': stats.iloc[0]['total_records'],
                'symbol_count': stats.iloc[0]['symbol_count'],
            }

        except Exception as e:
            logger.error(f"获取表信息失败: {e}")
            return {'error': str(e)}

    def __del__(self):
        """析构函数"""
        if self.engine:
            self.engine.dispose()
