"""
流动性风险规则模块

定义与流动性相关的风险控制规则。
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from .base import BaseRiskRule, RiskRuleResult, RiskLevel
from core.structures import Order, Position, Portfolio, OrderSide


class LiquidityRule(BaseRiskRule):
    """流动性规则
    
    控制流动性风险。
    """
    
    def __init__(self, min_liquidity_score: float = 0.5, **kwargs):
        """
        初始化流动性规则
        
        Args:
            min_liquidity_score: 最小流动性评分
        """
        super().__init__("liquidity", min_liquidity_score=min_liquidity_score, **kwargs)
        self.min_liquidity_score = min_liquidity_score
    
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskRuleResult:
        """检查订单的流动性"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        # 简化实现：假设所有标的流动性良好
        liquidity_score = 0.8  # 模拟流动性评分
        
        if liquidity_score < self.min_liquidity_score:
            return RiskRuleResult.fail_result(
                self.name,
                f"标的{order.symbol}流动性不足({liquidity_score:.2f} < {self.min_liquidity_score:.2f})",
                RiskLevel.MEDIUM,
                {
                    'symbol': order.symbol,
                    'liquidity_score': liquidity_score,
                    'min_score': self.min_liquidity_score
                }
            )
        
        return RiskRuleResult.pass_result(self.name, f"流动性充足({liquidity_score:.2f})")
    
    def check_position(self, position: Position, portfolio: Portfolio) -> RiskRuleResult:
        """检查持仓的流动性"""
        return RiskRuleResult.pass_result(self.name, "持仓流动性检查通过")
    
    def check_portfolio(self, portfolio: Portfolio) -> RiskRuleResult:
        """检查投资组合的流动性"""
        return RiskRuleResult.pass_result(self.name, "投资组合流动性检查通过")


class VolumeRule(BaseRiskRule):
    """成交量规则
    
    控制基于成交量的风险。
    """
    
    def __init__(self, min_avg_volume: float = 1000000, **kwargs):
        """
        初始化成交量规则
        
        Args:
            min_avg_volume: 最小平均成交量
        """
        super().__init__("volume", min_avg_volume=min_avg_volume, **kwargs)
        self.min_avg_volume = min_avg_volume
    
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskRuleResult:
        """检查订单的成交量要求"""
        if not self.enabled:
            return RiskRuleResult.pass_result(self.name, "规则已禁用")
        
        # 简化实现：假设成交量充足
        avg_volume = 2000000  # 模拟平均成交量
        
        if avg_volume < self.min_avg_volume:
            return RiskRuleResult.fail_result(
                self.name,
                f"标的{order.symbol}成交量不足({avg_volume:,.0f} < {self.min_avg_volume:,.0f})",
                RiskLevel.MEDIUM,
                {
                    'symbol': order.symbol,
                    'avg_volume': avg_volume,
                    'min_volume': self.min_avg_volume
                }
            )
        
        return RiskRuleResult.pass_result(self.name, f"成交量充足({avg_volume:,.0f})")
    
    def check_position(self, position: Position, portfolio: Portfolio) -> RiskRuleResult:
        """检查持仓的成交量"""
        return RiskRuleResult.pass_result(self.name, "持仓成交量检查通过")
    
    def check_portfolio(self, portfolio: Portfolio) -> RiskRuleResult:
        """检查投资组合的成交量"""
        return RiskRuleResult.pass_result(self.name, "投资组合成交量检查通过")
