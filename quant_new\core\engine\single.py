"""
单资产回测引擎

专门用于单个资产的策略回测。
"""

from typing import Dict, List, Optional, Any, Union
import pandas as pd
import numpy as np

from .base import BacktestEngine
from ...strategies.base import BaseStrategy, StrategyResult
from ...utils.logger import get_logger, log_execution_time

logger = get_logger(__name__)


class SingleAssetEngine(BacktestEngine):
    """单资产回测引擎"""
    
    @log_execution_time()
    def run_strategy(
        self,
        strategy: BaseStrategy,
        symbol: str,
        **kwargs
    ) -> StrategyResult:
        """
        运行单资产策略回测
        
        Args:
            strategy: 策略实例
            symbol: 股票代码
            **kwargs: 其他参数
            
        Returns:
            策略结果
        """
        logger.info(f"开始单资产回测: {symbol}, 策略: {strategy.name}")
        
        try:
            # 获取数据
            data = self.get_data(symbol)
            
            if data.empty:
                raise ValueError(f"股票 {symbol} 数据为空")
            
            # 验证数据
            if not self.validate_data(data):
                raise ValueError(f"股票 {symbol} 数据验证失败")
            
            logger.info(f"数据获取成功: {len(data)} 条记录")
            
            # 运行策略
            result = strategy.run(data, self.backtest_config)
            
            # 增强结果信息
            result = self._enhance_result(result, data, symbol)
            
            # 保存结果
            self._results[symbol] = result
            
            logger.info(f"回测完成: {symbol}, 总收益: {result.total_return:.2%}")
            
            return result
            
        except Exception as e:
            logger.error(f"单资产回测失败: {symbol}, 错误: {e}")
            raise
    
    def _enhance_result(
        self,
        result: StrategyResult,
        data: pd.DataFrame,
        symbol: str
    ) -> StrategyResult:
        """
        增强策略结果
        
        Args:
            result: 原始结果
            data: 价格数据
            symbol: 股票代码
            
        Returns:
            增强后的结果
        """
        try:
            # 计算基准收益（买入持有）
            benchmark_returns = data['close'].pct_change().dropna()
            
            # 如果策略有持仓数据，计算策略收益
            if not result.positions.empty and 'position' in result.positions.columns:
                strategy_returns = self._calculate_strategy_returns(result.positions, data)
                
                # 重新计算绩效指标
                performance_metrics = self.calculate_performance_metrics(
                    strategy_returns, 
                    benchmark_returns
                )
                
                # 更新结果
                for key, value in performance_metrics.items():
                    if hasattr(result, key):
                        setattr(result, key, value)
            
            # 计算交易统计
            if not result.signals.empty:
                trade_stats = self.calculate_trade_statistics(result.signals)
                
                # 更新交易统计
                result.total_trades = trade_stats.get('total_trades', 0)
                result.winning_trades = trade_stats.get('buy_trades', 0)  # 简化处理
                result.losing_trades = trade_stats.get('sell_trades', 0)
                
                # 添加元数据
                result.metadata.update({
                    'data_points': len(data),
                    'benchmark_return': (1 + benchmark_returns).prod() - 1,
                    'trade_statistics': trade_stats,
                    'backtest_config': self.backtest_config.dict() if hasattr(self.backtest_config, 'dict') else {}
                })
            
            return result
            
        except Exception as e:
            logger.warning(f"结果增强失败: {e}")
            return result
    
    def _calculate_strategy_returns(
        self,
        positions: pd.DataFrame,
        data: pd.DataFrame
    ) -> pd.Series:
        """
        计算策略收益
        
        Args:
            positions: 持仓数据
            data: 价格数据
            
        Returns:
            策略收益序列
        """
        # 对齐数据
        aligned_positions, aligned_prices = positions.align(data['close'], join='inner')
        
        if aligned_positions.empty or aligned_prices.empty:
            return pd.Series(dtype=float)
        
        # 计算价格收益率
        price_returns = aligned_prices.pct_change()
        
        # 获取持仓信号（滞后一期）
        if 'position' in aligned_positions.columns:
            position_signals = aligned_positions['position'].shift(1).fillna(0)
        else:
            position_signals = pd.Series(0, index=aligned_positions.index)
        
        # 计算策略收益
        strategy_returns = position_signals * price_returns
        
        return strategy_returns.dropna()
    
    def run_optimization(
        self,
        strategy_class: type,
        symbol: str,
        param_space: Dict[str, List],
        objective: str = 'sharpe_ratio',
        n_trials: int = 100
    ) -> Dict[str, Any]:
        """
        运行参数优化
        
        Args:
            strategy_class: 策略类
            symbol: 股票代码
            param_space: 参数空间
            objective: 优化目标
            n_trials: 试验次数
            
        Returns:
            优化结果
        """
        logger.info(f"开始参数优化: {symbol}, 目标: {objective}")
        
        # 获取数据
        data = self.get_data(symbol)
        
        if data.empty:
            raise ValueError(f"股票 {symbol} 数据为空")
        
        best_params = None
        best_score = float('-inf') if objective in ['sharpe_ratio', 'total_return'] else float('inf')
        results = []
        
        # 网格搜索
        param_combinations = self._generate_param_combinations(param_space)
        
        for i, params in enumerate(param_combinations[:n_trials]):
            try:
                # 创建策略配置
                config_class = strategy_class.__init__.__annotations__.get('config', None)
                if config_class:
                    config = config_class(**params)
                    strategy = strategy_class(config)
                else:
                    # 如果没有配置类，直接传递参数
                    strategy = strategy_class(params)
                
                # 运行回测
                result = strategy.run(data, self.backtest_config)
                
                # 获取目标值
                score = getattr(result, objective, 0)
                
                # 记录结果
                results.append({
                    'params': params,
                    'score': score,
                    'result': result
                })
                
                # 更新最佳结果
                is_better = (
                    (objective in ['sharpe_ratio', 'total_return'] and score > best_score) or
                    (objective in ['max_drawdown', 'volatility'] and score < best_score)
                )
                
                if is_better:
                    best_score = score
                    best_params = params
                
                if (i + 1) % 10 == 0:
                    logger.info(f"优化进度: {i + 1}/{min(n_trials, len(param_combinations))}")
                
            except Exception as e:
                logger.warning(f"参数组合 {params} 优化失败: {e}")
                continue
        
        optimization_result = {
            'best_params': best_params,
            'best_score': best_score,
            'objective': objective,
            'n_trials': len(results),
            'all_results': results
        }
        
        logger.info(f"参数优化完成: 最佳{objective} = {best_score:.4f}")
        
        return optimization_result
    
    def _generate_param_combinations(self, param_space: Dict[str, List]) -> List[Dict]:
        """生成参数组合"""
        import itertools
        
        keys = list(param_space.keys())
        values = list(param_space.values())
        
        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def run_walk_forward_analysis(
        self,
        strategy: BaseStrategy,
        symbol: str,
        train_period: int = 252,
        test_period: int = 63,
        step_size: int = 21
    ) -> Dict[str, Any]:
        """
        运行滚动窗口分析
        
        Args:
            strategy: 策略实例
            symbol: 股票代码
            train_period: 训练期长度（交易日）
            test_period: 测试期长度（交易日）
            step_size: 步长（交易日）
            
        Returns:
            滚动分析结果
        """
        logger.info(f"开始滚动窗口分析: {symbol}")
        
        # 获取全部数据
        data = self.get_data(symbol)
        
        if len(data) < train_period + test_period:
            raise ValueError("数据长度不足以进行滚动窗口分析")
        
        results = []
        start_idx = 0
        
        while start_idx + train_period + test_period <= len(data):
            try:
                # 划分训练和测试数据
                train_end_idx = start_idx + train_period
                test_end_idx = train_end_idx + test_period
                
                train_data = data.iloc[start_idx:train_end_idx]
                test_data = data.iloc[train_end_idx:test_end_idx]
                
                # 在测试数据上运行策略
                test_result = strategy.run(test_data, self.backtest_config)
                
                # 记录结果
                results.append({
                    'train_start': train_data.index[0],
                    'train_end': train_data.index[-1],
                    'test_start': test_data.index[0],
                    'test_end': test_data.index[-1],
                    'result': test_result
                })
                
                start_idx += step_size
                
            except Exception as e:
                logger.warning(f"滚动窗口分析步骤失败: {e}")
                start_idx += step_size
                continue
        
        # 汇总统计
        if results:
            returns = [r['result'].total_return for r in results]
            sharpe_ratios = [r['result'].sharpe_ratio for r in results]
            
            summary = {
                'n_periods': len(results),
                'avg_return': np.mean(returns),
                'std_return': np.std(returns),
                'avg_sharpe': np.mean(sharpe_ratios),
                'win_rate': sum(1 for r in returns if r > 0) / len(returns),
                'best_return': max(returns),
                'worst_return': min(returns)
            }
        else:
            summary = {}
        
        walk_forward_result = {
            'summary': summary,
            'detailed_results': results,
            'parameters': {
                'train_period': train_period,
                'test_period': test_period,
                'step_size': step_size
            }
        }
        
        logger.info(f"滚动窗口分析完成: {len(results)} 个周期")
        
        return walk_forward_result


__all__ = [
    "SingleAssetEngine"
]
