"""
缓存管理模块

提供高效的缓存管理功能，支持多种缓存策略。
"""

from typing import Any, Dict, Optional, Callable, Union
from datetime import datetime, timedelta
import time
import threading
import hashlib
import pickle
from functools import wraps
from dataclasses import dataclass
from enum import Enum

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import get_logger

logger = get_logger(__name__)


class CachePolicy(Enum):
    """缓存策略"""
    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    TTL = "ttl"  # 生存时间
    FIFO = "fifo"  # 先进先出


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl: Optional[float] = None
    
    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at.timestamp() > self.ttl
    
    @property
    def age(self) -> float:
        """缓存年龄（秒）"""
        return time.time() - self.created_at.timestamp()


class CacheManager:
    """缓存管理器
    
    提供高效的内存缓存功能：
    - 多种缓存策略
    - TTL支持
    - 线程安全
    - 统计信息
    - 自动清理
    """
    
    def __init__(
        self,
        max_size: int = 1000,
        default_ttl: Optional[float] = None,
        policy: CachePolicy = CachePolicy.LRU,
        cleanup_interval: float = 300  # 5分钟
    ):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认TTL（秒）
            policy: 缓存策略
            cleanup_interval: 清理间隔（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.policy = policy
        self.cleanup_interval = cleanup_interval
        
        # 缓存存储
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        
        # 统计信息
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0,
            'total_requests': 0
        }
        
        # 自动清理
        self._cleanup_timer = None
        self._start_cleanup_timer()
        
        logger.info(f"缓存管理器初始化: max_size={max_size}, policy={policy.value}")
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在或已过期则返回None
        """
        with self._lock:
            self._stats['total_requests'] += 1
            
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired:
                del self._cache[key]
                self._stats['expired'] += 1
                self._stats['misses'] += 1
                return None
            
            # 更新访问信息
            entry.last_accessed = datetime.now()
            entry.access_count += 1
            
            self._stats['hits'] += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒），None使用默认TTL
            
        Returns:
            是否设置成功
        """
        with self._lock:
            now = datetime.now()
            
            # 使用默认TTL
            if ttl is None:
                ttl = self.default_ttl
            
            # 创建缓存条目
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=now,
                last_accessed=now,
                access_count=0,
                ttl=ttl
            )
            
            # 检查是否需要清理空间
            if key not in self._cache and len(self._cache) >= self.max_size:
                self._evict_one()
            
            self._cache[key] = entry
            return True
    
    def delete(self, key: str) -> bool:
        """
        删除缓存条目
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self):
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
            logger.info("缓存已清空")
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        with self._lock:
            if key not in self._cache:
                return False
            
            entry = self._cache[key]
            if entry.is_expired:
                del self._cache[key]
                return False
            
            return True
    
    def size(self) -> int:
        """获取缓存大小"""
        with self._lock:
            return len(self._cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            stats = self._stats.copy()
            
            if stats['total_requests'] > 0:
                stats['hit_rate'] = stats['hits'] / stats['total_requests']
                stats['miss_rate'] = stats['misses'] / stats['total_requests']
            else:
                stats['hit_rate'] = 0.0
                stats['miss_rate'] = 0.0
            
            stats['current_size'] = len(self._cache)
            stats['max_size'] = self.max_size
            stats['utilization'] = len(self._cache) / self.max_size if self.max_size > 0 else 0
            
            return stats
    
    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self._stats = {
                'hits': 0,
                'misses': 0,
                'evictions': 0,
                'expired': 0,
                'total_requests': 0
            }
    
    def _evict_one(self):
        """根据策略驱逐一个缓存条目"""
        if not self._cache:
            return
        
        if self.policy == CachePolicy.LRU:
            # 最近最少使用
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].last_accessed)
        elif self.policy == CachePolicy.LFU:
            # 最少使用频率
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].access_count)
        elif self.policy == CachePolicy.FIFO:
            # 先进先出
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].created_at)
        else:
            # 默认LRU
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].last_accessed)
        
        del self._cache[oldest_key]
        self._stats['evictions'] += 1
    
    def _cleanup_expired(self):
        """清理过期条目"""
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired
            ]
            
            for key in expired_keys:
                del self._cache[key]
                self._stats['expired'] += 1
            
            if expired_keys:
                logger.debug(f"清理过期缓存条目: {len(expired_keys)}个")
    
    def _start_cleanup_timer(self):
        """启动清理定时器"""
        def cleanup():
            self._cleanup_expired()
            self._cleanup_timer = threading.Timer(self.cleanup_interval, cleanup)
            self._cleanup_timer.daemon = True
            self._cleanup_timer.start()
        
        self._cleanup_timer = threading.Timer(self.cleanup_interval, cleanup)
        self._cleanup_timer.daemon = True
        self._cleanup_timer.start()
    
    def stop(self):
        """停止缓存管理器"""
        if self._cleanup_timer:
            self._cleanup_timer.cancel()
        logger.info("缓存管理器已停止")
    
    def __del__(self):
        """析构函数"""
        self.stop()


# 全局缓存实例
_global_cache = CacheManager()


def cached(
    ttl: Optional[float] = None,
    key_func: Optional[Callable] = None,
    cache_manager: Optional[CacheManager] = None
):
    """
    缓存装饰器
    
    Args:
        ttl: 生存时间
        key_func: 自定义键生成函数
        cache_manager: 缓存管理器实例
    """
    def decorator(func):
        nonlocal cache_manager
        if cache_manager is None:
            cache_manager = _global_cache
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = _generate_cache_key(func.__name__, args, kwargs)
            
            # 尝试从缓存获取
            result = cache_manager.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            
            return result
        
        # 添加缓存控制方法
        wrapper.cache_clear = lambda: cache_manager.clear()
        wrapper.cache_info = lambda: cache_manager.get_stats()
        
        return wrapper
    
    return decorator


def _generate_cache_key(func_name: str, args: tuple, kwargs: dict) -> str:
    """生成缓存键"""
    # 创建包含函数名、参数的字符串
    key_parts = [func_name]
    
    # 添加位置参数
    for arg in args:
        if hasattr(arg, '__dict__'):
            # 对象参数，使用类名和id
            key_parts.append(f"{arg.__class__.__name__}_{id(arg)}")
        else:
            key_parts.append(str(arg))
    
    # 添加关键字参数
    for k, v in sorted(kwargs.items()):
        key_parts.append(f"{k}={v}")
    
    # 生成哈希
    key_string = "|".join(key_parts)
    return hashlib.md5(key_string.encode()).hexdigest()


def get_global_cache() -> CacheManager:
    """获取全局缓存实例"""
    return _global_cache


def set_global_cache(cache_manager: CacheManager):
    """设置全局缓存实例"""
    global _global_cache
    _global_cache = cache_manager
