"""
回测配置模块

定义回测相关的配置。
"""

from datetime import datetime, date
from typing import Optional, Dict, Any, Union
from pydantic import Field, validator

from .base import BaseConfig


class BacktestConfig(BaseConfig):
    """回测配置"""
    
    config_name: str = Field(default="backtest", description="配置名称")
    
    # 基本参数
    initial_capital: float = Field(default=1000000.0, description="初始资金", gt=0)
    commission_rate: float = Field(default=0.0003, description="手续费率", ge=0, le=0.1)
    slippage_rate: float = Field(default=0.0001, description="滑点率", ge=0, le=0.1)
    
    # 交易参数
    max_position_size: float = Field(default=1.0, description="最大仓位比例", ge=0, le=1)
    min_trade_amount: float = Field(default=1000.0, description="最小交易金额", ge=0)
    cash_reserve_ratio: float = Field(default=0.05, description="现金保留比例", ge=0, le=1)
    
    # 时间参数
    start_date: Optional[str] = Field(default=None, description="回测开始日期")
    end_date: Optional[str] = Field(default=None, description="回测结束日期")
    frequency: str = Field(default="1d", description="数据频率")
    
    # 基准参数
    benchmark: str = Field(default="000300", description="基准指数")
    
    # 风险参数
    enable_risk_management: bool = Field(default=True, description="是否启用风险管理")
    max_drawdown_limit: Optional[float] = Field(default=None, description="最大回撤限制")
    stop_loss_ratio: Optional[float] = Field(default=None, description="止损比例")
    take_profit_ratio: Optional[float] = Field(default=None, description="止盈比例")
    
    # 交易限制
    max_trades_per_day: Optional[int] = Field(default=None, description="每日最大交易次数")
    min_holding_period: Optional[int] = Field(default=None, description="最小持仓天数")
    max_holding_period: Optional[int] = Field(default=None, description="最大持仓天数")
    
    # 做空设置
    allow_short: bool = Field(default=False, description="是否允许做空")
    short_fee_rate: float = Field(default=0.001, description="做空费率", ge=0)
    
    # 杠杆设置
    enable_leverage: bool = Field(default=False, description="是否启用杠杆")
    max_leverage: float = Field(default=1.0, description="最大杠杆倍数", ge=1.0)
    
    # 滑点模型
    slippage_model: str = Field(default="fixed", description="滑点模型")
    slippage_params: Dict[str, Any] = Field(default_factory=dict, description="滑点参数")
    
    # 手续费模型
    commission_model: str = Field(default="fixed", description="手续费模型")
    commission_params: Dict[str, Any] = Field(default_factory=dict, description="手续费参数")
    
    @validator('frequency')
    def validate_frequency(cls, v):
        valid_frequencies = ['1m', '5m', '15m', '30m', '1h', '1d', '1w', '1M']
        if v not in valid_frequencies:
            raise ValueError(f"数据频率必须是: {valid_frequencies}")
        return v
    
    @validator('slippage_model')
    def validate_slippage_model(cls, v):
        valid_models = ['fixed', 'linear', 'sqrt', 'custom']
        if v not in valid_models:
            raise ValueError(f"滑点模型必须是: {valid_models}")
        return v
    
    @validator('commission_model')
    def validate_commission_model(cls, v):
        valid_models = ['fixed', 'tiered', 'percentage', 'custom']
        if v not in valid_models:
            raise ValueError(f"手续费模型必须是: {valid_models}")
        return v
    
    def get_date_range(self) -> tuple:
        """获取回测日期范围"""
        start = self.start_date
        end = self.end_date
        
        if start is None:
            start = "2020-01-01"
        if end is None:
            end = datetime.now().strftime("%Y-%m-%d")
        
        return start, end
    
    def calculate_commission(self, trade_value: float) -> float:
        """计算手续费"""
        if self.commission_model == "fixed":
            return trade_value * self.commission_rate
        elif self.commission_model == "tiered":
            # 阶梯式手续费
            params = self.commission_params
            tiers = params.get('tiers', [(float('inf'), self.commission_rate)])
            
            for threshold, rate in tiers:
                if trade_value <= threshold:
                    return trade_value * rate
            
            return trade_value * self.commission_rate
        elif self.commission_model == "percentage":
            return trade_value * self.commission_rate
        else:
            # 自定义模型
            return trade_value * self.commission_rate
    
    def calculate_slippage(self, trade_value: float, volume: float = None) -> float:
        """计算滑点"""
        if self.slippage_model == "fixed":
            return trade_value * self.slippage_rate
        elif self.slippage_model == "linear":
            # 线性滑点模型
            params = self.slippage_params
            base_rate = params.get('base_rate', self.slippage_rate)
            volume_factor = params.get('volume_factor', 0.0)
            
            if volume is not None:
                return trade_value * (base_rate + volume_factor * volume)
            else:
                return trade_value * base_rate
        elif self.slippage_model == "sqrt":
            # 平方根滑点模型
            params = self.slippage_params
            base_rate = params.get('base_rate', self.slippage_rate)
            volume_factor = params.get('volume_factor', 0.0)
            
            if volume is not None:
                import math
                return trade_value * (base_rate + volume_factor * math.sqrt(volume))
            else:
                return trade_value * base_rate
        else:
            # 自定义模型
            return trade_value * self.slippage_rate
    
    def is_valid_trade_time(self, timestamp: datetime) -> bool:
        """检查是否为有效交易时间"""
        # 简化实现，实际应该考虑交易所开市时间
        return True
    
    def get_position_limit(self, symbol: str) -> float:
        """获取持仓限制"""
        return self.max_position_size
    
    def to_vectorbt_params(self) -> Dict[str, Any]:
        """转换为VectorBT参数格式"""
        return {
            'init_cash': self.initial_capital,
            'fees': self.commission_rate,
            'slippage': self.slippage_rate,
            'min_size': self.min_trade_amount,
            'max_size': self.initial_capital * self.max_position_size,
            'size_type': 'amount',
            'direction': 'both' if self.allow_short else 'longonly',
            'freq': self.frequency,
        }
    
    @classmethod
    def create_conservative(cls) -> 'BacktestConfig':
        """创建保守型配置"""
        return cls(
            config_name="conservative",
            commission_rate=0.0005,
            slippage_rate=0.0002,
            max_position_size=0.8,
            cash_reserve_ratio=0.2,
            enable_risk_management=True,
            max_drawdown_limit=0.15,
            stop_loss_ratio=0.05,
            allow_short=False,
            enable_leverage=False,
        )
    
    @classmethod
    def create_aggressive(cls) -> 'BacktestConfig':
        """创建激进型配置"""
        return cls(
            config_name="aggressive",
            commission_rate=0.0003,
            slippage_rate=0.0001,
            max_position_size=1.0,
            cash_reserve_ratio=0.05,
            enable_risk_management=True,
            max_drawdown_limit=0.25,
            allow_short=True,
            enable_leverage=True,
            max_leverage=2.0,
        )
    
    @classmethod
    def create_default(cls) -> 'BacktestConfig':
        """创建默认配置"""
        return cls()
