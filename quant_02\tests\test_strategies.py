"""
策略模块测试

测试策略相关功能。
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from strategies.single.macd import MACDStrategy, MACDConfig
from strategies.factory import strategy_factory, create_strategy
from strategies.registry import strategy_registry


class TestMACDStrategy(unittest.TestCase):
    """MACD策略测试"""
    
    def setUp(self):
        """测试准备"""
        self.config = MACDConfig(
            strategy_name="test_macd",
            fast_period=12,
            slow_period=26,
            signal_period=9
        )
        self.strategy = MACDStrategy(self.config)
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        
        # 生成模拟价格数据
        price = 100
        prices = [price]
        
        for _ in range(len(dates) - 1):
            change = np.random.normal(0, 0.02)
            price = price * (1 + change)
            prices.append(price)
        
        self.test_data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
    
    def test_strategy_initialization(self):
        """测试策略初始化"""
        self.assertEqual(self.strategy.get_name(), "test_macd")
        self.assertEqual(self.strategy.config.fast_period, 12)
        self.assertEqual(self.strategy.config.slow_period, 26)
        self.assertEqual(self.strategy.config.signal_period, 9)
    
    def test_parameter_setting(self):
        """测试参数设置"""
        self.strategy.set_parameter('fast_period', 10)
        self.assertEqual(self.strategy.get_parameter('fast_period'), 10)
        
        params = {'slow_period': 20, 'signal_period': 8}
        self.strategy.update_parameters(params)
        self.assertEqual(self.strategy.get_parameter('slow_period'), 20)
        self.assertEqual(self.strategy.get_parameter('signal_period'), 8)
    
    def test_signal_generation(self):
        """测试信号生成"""
        signals = self.strategy.generate_signals(self.test_data)
        
        # 检查信号DataFrame结构
        self.assertIsInstance(signals, pd.DataFrame)
        self.assertIn('signal', signals.columns)
        self.assertIn('strength', signals.columns)
        self.assertIn('price', signals.columns)
        
        # 检查信号值范围
        self.assertTrue(signals['signal'].isin([-1, 0, 1]).all())
        self.assertTrue((signals['strength'] >= 0).all())
        self.assertTrue((signals['strength'] <= 1).all())
        
        # 检查有信号生成
        signal_count = (signals['signal'] != 0).sum()
        self.assertGreater(signal_count, 0)
    
    def test_indicator_caching(self):
        """测试指标缓存"""
        # 第一次计算
        signals1 = self.strategy.generate_signals(self.test_data)
        
        # 第二次计算（应该使用缓存）
        signals2 = self.strategy.generate_signals(self.test_data)
        
        # 结果应该相同
        pd.testing.assert_frame_equal(signals1, signals2)
    
    def test_empty_data_handling(self):
        """测试空数据处理"""
        empty_data = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
        
        with self.assertRaises(Exception):
            self.strategy.generate_signals(empty_data)
    
    def test_insufficient_data_handling(self):
        """测试数据不足处理"""
        # 只有几行数据
        small_data = self.test_data.head(5)
        
        # 应该能处理但可能没有信号
        signals = self.strategy.generate_signals(small_data)
        self.assertIsInstance(signals, pd.DataFrame)


class TestStrategyFactory(unittest.TestCase):
    """策略工厂测试"""
    
    def test_strategy_registration(self):
        """测试策略注册"""
        # 检查MACD策略是否已注册
        self.assertTrue(strategy_factory.is_available('macd'))
        
        # 获取可用策略列表
        available = strategy_factory.list_available()
        self.assertIn('macd', available)
    
    def test_strategy_creation(self):
        """测试策略创建"""
        # 使用工厂创建策略
        strategy = create_strategy('macd', {
            'strategy_name': 'test_factory_macd',
            'fast_period': 10,
            'slow_period': 20
        })
        
        self.assertIsInstance(strategy, MACDStrategy)
        self.assertEqual(strategy.get_name(), 'test_factory_macd')
        self.assertEqual(strategy.get_parameter('fast_period'), 10)
    
    def test_invalid_strategy_creation(self):
        """测试无效策略创建"""
        with self.assertRaises(Exception):
            create_strategy('nonexistent_strategy')
    
    def test_strategy_info(self):
        """测试策略信息获取"""
        info = strategy_factory.get_strategy_info('macd')
        
        self.assertIn('name', info)
        self.assertIn('strategy_class', info)
        self.assertIn('config_class', info)
        self.assertEqual(info['name'], 'macd')


class TestStrategyRegistry(unittest.TestCase):
    """策略注册器测试"""
    
    def test_strategy_search(self):
        """测试策略搜索"""
        # 按关键词搜索
        results = strategy_registry.search('macd')
        self.assertIn('macd', results)
        
        # 按分类搜索
        trend_strategies = strategy_registry.list_by_category('trend')
        self.assertIn('macd', trend_strategies)
    
    def test_registry_summary(self):
        """测试注册器摘要"""
        summary = strategy_registry.get_summary()
        
        self.assertIn('total_strategies', summary)
        self.assertIn('categories', summary)
        self.assertIn('strategies', summary)
        self.assertGreater(summary['total_strategies'], 0)


class TestStrategyPerformance(unittest.TestCase):
    """策略性能测试"""
    
    def setUp(self):
        """测试准备"""
        self.strategy = create_strategy('macd')
        
        # 创建大量测试数据
        dates = pd.date_range(start='2020-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        
        price = 100
        prices = [price]
        
        for _ in range(len(dates) - 1):
            change = np.random.normal(0, 0.01)
            price = price * (1 + change)
            prices.append(price)
        
        self.large_data = pd.DataFrame({
            'open': prices,
            'high': [p * 1.01 for p in prices],
            'low': [p * 0.99 for p in prices],
            'close': prices,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
    
    def test_large_dataset_performance(self):
        """测试大数据集性能"""
        import time
        
        start_time = time.time()
        signals = self.strategy.generate_signals(self.large_data)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        # 检查执行时间（应该在合理范围内）
        self.assertLess(execution_time, 5.0)  # 5秒内完成
        
        # 检查结果
        self.assertEqual(len(signals), len(self.large_data))
        
        print(f"大数据集处理时间: {execution_time:.2f}秒")
    
    def test_memory_usage(self):
        """测试内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 执行前内存
        mem_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行策略
        signals = self.strategy.generate_signals(self.large_data)
        
        # 执行后内存
        mem_after = process.memory_info().rss / 1024 / 1024  # MB
        
        mem_increase = mem_after - mem_before
        
        # 内存增长应该在合理范围内
        self.assertLess(mem_increase, 100)  # 不超过100MB
        
        print(f"内存增长: {mem_increase:.2f}MB")


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
