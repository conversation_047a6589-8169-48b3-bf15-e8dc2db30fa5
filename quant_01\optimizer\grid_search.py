"""
网格搜索优化器

通过遍历所有参数组合来寻找最优参数。
"""

import itertools
from typing import Dict, List, Any, Optional
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, as_completed
import pandas as pd

from .base import BaseOptimizer, OptimizationResult
from ..utils.logger import get_logger

logger = get_logger(__name__)


class GridSearchOptimizer(BaseOptimizer):
    """网格搜索优化器"""

    def __init__(
        self,
        strategy_class: type,
        param_space: Dict[str, List[Any]],
        objective: str = "sharpe_ratio",
        direction: str = "maximize",
        n_jobs: int = 1,
        random_state: Optional[int] = None,
        max_combinations: int = 10000
    ):
        """
        初始化网格搜索优化器

        Args:
            strategy_class: 策略类
            param_space: 参数空间
            objective: 优化目标
            direction: 优化方向
            n_jobs: 并行任务数
            random_state: 随机种子
            max_combinations: 最大参数组合数
        """
        super().__init__(strategy_class, param_space, objective, direction, n_jobs, random_state)

        self.max_combinations = max_combinations

        # 生成参数组合
        self.param_combinations = self._generate_combinations()

        # 检查组合数量
        if len(self.param_combinations) > max_combinations:
            logger.warning(f"参数组合数量 {len(self.param_combinations)} 超过限制 {max_combinations}")
            # 随机采样
            import random
            if random_state:
                random.seed(random_state)
            self.param_combinations = random.sample(self.param_combinations, max_combinations)

        logger.info(f"网格搜索优化器初始化完成: {len(self.param_combinations)} 个参数组合")

    def _generate_combinations(self) -> List[Dict[str, Any]]:
        """生成所有参数组合"""
        keys = list(self.param_space.keys())
        values = list(self.param_space.values())

        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)

        return combinations

    def optimize(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame] = None,
        early_stopping: bool = False,
        patience: int = 50,
        **kwargs
    ) -> OptimizationResult:
        """
        执行网格搜索优化

        Args:
            data: 训练数据
            validation_data: 验证数据
            early_stopping: 是否启用早停
            patience: 早停耐心值
            **kwargs: 其他参数

        Returns:
            优化结果
        """
        start_time = datetime.now()
        logger.info(f"开始网格搜索优化: {len(self.param_combinations)} 个组合")

        # 执行优化
        if self.n_jobs == 1:
            results = self._optimize_sequential(data, validation_data, early_stopping, patience)
        else:
            results = self._optimize_parallel(data, validation_data)

        # 分析结果
        optimization_result = self._analyze_results(results, data, validation_data)

        # 计算优化时间
        optimization_time = (datetime.now() - start_time).total_seconds()
        optimization_result.optimization_time = optimization_time

        logger.info(f"网格搜索完成: 最佳{self.objective} = {optimization_result.best_score:.4f}, 耗时: {optimization_time:.2f}秒")

        return optimization_result

    def _optimize_sequential(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame],
        early_stopping: bool,
        patience: int
    ) -> List[Dict[str, Any]]:
        """顺序优化"""
        results = []
        best_score = self._get_initial_best_score()
        no_improvement_count = 0

        for i, params in enumerate(self.param_combinations):
            try:
                # 评估训练集
                train_score = self._evaluate_strategy(params, data)

                # 评估验证集
                val_score = None
                if validation_data is not None:
                    val_score = self._evaluate_strategy(params, validation_data)

                result = {
                    'params': params,
                    'train_score': train_score,
                    'val_score': val_score,
                    'score': val_score if val_score is not None else train_score
                }

                results.append(result)

                # 早停检查
                if early_stopping and val_score is not None:
                    if self._is_better_score(val_score, best_score):
                        best_score = val_score
                        no_improvement_count = 0
                    else:
                        no_improvement_count += 1

                    if no_improvement_count >= patience:
                        logger.info(f"早停触发: {patience} 次无改善")
                        break

                # 进度报告
                if (i + 1) % 50 == 0:
                    logger.info(f"优化进度: {i + 1}/{len(self.param_combinations)}")

            except Exception as e:
                logger.warning(f"参数组合 {params} 评估失败: {e}")
                continue

        return results

    def _optimize_parallel(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> List[Dict[str, Any]]:
        """并行优化"""
        results = []

        with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
            # 提交任务
            future_to_params = {}

            for params in self.param_combinations:
                future = executor.submit(self._evaluate_params_wrapper, params, data, validation_data)
                future_to_params[future] = params

            # 收集结果
            completed = 0
            for future in as_completed(future_to_params):
                params = future_to_params[future]
                completed += 1

                try:
                    result = future.result()
                    if result:
                        results.append(result)

                    if completed % 50 == 0:
                        logger.info(f"并行优化进度: {completed}/{len(self.param_combinations)}")

                except Exception as e:
                    logger.warning(f"参数组合 {params} 并行评估失败: {e}")

        return results

    def _evaluate_params_wrapper(
        self,
        params: Dict[str, Any],
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> Optional[Dict[str, Any]]:
        """参数评估包装器（用于并行处理）"""
        try:
            train_score = self._evaluate_strategy(params, data)

            val_score = None
            if validation_data is not None:
                val_score = self._evaluate_strategy(params, validation_data)

            return {
                'params': params,
                'train_score': train_score,
                'val_score': val_score,
                'score': val_score if val_score is not None else train_score
            }
        except Exception as e:
            logger.warning(f"参数组合 {params} 评估失败: {e}")
            return None

    def _analyze_results(
        self,
        results: List[Dict[str, Any]],
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> OptimizationResult:
        """分析优化结果"""
        if not results:
            raise ValueError("没有有效的优化结果")

        # 找到最佳结果
        best_result = max(results, key=lambda x: x['score']) if self.direction == 'maximize' else min(results, key=lambda x: x['score'])

        # 计算过拟合分数
        overfitting_score = None
        if validation_data is not None and best_result['val_score'] is not None:
            overfitting_score = self._calculate_overfitting_score(
                best_result['train_score'],
                best_result['val_score']
            )

        return OptimizationResult(
            best_params=best_result['params'],
            best_score=best_result['score'],
            objective=self.objective,
            direction=self.direction,
            n_trials=len(results),
            optimization_time=0,  # 将在外部设置
            all_results=results,
            validation_score=best_result.get('val_score'),
            overfitting_score=overfitting_score
        )

    def get_optimization_summary(self, result: OptimizationResult) -> Dict[str, Any]:
        """获取优化摘要"""
        summary = {
            'method': 'Grid Search',
            'total_combinations': len(self.param_combinations),
            'evaluated_combinations': result.n_trials,
            'best_params': result.best_params,
            'best_score': result.best_score,
            'objective': result.objective,
            'optimization_time': result.optimization_time
        }

        if result.validation_score is not None:
            summary['validation_score'] = result.validation_score
            summary['overfitting_score'] = result.overfitting_score

        # 参数重要性
        param_importance = self.get_param_importance(result.all_results)
        summary['param_importance'] = param_importance

        return summary
