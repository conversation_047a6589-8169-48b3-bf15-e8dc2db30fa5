"""
数据库数据源实现

基于SQLAlchemy的数据库数据源，支持多种数据库。
"""

from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd

try:
    from sqlalchemy import create_engine, text
    from sqlalchemy.orm import sessionmaker
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

from .base import DataSeed
from ..core.config.backtest import FreqType, AssetType
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DatabaseDataSeed(DataSeed):
    """数据库数据源实现"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化数据库数据源
        
        Args:
            config: 数据库配置
                - url: 数据库连接URL
                - table_prefix: 表名前缀
                - schema: 数据库模式
        """
        if not SQLALCHEMY_AVAILABLE:
            raise ImportError("SQLAlchemy库未安装，请运行: pip install sqlalchemy")
        
        super().__init__(config)
        
        # 数据库配置
        self.db_url = self.config.get('url', 'sqlite:///quant_data.db')
        self.table_prefix = self.config.get('table_prefix', 'stock_')
        self.schema = self.config.get('schema', None)
        
        # 表名配置
        self.daily_table = f"{self.table_prefix}daily"
        self.minute_table = f"{self.table_prefix}minute"
        self.basic_info_table = f"{self.table_prefix}basic_info"
        self.universe_table = f"{self.table_prefix}universe"
        
        self.engine = None
        self.Session = None
        
        logger.info("数据库数据源初始化完成")
    
    def _setup(self):
        """初始化数据库连接"""
        try:
            self.engine = create_engine(
                self.db_url,
                echo=self.config.get('echo', False),
                pool_pre_ping=True
            )
            
            self.Session = sessionmaker(bind=self.engine)
            
            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            logger.info(f"数据库连接成功: {self.db_url}")
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def get_daily_data(
        self, 
        symbol: str, 
        start_date: Union[str, date, datetime], 
        end_date: Union[str, date, datetime],
        adjust: Optional[str] = None
    ) -> pd.DataFrame:
        """
        获取日线数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            adjust: 复权类型
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        if not self.engine:
            self._setup()
        
        try:
            # 构建查询SQL
            sql = f"""
            SELECT 
                trade_date as date,
                open_price as open,
                high_price as high,
                low_price as low,
                close_price as close,
                volume,
                amount,
                pct_chg,
                change_amount as change
            FROM {self.daily_table}
            WHERE symbol = :symbol
                AND trade_date >= :start_date
                AND trade_date <= :end_date
            ORDER BY trade_date
            """
            
            # 执行查询
            data = pd.read_sql(
                sql,
                self.engine,
                params={
                    'symbol': symbol,
                    'start_date': start_date,
                    'end_date': end_date
                }
            )
            
            if data.empty:
                logger.warning(f"未找到 {symbol} 的数据")
                return pd.DataFrame()
            
            # 设置日期索引
            data['date'] = pd.to_datetime(data['date'])
            data = data.set_index('date')
            
            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            data = data.dropna()
            
            logger.debug(f"从数据库获取 {symbol} 日线数据成功，共 {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"从数据库获取 {symbol} 日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_minute_data(
        self,
        symbol: str,
        date: Union[str, date, datetime],
        freq: FreqType = FreqType.MINUTE_1
    ) -> pd.DataFrame:
        """
        获取分钟级数据
        
        Args:
            symbol: 股票代码
            date: 日期
            freq: 数据频率
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        if not self.engine:
            self._setup()
        
        try:
            # 构建查询SQL
            sql = f"""
            SELECT 
                datetime,
                open_price as open,
                high_price as high,
                low_price as low,
                close_price as close,
                volume,
                amount
            FROM {self.minute_table}
            WHERE symbol = :symbol
                AND DATE(datetime) = :date
                AND freq = :freq
            ORDER BY datetime
            """
            
            # 执行查询
            data = pd.read_sql(
                sql,
                self.engine,
                params={
                    'symbol': symbol,
                    'date': date,
                    'freq': freq.value
                }
            )
            
            if data.empty:
                return pd.DataFrame()
            
            # 设置时间索引
            data['datetime'] = pd.to_datetime(data['datetime'])
            data = data.set_index('datetime')
            
            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            data = data.dropna()
            
            logger.debug(f"从数据库获取 {symbol} 分钟数据成功，共 {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"从数据库获取 {symbol} 分钟数据失败: {e}")
            return pd.DataFrame()
    
    def get_universe(
        self, 
        asset_type: AssetType = AssetType.STOCK,
        market: Optional[str] = None
    ) -> List[str]:
        """
        获取标的列表
        
        Args:
            asset_type: 资产类型
            market: 市场代码
            
        Returns:
            股票代码列表
        """
        if not self.engine:
            self._setup()
        
        try:
            # 构建查询SQL
            sql = f"""
            SELECT DISTINCT symbol
            FROM {self.universe_table}
            WHERE asset_type = :asset_type
            """
            
            params = {'asset_type': asset_type.value}
            
            if market:
                sql += " AND market = :market"
                params['market'] = market
            
            sql += " ORDER BY symbol"
            
            # 执行查询
            result = pd.read_sql(sql, self.engine, params=params)
            
            if result.empty:
                return []
            
            symbols = result['symbol'].tolist()
            
            logger.info(f"从数据库获取标的列表成功，共 {len(symbols)} 个")
            return symbols
            
        except Exception as e:
            logger.error(f"从数据库获取标的列表失败: {e}")
            return []
    
    def get_basic_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取股票基本信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            基本信息字典
        """
        if not self.engine:
            self._setup()
        
        try:
            sql = f"""
            SELECT *
            FROM {self.basic_info_table}
            WHERE symbol = :symbol
            """
            
            result = pd.read_sql(sql, self.engine, params={'symbol': symbol})
            
            if result.empty:
                return {}
            
            # 转换为字典
            info = result.iloc[0].to_dict()
            
            return info
            
        except Exception as e:
            logger.error(f"从数据库获取 {symbol} 基本信息失败: {e}")
            return {}
    
    def save_daily_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """
        保存日线数据到数据库
        
        Args:
            symbol: 股票代码
            data: 数据DataFrame
            
        Returns:
            是否成功
        """
        if not self.engine:
            self._setup()
        
        try:
            # 准备数据
            save_data = data.copy()
            save_data['symbol'] = symbol
            save_data['trade_date'] = save_data.index
            
            # 重命名列
            column_mapping = {
                'open': 'open_price',
                'high': 'high_price', 
                'low': 'low_price',
                'close': 'close_price',
                'change': 'change_amount'
            }
            save_data = save_data.rename(columns=column_mapping)
            
            # 保存到数据库
            save_data.to_sql(
                self.daily_table,
                self.engine,
                if_exists='append',
                index=False,
                method='multi'
            )
            
            logger.info(f"保存 {symbol} 日线数据到数据库成功，共 {len(save_data)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"保存 {symbol} 日线数据到数据库失败: {e}")
            return False
    
    def create_tables(self):
        """创建数据表"""
        if not self.engine:
            self._setup()
        
        try:
            # 创建日线数据表
            daily_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.daily_table} (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol VARCHAR(20) NOT NULL,
                trade_date DATE NOT NULL,
                open_price DECIMAL(10,3),
                high_price DECIMAL(10,3),
                low_price DECIMAL(10,3),
                close_price DECIMAL(10,3),
                volume BIGINT,
                amount DECIMAL(20,2),
                pct_chg DECIMAL(8,4),
                change_amount DECIMAL(10,3),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, trade_date)
            )
            """
            
            # 创建分钟数据表
            minute_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.minute_table} (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol VARCHAR(20) NOT NULL,
                datetime TIMESTAMP NOT NULL,
                freq VARCHAR(10) NOT NULL,
                open_price DECIMAL(10,3),
                high_price DECIMAL(10,3),
                low_price DECIMAL(10,3),
                close_price DECIMAL(10,3),
                volume BIGINT,
                amount DECIMAL(20,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, datetime, freq)
            )
            """
            
            # 创建基本信息表
            basic_info_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.basic_info_table} (
                symbol VARCHAR(20) PRIMARY KEY,
                name VARCHAR(100),
                market VARCHAR(10),
                industry VARCHAR(50),
                list_date DATE,
                market_cap DECIMAL(20,2),
                pe_ratio DECIMAL(8,2),
                pb_ratio DECIMAL(8,2),
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            
            # 创建标的列表表
            universe_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.universe_table} (
                symbol VARCHAR(20) NOT NULL,
                asset_type VARCHAR(20) NOT NULL,
                market VARCHAR(10),
                status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY(symbol, asset_type)
            )
            """
            
            # 执行SQL
            with self.engine.connect() as conn:
                conn.execute(text(daily_sql))
                conn.execute(text(minute_sql))
                conn.execute(text(basic_info_sql))
                conn.execute(text(universe_sql))
                conn.commit()
            
            logger.info("数据表创建成功")
            
        except Exception as e:
            logger.error(f"数据表创建失败: {e}")
            raise


__all__ = [
    "DatabaseDataSeed"
]
