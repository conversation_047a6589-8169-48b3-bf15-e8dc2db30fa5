"""
趋势指标

实现各种趋势分析技术指标。
"""

from typing import Tuple, Optional
import pandas as pd
import numpy as np

from ..logger import get_logger

logger = get_logger(__name__)


def sma(data: pd.Series, period: int) -> pd.Series:
    """
    简单移动平均线 (Simple Moving Average)
    
    Args:
        data: 价格序列
        period: 计算周期
        
    Returns:
        SMA序列
    """
    if len(data) < period:
        logger.warning(f"数据长度不足: {len(data)} < {period}")
        return pd.Series(index=data.index, dtype=float)
    
    return data.rolling(window=period, min_periods=period).mean()


def ema(data: pd.Series, period: int, alpha: Optional[float] = None) -> pd.Series:
    """
    指数移动平均线 (Exponential Moving Average)
    
    Args:
        data: 价格序列
        period: 计算周期
        alpha: 平滑系数，默认为 2/(period+1)
        
    Returns:
        EMA序列
    """
    if len(data) < period:
        logger.warning(f"数据长度不足: {len(data)} < {period}")
        return pd.Series(index=data.index, dtype=float)
    
    if alpha is None:
        alpha = 2.0 / (period + 1)
    
    return data.ewm(alpha=alpha, adjust=False).mean()


def macd(
    data: pd.Series, 
    fast_period: int = 12, 
    slow_period: int = 26, 
    signal_period: int = 9
) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    MACD指标 (Moving Average Convergence Divergence)
    
    Args:
        data: 价格序列
        fast_period: 快线周期
        slow_period: 慢线周期
        signal_period: 信号线周期
        
    Returns:
        (MACD线, 信号线, 柱状图)
    """
    if len(data) < slow_period:
        logger.warning(f"数据长度不足: {len(data)} < {slow_period}")
        empty_series = pd.Series(index=data.index, dtype=float)
        return empty_series, empty_series, empty_series
    
    # 计算快慢EMA
    ema_fast = ema(data, fast_period)
    ema_slow = ema(data, slow_period)
    
    # MACD线 = 快线 - 慢线
    macd_line = ema_fast - ema_slow
    
    # 信号线 = MACD线的EMA
    signal_line = ema(macd_line, signal_period)
    
    # 柱状图 = MACD线 - 信号线
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram


def bollinger_bands(
    data: pd.Series, 
    period: int = 20, 
    std_dev: float = 2.0
) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    布林带 (Bollinger Bands)
    
    Args:
        data: 价格序列
        period: 计算周期
        std_dev: 标准差倍数
        
    Returns:
        (上轨, 中轨, 下轨)
    """
    if len(data) < period:
        logger.warning(f"数据长度不足: {len(data)} < {period}")
        empty_series = pd.Series(index=data.index, dtype=float)
        return empty_series, empty_series, empty_series
    
    # 中轨 = 简单移动平均
    middle_band = sma(data, period)
    
    # 标准差
    rolling_std = data.rolling(window=period, min_periods=period).std()
    
    # 上轨 = 中轨 + 标准差 * 倍数
    upper_band = middle_band + (rolling_std * std_dev)
    
    # 下轨 = 中轨 - 标准差 * 倍数
    lower_band = middle_band - (rolling_std * std_dev)
    
    return upper_band, middle_band, lower_band


def dmi(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    period: int = 14
) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    方向性移动指标 (Directional Movement Index)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: 计算周期
        
    Returns:
        (DI+, DI-, ADX)
    """
    if len(high) < period + 1:
        logger.warning(f"数据长度不足: {len(high)} < {period + 1}")
        empty_series = pd.Series(index=high.index, dtype=float)
        return empty_series, empty_series, empty_series
    
    # 计算真实范围 (True Range)
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算方向性移动
    dm_plus = np.where((high - high.shift(1)) > (low.shift(1) - low), 
                       np.maximum(high - high.shift(1), 0), 0)
    dm_minus = np.where((low.shift(1) - low) > (high - high.shift(1)), 
                        np.maximum(low.shift(1) - low, 0), 0)
    
    dm_plus = pd.Series(dm_plus, index=high.index)
    dm_minus = pd.Series(dm_minus, index=high.index)
    
    # 计算平滑的TR和DM
    atr_value = tr.rolling(window=period).mean()
    dm_plus_smooth = dm_plus.rolling(window=period).mean()
    dm_minus_smooth = dm_minus.rolling(window=period).mean()
    
    # 计算DI
    di_plus = 100 * dm_plus_smooth / atr_value
    di_minus = 100 * dm_minus_smooth / atr_value
    
    # 计算ADX
    dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
    adx = dx.rolling(window=period).mean()
    
    return di_plus, di_minus, adx


def parabolic_sar(
    high: pd.Series, 
    low: pd.Series, 
    acceleration: float = 0.02, 
    maximum: float = 0.2
) -> pd.Series:
    """
    抛物线SAR (Parabolic Stop and Reverse)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        acceleration: 加速因子
        maximum: 最大加速因子
        
    Returns:
        SAR序列
    """
    if len(high) < 2:
        logger.warning("数据长度不足")
        return pd.Series(index=high.index, dtype=float)
    
    sar = pd.Series(index=high.index, dtype=float)
    trend = pd.Series(index=high.index, dtype=int)  # 1: 上升, -1: 下降
    af = pd.Series(index=high.index, dtype=float)
    ep = pd.Series(index=high.index, dtype=float)  # 极值点
    
    # 初始化
    sar.iloc[0] = low.iloc[0]
    trend.iloc[0] = 1
    af.iloc[0] = acceleration
    ep.iloc[0] = high.iloc[0]
    
    for i in range(1, len(high)):
        prev_sar = sar.iloc[i-1]
        prev_trend = trend.iloc[i-1]
        prev_af = af.iloc[i-1]
        prev_ep = ep.iloc[i-1]
        
        if prev_trend == 1:  # 上升趋势
            sar.iloc[i] = prev_sar + prev_af * (prev_ep - prev_sar)
            
            # 确保SAR不超过前两个周期的最低价
            if i >= 2:
                sar.iloc[i] = min(sar.iloc[i], low.iloc[i-1], low.iloc[i-2])
            else:
                sar.iloc[i] = min(sar.iloc[i], low.iloc[i-1])
            
            # 检查趋势反转
            if low.iloc[i] <= sar.iloc[i]:
                trend.iloc[i] = -1
                sar.iloc[i] = prev_ep
                af.iloc[i] = acceleration
                ep.iloc[i] = low.iloc[i]
            else:
                trend.iloc[i] = 1
                if high.iloc[i] > prev_ep:
                    ep.iloc[i] = high.iloc[i]
                    af.iloc[i] = min(prev_af + acceleration, maximum)
                else:
                    ep.iloc[i] = prev_ep
                    af.iloc[i] = prev_af
        
        else:  # 下降趋势
            sar.iloc[i] = prev_sar + prev_af * (prev_ep - prev_sar)
            
            # 确保SAR不超过前两个周期的最高价
            if i >= 2:
                sar.iloc[i] = max(sar.iloc[i], high.iloc[i-1], high.iloc[i-2])
            else:
                sar.iloc[i] = max(sar.iloc[i], high.iloc[i-1])
            
            # 检查趋势反转
            if high.iloc[i] >= sar.iloc[i]:
                trend.iloc[i] = 1
                sar.iloc[i] = prev_ep
                af.iloc[i] = acceleration
                ep.iloc[i] = high.iloc[i]
            else:
                trend.iloc[i] = -1
                if low.iloc[i] < prev_ep:
                    ep.iloc[i] = low.iloc[i]
                    af.iloc[i] = min(prev_af + acceleration, maximum)
                else:
                    ep.iloc[i] = prev_ep
                    af.iloc[i] = prev_af
    
    return sar


def ichimoku_cloud(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series,
    tenkan_period: int = 9,
    kijun_period: int = 26,
    senkou_b_period: int = 52,
    displacement: int = 26
) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series, pd.Series]:
    """
    一目均衡表 (Ichimoku Cloud)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        tenkan_period: 转换线周期
        kijun_period: 基准线周期
        senkou_b_period: 先行带B周期
        displacement: 位移周期
        
    Returns:
        (转换线, 基准线, 先行带A, 先行带B, 滞后线)
    """
    # 转换线 = (9日最高价 + 9日最低价) / 2
    tenkan_sen = (high.rolling(tenkan_period).max() + low.rolling(tenkan_period).min()) / 2
    
    # 基准线 = (26日最高价 + 26日最低价) / 2
    kijun_sen = (high.rolling(kijun_period).max() + low.rolling(kijun_period).min()) / 2
    
    # 先行带A = (转换线 + 基准线) / 2，向前位移26日
    senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(displacement)
    
    # 先行带B = (52日最高价 + 52日最低价) / 2，向前位移26日
    senkou_span_b = ((high.rolling(senkou_b_period).max() + 
                      low.rolling(senkou_b_period).min()) / 2).shift(displacement)
    
    # 滞后线 = 收盘价，向后位移26日
    chikou_span = close.shift(-displacement)
    
    return tenkan_sen, kijun_sen, senkou_span_a, senkou_span_b, chikou_span


__all__ = [
    "sma",
    "ema",
    "macd",
    "bollinger_bands",
    "dmi",
    "parabolic_sar",
    "ichimoku_cloud"
]
