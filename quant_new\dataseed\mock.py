"""
Mock数据源实现

用于测试和演示的模拟数据源。
"""

import random
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date, timedelta
import pandas as pd
import numpy as np

from .base import DataSeed
from ..core.config.backtest import FreqType, AssetType
from ..utils.logger import get_logger

logger = get_logger(__name__)


class MockDataSeed(DataSeed):
    """Mock数据源实现"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化Mock数据源
        
        Args:
            config: 配置参数
        """
        super().__init__(config)
        
        # 配置参数
        self.base_price = self.config.get('base_price', 100.0)
        self.volatility = self.config.get('volatility', 0.02)
        self.trend = self.config.get('trend', 0.0001)  # 日均涨幅
        self.volume_base = self.config.get('volume_base', 1000000)
        
        # 预定义股票列表
        self.stock_universe = [
            '000001', '000002', '000858', '002415', '002594',
            '600000', '600036', '600519', '600887', '601318'
        ]
        
        logger.info("Mock数据源初始化完成")
    
    def _generate_price_series(
        self, 
        start_price: float,
        periods: int,
        freq: str = 'D'
    ) -> pd.Series:
        """
        生成价格序列（几何布朗运动）
        
        Args:
            start_price: 起始价格
            periods: 周期数
            freq: 频率
            
        Returns:
            价格序列
        """
        # 根据频率调整参数
        if freq == 'D':  # 日线
            dt = 1/252  # 年化
            drift = self.trend
            vol = self.volatility
        elif freq in ['1min', '5min', '15min', '30min']:  # 分钟线
            dt = 1/(252*240)  # 年化（每天240分钟交易时间）
            drift = self.trend / 240
            vol = self.volatility / np.sqrt(240)
        else:
            dt = 1/252
            drift = self.trend
            vol = self.volatility
        
        # 生成随机游走
        random_shocks = np.random.normal(0, 1, periods)
        price_changes = drift * dt + vol * np.sqrt(dt) * random_shocks
        
        # 计算累积价格
        log_prices = np.log(start_price) + np.cumsum(price_changes)
        prices = np.exp(log_prices)
        
        return pd.Series(prices)
    
    def _generate_volume_series(self, periods: int) -> pd.Series:
        """生成成交量序列"""
        # 基础成交量 + 随机波动
        base_volumes = np.full(periods, self.volume_base)
        volume_noise = np.random.normal(1, 0.3, periods)
        volumes = base_volumes * np.abs(volume_noise)  # 确保成交量为正
        
        return pd.Series(volumes.astype(int))
    
    def get_daily_data(
        self, 
        symbol: str, 
        start_date: Union[str, date, datetime], 
        end_date: Union[str, date, datetime],
        adjust: Optional[str] = None
    ) -> pd.DataFrame:
        """
        获取日线数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            adjust: 复权类型（忽略）
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # 转换日期
            if isinstance(start_date, str):
                start_date = pd.to_datetime(start_date).date()
            elif isinstance(start_date, datetime):
                start_date = start_date.date()
                
            if isinstance(end_date, str):
                end_date = pd.to_datetime(end_date).date()
            elif isinstance(end_date, datetime):
                end_date = end_date.date()
            
            # 生成交易日
            date_range = pd.bdate_range(start=start_date, end=end_date, freq='B')
            periods = len(date_range)
            
            if periods == 0:
                return pd.DataFrame()
            
            # 设置随机种子（基于股票代码，确保可重复）
            seed = hash(symbol) % (2**32)
            np.random.seed(seed)
            
            # 生成价格序列
            close_prices = self._generate_price_series(self.base_price, periods, 'D')
            
            # 生成OHLC
            # 开盘价：前一日收盘价 + 小幅跳空
            open_prices = close_prices.shift(1).fillna(self.base_price)
            gap_ratio = np.random.normal(0, 0.005, periods)  # 跳空幅度
            open_prices = open_prices * (1 + gap_ratio)
            
            # 最高价和最低价
            intraday_range = np.random.uniform(0.01, 0.05, periods)  # 日内波动范围
            high_prices = np.maximum(open_prices, close_prices) * (1 + intraday_range/2)
            low_prices = np.minimum(open_prices, close_prices) * (1 - intraday_range/2)
            
            # 生成成交量
            volumes = self._generate_volume_series(periods)
            
            # 创建DataFrame
            data = pd.DataFrame({
                'open': open_prices,
                'high': high_prices,
                'low': low_prices,
                'close': close_prices,
                'volume': volumes
            }, index=date_range)
            
            # 计算成交额
            data['amount'] = data['close'] * data['volume']
            
            # 计算涨跌幅
            data['pct_chg'] = data['close'].pct_change() * 100
            
            # 移除空值
            data = data.dropna()
            
            logger.debug(f"生成 {symbol} Mock日线数据成功，共 {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"生成 {symbol} Mock日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_minute_data(
        self,
        symbol: str,
        date: Union[str, date, datetime],
        freq: FreqType = FreqType.MINUTE_1
    ) -> pd.DataFrame:
        """
        获取分钟级数据
        
        Args:
            symbol: 股票代码
            date: 日期
            freq: 数据频率
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # 转换日期
            if isinstance(date, str):
                date = pd.to_datetime(date).date()
            elif isinstance(date, datetime):
                date = date.date()
            
            # 生成交易时间
            morning_start = pd.Timestamp.combine(date, pd.Timestamp('09:30:00').time())
            morning_end = pd.Timestamp.combine(date, pd.Timestamp('11:30:00').time())
            afternoon_start = pd.Timestamp.combine(date, pd.Timestamp('13:00:00').time())
            afternoon_end = pd.Timestamp.combine(date, pd.Timestamp('15:00:00').time())
            
            # 根据频率生成时间序列
            freq_map = {
                FreqType.MINUTE_1: '1min',
                FreqType.MINUTE_5: '5min',
                FreqType.MINUTE_15: '15min',
                FreqType.MINUTE_30: '30min'
            }
            
            freq_str = freq_map.get(freq, '1min')
            
            # 生成上午和下午的时间序列
            morning_times = pd.date_range(morning_start, morning_end, freq=freq_str)
            afternoon_times = pd.date_range(afternoon_start, afternoon_end, freq=freq_str)
            
            # 合并时间序列
            time_index = morning_times.union(afternoon_times)
            periods = len(time_index)
            
            if periods == 0:
                return pd.DataFrame()
            
            # 设置随机种子
            seed = hash(f"{symbol}_{date}") % (2**32)
            np.random.seed(seed)
            
            # 生成价格序列
            close_prices = self._generate_price_series(self.base_price, periods, freq_str)
            
            # 生成OHLC
            open_prices = close_prices.shift(1).fillna(self.base_price)
            
            # 分钟级别的波动更小
            intraday_range = np.random.uniform(0.001, 0.01, periods)
            high_prices = np.maximum(open_prices, close_prices) * (1 + intraday_range/2)
            low_prices = np.minimum(open_prices, close_prices) * (1 - intraday_range/2)
            
            # 生成成交量（分钟级别更小）
            minute_volume_base = self.volume_base // (240 // int(freq_str.replace('min', '')))
            volumes = self._generate_volume_series(periods) // 10
            
            # 创建DataFrame
            data = pd.DataFrame({
                'open': open_prices,
                'high': high_prices,
                'low': low_prices,
                'close': close_prices,
                'volume': volumes
            }, index=time_index)
            
            data['amount'] = data['close'] * data['volume']
            data = data.dropna()
            
            logger.debug(f"生成 {symbol} Mock分钟数据成功，共 {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"生成 {symbol} Mock分钟数据失败: {e}")
            return pd.DataFrame()
    
    def get_universe(
        self, 
        asset_type: AssetType = AssetType.STOCK,
        market: Optional[str] = None
    ) -> List[str]:
        """
        获取标的列表
        
        Args:
            asset_type: 资产类型
            market: 市场代码
            
        Returns:
            股票代码列表
        """
        if asset_type == AssetType.STOCK:
            symbols = self.stock_universe.copy()
            
            # 按市场过滤
            if market:
                if market.lower() == 'sh':
                    symbols = [s for s in symbols if s.startswith('6')]
                elif market.lower() == 'sz':
                    symbols = [s for s in symbols if s.startswith(('0', '3'))]
            
            return symbols
        
        return []
    
    def get_basic_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取股票基本信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            基本信息字典
        """
        # 模拟基本信息
        info = {
            'symbol': symbol,
            'name': f'股票{symbol}',
            'market': 'SH' if symbol.startswith('6') else 'SZ',
            'industry': '制造业',
            'list_date': '2010-01-01',
            'market_cap': random.uniform(10, 1000) * 100000000,  # 市值
            'pe_ratio': random.uniform(10, 50),  # 市盈率
            'pb_ratio': random.uniform(1, 10),   # 市净率
        }
        
        return info
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """
        获取最新价格
        
        Args:
            symbol: 股票代码
            
        Returns:
            最新价格
        """
        # 生成随机价格
        seed = hash(symbol) % (2**32)
        random.seed(seed)
        
        return round(random.uniform(10, 200), 2)


__all__ = [
    "MockDataSeed"
]
