"""
装饰器工具

提供常用的装饰器功能。
"""

import time
import functools
from typing import Any, Callable, Optional, Union
from .cache import cache_manager
from .logger import get_logger

logger = get_logger(__name__)


def cache_result(
    ttl: int = 3600,
    cache_type: str = "memory",
    key_func: Optional[Callable] = None
):
    """
    缓存结果装饰器
    
    Args:
        ttl: 缓存生存时间（秒）
        cache_type: 缓存类型 ("memory", "disk", "both")
        key_func: 自定义键生成函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__module__}.{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {func.__name__}")
                return cached_result
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 存储到缓存
            cache_manager.set(cache_key, result, ttl)
            logger.debug(f"结果已缓存: {func.__name__}")
            
            return result
        
        return wrapper
    return decorator


def timing(func: Callable) -> Callable:
    """
    计时装饰器
    
    记录函数执行时间。
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            logger.debug(f"函数执行完成: {func.__name__}, 耗时: {execution_time:.4f}秒")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"函数执行失败: {func.__name__}, 耗时: {execution_time:.4f}秒, 错误: {e}")
            raise
    
    return wrapper


def retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟倍数
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                    
                except exceptions as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"函数重试失败: {func.__name__}, 最大重试次数已达到")
                        raise
                    
                    logger.warning(f"函数执行失败，第{attempt + 1}次重试: {func.__name__}, 错误: {e}")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            return None  # 不应该到达这里
        
        return wrapper
    return decorator


def validate_args(**validators):
    """
    参数验证装饰器
    
    Args:
        **validators: 参数验证器字典
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数签名
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证参数
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    if not validator(value):
                        raise ValueError(f"参数验证失败: {param_name} = {value}")
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def log_calls(level: str = "DEBUG"):
    """
    函数调用日志装饰器
    
    Args:
        level: 日志级别
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger_func = getattr(logger, level.lower())
            
            # 记录函数调用
            args_str = ", ".join([str(arg) for arg in args])
            kwargs_str = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
            params_str = ", ".join(filter(None, [args_str, kwargs_str]))
            
            logger_func(f"调用函数: {func.__name__}({params_str})")
            
            try:
                result = func(*args, **kwargs)
                logger_func(f"函数返回: {func.__name__} -> {type(result).__name__}")
                return result
                
            except Exception as e:
                logger.error(f"函数异常: {func.__name__} -> {e}")
                raise
        
        return wrapper
    return decorator


def singleton(cls):
    """
    单例装饰器
    
    确保类只有一个实例。
    """
    instances = {}
    
    @functools.wraps(cls)
    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]
    
    return get_instance


def deprecated(reason: str = ""):
    """
    废弃警告装饰器
    
    Args:
        reason: 废弃原因
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            warning_msg = f"函数 {func.__name__} 已废弃"
            if reason:
                warning_msg += f": {reason}"
            
            logger.warning(warning_msg)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def rate_limit(calls: int, period: int):
    """
    速率限制装饰器
    
    Args:
        calls: 允许的调用次数
        period: 时间周期（秒）
    """
    def decorator(func: Callable) -> Callable:
        call_times = []
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            now = time.time()
            
            # 清理过期的调用记录
            call_times[:] = [t for t in call_times if now - t < period]
            
            # 检查是否超过限制
            if len(call_times) >= calls:
                raise RuntimeError(f"速率限制: {calls}次调用/{period}秒")
            
            # 记录本次调用
            call_times.append(now)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


# 便捷的装饰器组合
def performance_monitor(func: Callable) -> Callable:
    """
    性能监控装饰器组合
    
    结合计时和日志功能。
    """
    return timing(log_calls("INFO")(func))


def robust_function(
    max_attempts: int = 3,
    cache_ttl: int = 300,
    log_level: str = "DEBUG"
):
    """
    健壮函数装饰器组合
    
    结合重试、缓存和日志功能。
    """
    def decorator(func: Callable) -> Callable:
        return retry(max_attempts)(
            cache_result(cache_ttl)(
                log_calls(log_level)(func)
            )
        )
    
    return decorator
