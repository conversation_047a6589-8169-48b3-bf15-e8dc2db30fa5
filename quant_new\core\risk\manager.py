"""
风险管理器

统一的风险控制管理器，整合各种风险控制功能。
"""

from typing import Dict, List, Optional, Any, Callable
import pandas as pd
import numpy as np

from ..config.risk import RiskConfig
from ...utils.logger import get_logger

logger = get_logger(__name__)


class RiskManager:
    """风险管理器"""
    
    def __init__(self, config: RiskConfig):
        """
        初始化风险管理器
        
        Args:
            config: 风险配置
        """
        self.config = config
        self.rules = []
        self.alerts = []
        
        # 风险状态
        self.risk_metrics = {}
        self.position_limits = {}
        self.emergency_stop = False
        
        # 初始化风险规则
        self._setup_risk_rules()
        
        logger.info("风险管理器初始化完成")
    
    def _setup_risk_rules(self):
        """设置风险规则"""
        if not self.config.enabled:
            return
        
        # 添加基础风险规则
        if self.config.limits.max_drawdown_limit > 0:
            self.add_rule(self._max_drawdown_rule)
        
        if self.config.limits.daily_loss_limit > 0:
            self.add_rule(self._daily_loss_rule)
        
        if self.config.limits.max_volatility > 0:
            self.add_rule(self._volatility_rule)
        
        if self.config.limits.var_limit > 0:
            self.add_rule(self._var_rule)
    
    def add_rule(self, rule: Callable):
        """
        添加风险规则
        
        Args:
            rule: 风险规则函数
        """
        self.rules.append(rule)
        logger.debug(f"添加风险规则: {rule.__name__}")
    
    def remove_rule(self, rule: Callable):
        """
        移除风险规则
        
        Args:
            rule: 风险规则函数
        """
        if rule in self.rules:
            self.rules.remove(rule)
            logger.debug(f"移除风险规则: {rule.__name__}")
    
    def apply_risk_control(
        self,
        positions: pd.DataFrame,
        price_data: pd.DataFrame,
        portfolio_value: float
    ) -> pd.DataFrame:
        """
        应用风险控制
        
        Args:
            positions: 持仓数据
            price_data: 价格数据
            portfolio_value: 组合价值
            
        Returns:
            调整后的持仓数据
        """
        if not self.config.enabled:
            return positions
        
        # 检查紧急停止
        if self.emergency_stop:
            logger.warning("紧急停止激活，清空所有持仓")
            return self._clear_all_positions(positions)
        
        adjusted_positions = positions.copy()
        
        # 应用所有风险规则
        for rule in self.rules:
            try:
                adjusted_positions = rule(
                    adjusted_positions, 
                    price_data, 
                    portfolio_value
                )
            except Exception as e:
                logger.error(f"风险规则 {rule.__name__} 执行失败: {e}")
        
        # 检查仓位限制
        adjusted_positions = self._apply_position_limits(adjusted_positions)
        
        return adjusted_positions
    
    def _max_drawdown_rule(
        self,
        positions: pd.DataFrame,
        price_data: pd.DataFrame,
        portfolio_value: float
    ) -> pd.DataFrame:
        """最大回撤规则"""
        # 计算当前回撤
        if 'portfolio_peak' not in self.risk_metrics:
            self.risk_metrics['portfolio_peak'] = portfolio_value
        
        peak = self.risk_metrics['portfolio_peak']
        if portfolio_value > peak:
            self.risk_metrics['portfolio_peak'] = portfolio_value
            peak = portfolio_value
        
        current_drawdown = (peak - portfolio_value) / peak
        
        # 检查是否超过限制
        if current_drawdown > self.config.limits.max_drawdown_limit:
            logger.warning(f"最大回撤超限: {current_drawdown:.2%} > {self.config.limits.max_drawdown_limit:.2%}")
            
            # 减少仓位
            reduction_factor = 1 - (current_drawdown - self.config.limits.max_drawdown_limit) * 2
            reduction_factor = max(0.1, reduction_factor)  # 最少保留10%仓位
            
            if 'position' in positions.columns:
                positions['position'] *= reduction_factor
            
            self._add_alert("最大回撤超限", {
                'current_drawdown': current_drawdown,
                'limit': self.config.limits.max_drawdown_limit,
                'action': f'仓位减少至 {reduction_factor:.1%}'
            })
        
        return positions
    
    def _daily_loss_rule(
        self,
        positions: pd.DataFrame,
        price_data: pd.DataFrame,
        portfolio_value: float
    ) -> pd.DataFrame:
        """单日损失规则"""
        # 获取今日开始价值
        today = price_data.index[-1].date() if not price_data.empty else None
        
        if today:
            daily_key = f'daily_start_{today}'
            if daily_key not in self.risk_metrics:
                self.risk_metrics[daily_key] = portfolio_value
            
            daily_start_value = self.risk_metrics[daily_key]
            daily_loss = (daily_start_value - portfolio_value) / daily_start_value
            
            # 检查是否超过限制
            if daily_loss > self.config.limits.daily_loss_limit:
                logger.warning(f"单日损失超限: {daily_loss:.2%} > {self.config.limits.daily_loss_limit:.2%}")
                
                # 清空所有持仓
                positions = self._clear_all_positions(positions)
                
                self._add_alert("单日损失超限", {
                    'daily_loss': daily_loss,
                    'limit': self.config.limits.daily_loss_limit,
                    'action': '清空所有持仓'
                })
        
        return positions
    
    def _volatility_rule(
        self,
        positions: pd.DataFrame,
        price_data: pd.DataFrame,
        portfolio_value: float
    ) -> pd.DataFrame:
        """波动率规则"""
        if len(price_data) < 20:  # 需要足够的数据计算波动率
            return positions
        
        # 计算20日波动率
        returns = price_data['close'].pct_change().dropna()
        if len(returns) >= 20:
            volatility = returns.rolling(20).std().iloc[-1] * np.sqrt(252)
            
            if volatility > self.config.limits.max_volatility:
                logger.warning(f"波动率超限: {volatility:.2%} > {self.config.limits.max_volatility:.2%}")
                
                # 根据波动率调整仓位
                vol_adjustment = self.config.limits.max_volatility / volatility
                vol_adjustment = min(1.0, vol_adjustment)
                
                if 'position' in positions.columns:
                    positions['position'] *= vol_adjustment
                
                self._add_alert("波动率超限", {
                    'current_volatility': volatility,
                    'limit': self.config.limits.max_volatility,
                    'adjustment': vol_adjustment
                })
        
        return positions
    
    def _var_rule(
        self,
        positions: pd.DataFrame,
        price_data: pd.DataFrame,
        portfolio_value: float
    ) -> pd.DataFrame:
        """VaR规则"""
        if len(price_data) < 30:  # 需要足够的数据计算VaR
            return positions
        
        # 计算VaR
        returns = price_data['close'].pct_change().dropna()
        if len(returns) >= 30:
            var_95 = returns.quantile(1 - self.config.limits.var_confidence)
            
            if abs(var_95) > self.config.limits.var_limit:
                logger.warning(f"VaR超限: {abs(var_95):.2%} > {self.config.limits.var_limit:.2%}")
                
                # 调整仓位
                var_adjustment = self.config.limits.var_limit / abs(var_95)
                var_adjustment = min(1.0, var_adjustment)
                
                if 'position' in positions.columns:
                    positions['position'] *= var_adjustment
                
                self._add_alert("VaR超限", {
                    'current_var': abs(var_95),
                    'limit': self.config.limits.var_limit,
                    'adjustment': var_adjustment
                })
        
        return positions
    
    def _apply_position_limits(self, positions: pd.DataFrame) -> pd.DataFrame:
        """应用仓位限制"""
        if 'position' in positions.columns:
            # 单个标的最大仓位限制
            max_single = self.config.position.max_single_position
            positions['position'] = positions['position'].clip(-max_single, max_single)
            
            # 总仓位限制
            total_position = positions['position'].abs().sum()
            max_total = self.config.position.max_position_ratio
            
            if total_position > max_total:
                scale_factor = max_total / total_position
                positions['position'] *= scale_factor
                
                logger.info(f"总仓位超限，按比例缩减: {scale_factor:.2%}")
        
        return positions
    
    def _clear_all_positions(self, positions: pd.DataFrame) -> pd.DataFrame:
        """清空所有持仓"""
        if 'position' in positions.columns:
            positions['position'] = 0.0
        return positions
    
    def _add_alert(self, alert_type: str, details: Dict[str, Any]):
        """添加风险警报"""
        alert = {
            'timestamp': pd.Timestamp.now(),
            'type': alert_type,
            'details': details
        }
        
        self.alerts.append(alert)
        
        # 保持最近100条警报
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
        
        logger.warning(f"风险警报: {alert_type} - {details}")
    
    def check_emergency_conditions(
        self,
        portfolio_value: float,
        initial_value: float
    ) -> bool:
        """
        检查紧急停止条件
        
        Args:
            portfolio_value: 当前组合价值
            initial_value: 初始组合价值
            
        Returns:
            是否触发紧急停止
        """
        if not self.config.emergency_stop:
            return False
        
        # 检查强制平仓阈值
        total_loss = (initial_value - portfolio_value) / initial_value
        
        if total_loss >= self.config.liquidation_threshold:
            logger.critical(f"触发紧急停止: 总损失 {total_loss:.2%} >= {self.config.liquidation_threshold:.2%}")
            self.emergency_stop = True
            
            self._add_alert("紧急停止", {
                'total_loss': total_loss,
                'threshold': self.config.liquidation_threshold,
                'action': '强制平仓'
            })
            
            return True
        
        return False
    
    def get_risk_report(self) -> Dict[str, Any]:
        """
        获取风险报告
        
        Returns:
            风险报告字典
        """
        return {
            'risk_metrics': self.risk_metrics.copy(),
            'recent_alerts': self.alerts[-10:],  # 最近10条警报
            'emergency_stop': self.emergency_stop,
            'active_rules': len(self.rules),
            'config': {
                'enabled': self.config.enabled,
                'max_drawdown_limit': self.config.limits.max_drawdown_limit,
                'daily_loss_limit': self.config.limits.daily_loss_limit,
                'max_volatility': self.config.limits.max_volatility,
                'var_limit': self.config.limits.var_limit
            }
        }
    
    def reset_emergency_stop(self):
        """重置紧急停止状态"""
        self.emergency_stop = False
        logger.info("紧急停止状态已重置")
    
    def clear_alerts(self):
        """清空警报"""
        self.alerts.clear()
        logger.info("风险警报已清空")


__all__ = [
    "RiskManager"
]
