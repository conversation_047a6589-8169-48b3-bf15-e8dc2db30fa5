"""
量化回测引擎基类

提供统一的回测引擎接口和核心功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd
import time

from ..config.global_config import GlobalConfig
from ..structures.market import OHLCV, MarketData
from ..structures.portfolio import Portfolio, StrategyResult
from ..interfaces import IDataSource, IStrategy, IEngine
from ..exceptions import EngineException, BacktestException


class QuantEngine(IEngine):
    """量化回测引擎

    统一的回测引擎接口，支持单资产和多资产回测。

    优化特性：
    - 插件化架构
    - 高性能计算
    - 智能缓存
    - 并行处理
    - 完整监控
    """

    def __init__(
        self,
        data_source: Optional[IDataSource] = None,
        config: Optional[GlobalConfig] = None
    ):
        """
        初始化量化引擎

        Args:
            data_source: 数据源
            config: 全局配置
        """
        self.config = config or GlobalConfig.create_default()
        self.data_source = data_source

        # 内部状态
        self._strategies: Dict[str, IStrategy] = {}
        self._results: Dict[str, StrategyResult] = {}
        self._market_data: Optional[MarketData] = None

        # 性能统计
        self._stats = {
            'total_backtests': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'last_run': None
        }

        # 初始化日志
        self._init_logger()
        self.logger.info("量化引擎初始化完成")

    def _init_logger(self):
        """初始化日志"""
        from ...utils.logger import get_logger
        self.logger = get_logger(__name__)

    def set_data_source(self, data_source: IDataSource):
        """设置数据源"""
        self.data_source = data_source
        self.logger.info(f"数据源已设置: {data_source.__class__.__name__}")

    def register_strategy(self, name: str, strategy: IStrategy):
        """注册策略"""
        self._strategies[name] = strategy
        self.logger.info(f"策略已注册: {name}")

    def get_strategy(self, name: str) -> Optional[IStrategy]:
        """获取策略"""
        return self._strategies.get(name)

    def list_strategies(self) -> List[str]:
        """列出所有策略"""
        return list(self._strategies.keys())

    def run_backtest(
        self,
        strategy: Union[str, IStrategy],
        symbol: str,
        start_date: str,
        end_date: str,
        **kwargs
    ) -> StrategyResult:
        """
        运行回测

        Args:
            strategy: 策略名称或策略对象
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 其他参数

        Returns:
            回测结果
        """
        start_time = time.time()

        try:
            # 获取策略对象
            if isinstance(strategy, str):
                strategy_obj = self.get_strategy(strategy)
                if strategy_obj is None:
                    raise EngineException(f"策略未注册: {strategy}")
            else:
                strategy_obj = strategy

            # 检查数据源
            if self.data_source is None:
                raise EngineException("未设置数据源")

            self.logger.info(f"开始回测: {symbol}, 策略: {strategy_obj.get_name()}")

            # 获取数据
            data = self._get_data(symbol, start_date, end_date)

            # 验证数据
            self._validate_data(data)

            # 初始化策略
            strategy_obj.initialize()

            # 生成交易信号
            signals = strategy_obj.generate_signals(data)

            # 执行回测
            result = self._execute_backtest(data, signals, strategy_obj, symbol)

            # 更新统计信息
            execution_time = time.time() - start_time
            self._update_stats(execution_time)

            # 保存结果
            result_key = f"{symbol}_{strategy_obj.get_name()}"
            self._results[result_key] = result

            self.logger.info(f"回测完成: {symbol}, 耗时: {execution_time:.2f}秒")

            return result

        except Exception as e:
            self.logger.error(f"回测失败: {e}")
            raise BacktestException(f"回测执行失败: {e}")

    def _get_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """获取数据"""
        try:
            data = self.data_source.get_data(symbol, start_date, end_date)
            if data.empty:
                raise EngineException(f"数据为空: {symbol}")
            return data
        except Exception as e:
            raise EngineException(f"数据获取失败: {e}")

    def _validate_data(self, data: pd.DataFrame):
        """验证数据"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]

        if missing_columns:
            raise EngineException(f"数据缺少必要列: {missing_columns}")

        if data.isnull().any().any():
            self.logger.warning("数据包含空值，将进行前向填充")
            data.fillna(method='ffill', inplace=True)

    def _execute_backtest(
        self,
        data: pd.DataFrame,
        signals: pd.DataFrame,
        strategy: IStrategy,
        symbol: str
    ) -> StrategyResult:
        """执行回测"""
        # 创建投资组合
        portfolio = Portfolio(
            portfolio_id=f"backtest_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            name=f"{strategy.get_name()}_{symbol}",
            initial_capital=100000.0,  # 默认初始资金
            timestamp=datetime.now()
        )

        # 模拟交易执行
        for i, (timestamp, signal_row) in enumerate(signals.iterrows()):
            if i == 0:
                continue  # 跳过第一行

            current_price = data.loc[timestamp, 'close']

            # 处理买入信号
            if signal_row.get('buy_signal', False):
                self._execute_buy_order(portfolio, symbol, current_price, timestamp)

            # 处理卖出信号
            elif signal_row.get('sell_signal', False):
                self._execute_sell_order(portfolio, symbol, current_price, timestamp)

            # 更新组合价值
            portfolio.update_value_history(timestamp, portfolio.total_value)

        # 计算回测结果
        result = self._calculate_result(portfolio, data, strategy, symbol)

        return result

    def _execute_buy_order(self, portfolio: Portfolio, symbol: str, price: float, timestamp: datetime):
        """执行买入订单"""
        # 简化实现：使用固定金额买入
        trade_amount = 10000.0  # 固定交易金额
        quantity = trade_amount / price

        from ..structures.trade import Trade, OrderSide

        trade = Trade(
            trade_id=f"buy_{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            side=OrderSide.BUY,
            quantity=quantity,
            price=price,
            timestamp=timestamp,
            commission=trade_amount * 0.0003  # 手续费
        )

        portfolio.add_trade(trade)

    def _execute_sell_order(self, portfolio: Portfolio, symbol: str, price: float, timestamp: datetime):
        """执行卖出订单"""
        position = portfolio.get_position(symbol)
        if position is None or position.is_flat:
            return  # 没有持仓，无法卖出

        from ..structures.trade import Trade, OrderSide

        trade = Trade(
            trade_id=f"sell_{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            side=OrderSide.SELL,
            quantity=position.quantity,
            price=price,
            timestamp=timestamp,
            commission=position.quantity * price * 0.0003  # 手续费
        )

        portfolio.add_trade(trade)

    def _calculate_result(
        self,
        portfolio: Portfolio,
        data: pd.DataFrame,
        strategy: IStrategy,
        symbol: str
    ) -> StrategyResult:
        """计算回测结果"""
        # 计算基本指标
        total_return = portfolio.total_return

        # 计算年化收益率
        days = len(data)
        annual_return = (1 + total_return) ** (252 / days) - 1 if days > 0 else 0

        # 计算其他指标
        metrics = portfolio.calculate_metrics()

        result = StrategyResult(
            strategy_id=strategy.get_name(),
            symbol=symbol,
            start_date=data.index[0],
            end_date=data.index[-1],
            total_return=total_return,
            annual_return=annual_return,
            volatility=metrics.get('volatility', 0),
            sharpe_ratio=metrics.get('sharpe_ratio', 0),
            max_drawdown=metrics.get('max_drawdown', 0),
            total_trades=len(portfolio.trades),
            win_rate=metrics.get('win_rate', 0),
            portfolio=portfolio,
            equity_curve=portfolio.value_history,
            trades=portfolio.trades
        )

        return result

    def _update_stats(self, execution_time: float):
        """更新统计信息"""
        self._stats['total_backtests'] += 1
        self._stats['total_time'] += execution_time
        self._stats['avg_time'] = self._stats['total_time'] / self._stats['total_backtests']
        self._stats['last_run'] = datetime.now()

    def set_initial_capital(self, capital: float):
        """设置初始资金"""
        self.initial_capital = capital

    def set_commission(self, commission: float):
        """设置手续费"""
        self.commission = commission

    def get_result(self, key: str) -> Optional[StrategyResult]:
        """获取回测结果"""
        return self._results.get(key)

    def list_results(self) -> List[str]:
        """列出所有结果"""
        return list(self._results.keys())

    def clear_results(self):
        """清空结果"""
        self._results.clear()
        self.logger.info("回测结果已清空")

    def get_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        stats = self._stats.copy()

        # 添加额外信息
        stats['registered_strategies'] = len(self._strategies)
        stats['cached_results'] = len(self._results)
        stats['data_source'] = self.data_source.__class__.__name__ if self.data_source else None

        return stats
