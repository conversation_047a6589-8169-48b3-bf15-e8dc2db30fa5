"""
HTML报告生成器

生成美观的HTML格式回测报告。
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
import numpy as np

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

from .generator import ReportGenerator
from ...strategies.base import StrategyResult
from ...utils.logger import get_logger

logger = get_logger(__name__)


class HTMLReportGenerator(ReportGenerator):
    """HTML报告生成器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化HTML报告生成器
        
        Args:
            config: 配置参数
        """
        super().__init__(config)
        
        if not PLOTLY_AVAILABLE:
            logger.warning("Plotly未安装，图表功能将受限")
        
        # 确保输出目录存在
        os.makedirs(self.output_path, exist_ok=True)
    
    def generate_single_strategy_report(
        self,
        result: StrategyResult,
        data: pd.DataFrame,
        output_file: str,
        **kwargs
    ) -> str:
        """
        生成单策略HTML报告
        
        Args:
            result: 策略结果
            data: 原始数据
            output_file: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            生成的报告文件路径
        """
        try:
            logger.info(f"开始生成HTML报告: {output_file}")
            
            # 计算绩效指标
            metrics = self.calculate_performance_metrics(result)
            
            # 生成HTML内容
            html_content = self._create_single_strategy_html(result, data, metrics, **kwargs)
            
            # 保存文件
            output_path = os.path.join(self.output_path, output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML报告生成完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"HTML报告生成失败: {e}")
            raise
    
    def generate_multi_strategy_report(
        self,
        results: Dict[str, StrategyResult],
        output_file: str,
        **kwargs
    ) -> str:
        """
        生成多策略对比HTML报告
        
        Args:
            results: 多个策略结果
            output_file: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            生成的报告文件路径
        """
        try:
            logger.info(f"开始生成多策略对比HTML报告: {output_file}")
            
            # 生成HTML内容
            html_content = self._create_multi_strategy_html(results, **kwargs)
            
            # 保存文件
            output_path = os.path.join(self.output_path, output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"多策略对比HTML报告生成完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"多策略对比HTML报告生成失败: {e}")
            raise
    
    def generate_optimization_report(
        self,
        optimization_result: Dict[str, Any],
        output_file: str,
        **kwargs
    ) -> str:
        """
        生成参数优化HTML报告
        
        Args:
            optimization_result: 优化结果
            output_file: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            生成的报告文件路径
        """
        try:
            logger.info(f"开始生成参数优化HTML报告: {output_file}")
            
            # 生成HTML内容
            html_content = self._create_optimization_html(optimization_result, **kwargs)
            
            # 保存文件
            output_path = os.path.join(self.output_path, output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"参数优化HTML报告生成完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"参数优化HTML报告生成失败: {e}")
            raise
    
    def _create_single_strategy_html(
        self,
        result: StrategyResult,
        data: pd.DataFrame,
        metrics: Dict[str, Any],
        **kwargs
    ) -> str:
        """创建单策略HTML内容"""
        
        # HTML模板
        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>量化策略回测报告 - {strategy_name}</title>
            <style>
                {css_styles}
            </style>
        </head>
        <body>
            <div class="container">
                <header>
                    <h1>量化策略回测报告</h1>
                    <div class="report-info">
                        <p><strong>策略名称:</strong> {strategy_name}</p>
                        <p><strong>标的代码:</strong> {symbol}</p>
                        <p><strong>回测期间:</strong> {start_date} 至 {end_date}</p>
                        <p><strong>生成时间:</strong> {generate_time}</p>
                    </div>
                </header>
                
                <section class="summary">
                    <h2>策略概览</h2>
                    <div class="metrics-grid">
                        {metrics_cards}
                    </div>
                </section>
                
                <section class="charts">
                    <h2>图表分析</h2>
                    {charts_content}
                </section>
                
                <section class="detailed-metrics">
                    <h2>详细指标</h2>
                    {detailed_metrics_table}
                </section>
                
                <section class="trade-analysis">
                    <h2>交易分析</h2>
                    {trade_analysis_content}
                </section>
                
                <footer>
                    <p>报告由量化回测引擎生成 | 生成时间: {generate_time}</p>
                </footer>
            </div>
        </body>
        </html>
        """
        
        # CSS样式
        css_styles = self._get_css_styles()
        
        # 生成各部分内容
        metrics_cards = self._create_metrics_cards(metrics)
        charts_content = self._create_charts(result, data) if PLOTLY_AVAILABLE else "<p>图表功能需要安装Plotly</p>"
        detailed_metrics_table = self._create_detailed_metrics_table(metrics)
        trade_analysis_content = self._create_trade_analysis(result)
        
        # 填充模板
        html_content = html_template.format(
            strategy_name=result.strategy_name,
            symbol=result.symbol,
            start_date=result.start_date.strftime('%Y-%m-%d'),
            end_date=result.end_date.strftime('%Y-%m-%d'),
            generate_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            css_styles=css_styles,
            metrics_cards=metrics_cards,
            charts_content=charts_content,
            detailed_metrics_table=detailed_metrics_table,
            trade_analysis_content=trade_analysis_content
        )
        
        return html_content
    
    def _create_multi_strategy_html(self, results: Dict[str, StrategyResult], **kwargs) -> str:
        """创建多策略对比HTML内容"""
        
        # 简化的多策略对比模板
        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>多策略对比报告</title>
            <style>{css_styles}</style>
        </head>
        <body>
            <div class="container">
                <header>
                    <h1>多策略对比报告</h1>
                    <p><strong>生成时间:</strong> {generate_time}</p>
                </header>
                
                <section class="comparison">
                    <h2>策略对比</h2>
                    {comparison_table}
                </section>
                
                <section class="charts">
                    <h2>收益对比图</h2>
                    {comparison_charts}
                </section>
            </div>
        </body>
        </html>
        """
        
        css_styles = self._get_css_styles()
        comparison_table = self._create_comparison_table(results)
        comparison_charts = self._create_comparison_charts(results) if PLOTLY_AVAILABLE else "<p>图表功能需要安装Plotly</p>"
        
        html_content = html_template.format(
            css_styles=css_styles,
            generate_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            comparison_table=comparison_table,
            comparison_charts=comparison_charts
        )
        
        return html_content
    
    def _create_optimization_html(self, optimization_result: Dict[str, Any], **kwargs) -> str:
        """创建参数优化HTML内容"""
        
        # 简化的优化报告模板
        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>参数优化报告</title>
            <style>{css_styles}</style>
        </head>
        <body>
            <div class="container">
                <header>
                    <h1>参数优化报告</h1>
                    <p><strong>生成时间:</strong> {generate_time}</p>
                </header>
                
                <section class="optimization-summary">
                    <h2>优化结果</h2>
                    {optimization_summary}
                </section>
                
                <section class="parameter-analysis">
                    <h2>参数分析</h2>
                    {parameter_analysis}
                </section>
            </div>
        </body>
        </html>
        """
        
        css_styles = self._get_css_styles()
        optimization_summary = self._create_optimization_summary(optimization_result)
        parameter_analysis = self._create_parameter_analysis(optimization_result)
        
        html_content = html_template.format(
            css_styles=css_styles,
            generate_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            optimization_summary=optimization_summary,
            parameter_analysis=parameter_analysis
        )
        
        return html_content
    
    def _get_css_styles(self) -> str:
        """获取CSS样式"""
        return """
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007acc;
        }
        
        h1 {
            color: #007acc;
            margin-bottom: 10px;
        }
        
        h2 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        
        .report-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .positive {
            color: #28a745;
        }
        
        .negative {
            color: #dc3545;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
        
        section {
            margin-bottom: 40px;
        }
        """
    
    def _create_metrics_cards(self, metrics: Dict[str, Any]) -> str:
        """创建指标卡片"""
        basic_metrics = metrics.get('basic_metrics', {})
        
        cards = []
        
        # 主要指标卡片
        key_metrics = [
            ('total_return', '总收益率', 'percentage'),
            ('annual_return', '年化收益率', 'percentage'),
            ('sharpe_ratio', '夏普比率', 'number'),
            ('max_drawdown', '最大回撤', 'percentage'),
            ('volatility', '波动率', 'percentage'),
            ('win_rate', '胜率', 'percentage')
        ]
        
        for key, label, format_type in key_metrics:
            value = basic_metrics.get(key, 0)
            
            if format_type == 'percentage':
                formatted_value = self.format_percentage(value)
                css_class = 'positive' if value > 0 else 'negative'
            else:
                formatted_value = self.format_number(value)
                css_class = 'positive' if value > 0 else 'negative'
            
            card_html = f"""
            <div class="metric-card">
                <div class="metric-value {css_class}">{formatted_value}</div>
                <div class="metric-label">{label}</div>
            </div>
            """
            cards.append(card_html)
        
        return '\n'.join(cards)
    
    def _create_charts(self, result: StrategyResult, data: pd.DataFrame) -> str:
        """创建图表"""
        if not PLOTLY_AVAILABLE:
            return "<p>图表功能需要安装Plotly库</p>"
        
        try:
            # 创建子图
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('价格走势', '收益曲线', '回撤曲线', '信号分布'),
                specs=[[{"secondary_y": True}, {"secondary_y": False}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # 价格走势图
            fig.add_trace(
                go.Scatter(x=data.index, y=data['close'], name='价格', line=dict(color='blue')),
                row=1, col=1
            )
            
            # 简化的图表内容
            chart_html = fig.to_html(include_plotlyjs='cdn', div_id="charts")
            
            return chart_html
            
        except Exception as e:
            logger.warning(f"图表生成失败: {e}")
            return "<p>图表生成失败</p>"
    
    def _create_detailed_metrics_table(self, metrics: Dict[str, Any]) -> str:
        """创建详细指标表格"""
        basic_metrics = metrics.get('basic_metrics', {})
        trade_metrics = metrics.get('trade_metrics', {})
        risk_metrics = metrics.get('risk_metrics', {})
        
        table_html = """
        <table>
            <thead>
                <tr>
                    <th>指标类别</th>
                    <th>指标名称</th>
                    <th>数值</th>
                </tr>
            </thead>
            <tbody>
        """
        
        # 收益指标
        for key, label in [
            ('total_return', '总收益率'),
            ('annual_return', '年化收益率'),
            ('sharpe_ratio', '夏普比率'),
            ('calmar_ratio', 'Calmar比率'),
            ('sortino_ratio', 'Sortino比率')
        ]:
            value = basic_metrics.get(key, 0)
            formatted_value = self.format_percentage(value) if 'return' in key else self.format_number(value)
            table_html += f"""
                <tr>
                    <td>收益指标</td>
                    <td>{label}</td>
                    <td>{formatted_value}</td>
                </tr>
            """
        
        # 风险指标
        for key, label in [
            ('max_drawdown', '最大回撤'),
            ('volatility', '波动率')
        ]:
            value = risk_metrics.get(key, 0)
            formatted_value = self.format_percentage(value)
            table_html += f"""
                <tr>
                    <td>风险指标</td>
                    <td>{label}</td>
                    <td>{formatted_value}</td>
                </tr>
            """
        
        # 交易指标
        for key, label in [
            ('total_trades', '总交易次数'),
            ('winning_trades', '盈利交易次数'),
            ('losing_trades', '亏损交易次数'),
            ('profit_factor', '盈亏比')
        ]:
            value = trade_metrics.get(key, 0)
            formatted_value = str(int(value)) if 'trades' in key else self.format_number(value)
            table_html += f"""
                <tr>
                    <td>交易指标</td>
                    <td>{label}</td>
                    <td>{formatted_value}</td>
                </tr>
            """
        
        table_html += """
            </tbody>
        </table>
        """
        
        return table_html
    
    def _create_trade_analysis(self, result: StrategyResult) -> str:
        """创建交易分析"""
        if result.signals.empty:
            return "<p>无交易信号数据</p>"
        
        signals = result.signals
        trades = signals[signals['signal'] != 0]
        
        if trades.empty:
            return "<p>无交易记录</p>"
        
        analysis_html = f"""
        <div class="trade-summary">
            <h3>交易概览</h3>
            <p><strong>总信号数:</strong> {len(trades)}</p>
            <p><strong>买入信号:</strong> {(trades['signal'] > 0).sum()}</p>
            <p><strong>卖出信号:</strong> {(trades['signal'] < 0).sum()}</p>
        </div>
        """
        
        return analysis_html
    
    def _create_comparison_table(self, results: Dict[str, StrategyResult]) -> str:
        """创建策略对比表格"""
        table_html = """
        <table>
            <thead>
                <tr>
                    <th>策略名称</th>
                    <th>总收益率</th>
                    <th>年化收益率</th>
                    <th>夏普比率</th>
                    <th>最大回撤</th>
                    <th>波动率</th>
                </tr>
            </thead>
            <tbody>
        """
        
        for strategy_name, result in results.items():
            table_html += f"""
                <tr>
                    <td>{strategy_name}</td>
                    <td>{self.format_percentage(result.total_return)}</td>
                    <td>{self.format_percentage(result.annual_return)}</td>
                    <td>{self.format_number(result.sharpe_ratio)}</td>
                    <td>{self.format_percentage(result.max_drawdown)}</td>
                    <td>{self.format_percentage(result.volatility)}</td>
                </tr>
            """
        
        table_html += """
            </tbody>
        </table>
        """
        
        return table_html
    
    def _create_comparison_charts(self, results: Dict[str, StrategyResult]) -> str:
        """创建对比图表"""
        if not PLOTLY_AVAILABLE:
            return "<p>图表功能需要安装Plotly库</p>"
        
        return "<p>对比图表功能开发中...</p>"
    
    def _create_optimization_summary(self, optimization_result: Dict[str, Any]) -> str:
        """创建优化结果摘要"""
        best_params = optimization_result.get('best_params', {})
        best_score = optimization_result.get('best_score', 0)
        n_trials = optimization_result.get('n_trials', 0)
        
        summary_html = f"""
        <div class="optimization-summary">
            <h3>优化结果摘要</h3>
            <p><strong>最佳得分:</strong> {self.format_number(best_score)}</p>
            <p><strong>试验次数:</strong> {n_trials}</p>
            <p><strong>最佳参数:</strong></p>
            <ul>
        """
        
        for param, value in best_params.items():
            summary_html += f"<li><strong>{param}:</strong> {value}</li>"
        
        summary_html += """
            </ul>
        </div>
        """
        
        return summary_html
    
    def _create_parameter_analysis(self, optimization_result: Dict[str, Any]) -> str:
        """创建参数分析"""
        param_importance = optimization_result.get('param_importance', {})
        
        if not param_importance:
            return "<p>无参数重要性数据</p>"
        
        analysis_html = """
        <div class="parameter-importance">
            <h3>参数重要性</h3>
            <table>
                <thead>
                    <tr>
                        <th>参数名称</th>
                        <th>重要性得分</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for param, importance in sorted(param_importance.items(), key=lambda x: x[1], reverse=True):
            analysis_html += f"""
                <tr>
                    <td>{param}</td>
                    <td>{self.format_percentage(importance)}</td>
                </tr>
            """
        
        analysis_html += """
                </tbody>
            </table>
        </div>
        """
        
        return analysis_html


__all__ = [
    "HTMLReportGenerator"
]
