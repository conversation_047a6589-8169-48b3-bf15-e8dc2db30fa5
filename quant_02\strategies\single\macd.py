"""
MACD策略模块

基于MACD指标的交易策略实现。
"""

from typing import Optional, Dict, Any
import pandas as pd
import numpy as np
from pydantic import Field, validator

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from ..base.strategy import BaseStrategy, StrategyConfig
from ..registry import register_strategy
from utils.logger import get_logger

logger = get_logger(__name__)


class MACDConfig(StrategyConfig):
    """MACD策略配置"""
    
    # MACD参数
    fast_period: int = Field(default=12, description="快速EMA周期", gt=0)
    slow_period: int = Field(default=26, description="慢速EMA周期", gt=0)
    signal_period: int = Field(default=9, description="信号线周期", gt=0)
    
    # 信号过滤
    macd_threshold: float = Field(default=0.0, description="MACD阈值")
    use_histogram_filter: bool = Field(default=True, description="是否使用柱状图过滤")
    histogram_threshold: float = Field(default=0.0, description="柱状图阈值")
    
    # 趋势过滤
    use_trend_filter: bool = Field(default=False, description="是否使用趋势过滤")
    trend_period: int = Field(default=50, description="趋势判断周期", gt=0)
    
    # 成交量过滤
    use_volume_filter: bool = Field(default=False, description="是否使用成交量过滤")
    volume_threshold: float = Field(default=1.5, description="成交量倍数阈值", gt=0)
    
    @validator('slow_period')
    def validate_periods(cls, v, values):
        if 'fast_period' in values and v <= values['fast_period']:
            raise ValueError("慢速周期必须大于快速周期")
        return v


@register_strategy(
    name="macd",
    category="trend",
    tags=["macd", "trend", "momentum"],
    description="基于MACD指标的趋势跟踪策略",
    author="Quant Team",
    version="2.0.0"
)
class MACDStrategy(BaseStrategy):
    """MACD策略
    
    策略逻辑：
    1. 计算MACD指标（MACD线、信号线、柱状图）
    2. 当MACD线上穿信号线时产生买入信号
    3. 当MACD线下穿信号线时产生卖出信号
    4. 根据柱状图强度计算信号强度
    5. 可选的价格和成交量过滤
    6. 可选的止损止盈
    
    优化特性：
    - 向量化计算，高性能
    - 多重信号过滤
    - 动态信号强度计算
    - 完整的参数验证
    """
    
    def __init__(self, config: Optional[MACDConfig] = None):
        """
        初始化MACD策略
        
        Args:
            config: MACD策略配置
        """
        if config is None:
            config = MACDConfig(strategy_name="macd")
        
        super().__init__(config)
        self.config: MACDConfig = config
        
        logger.info(f"MACD策略初始化完成: fast={config.fast_period}, slow={config.slow_period}, signal={config.signal_period}")
    
    def precompute_indicators(self, data: pd.DataFrame):
        """预计算MACD指标"""
        close_prices = data['close']
        
        # 计算快速和慢速EMA
        fast_ema = close_prices.ewm(span=self.config.fast_period).mean()
        slow_ema = close_prices.ewm(span=self.config.slow_period).mean()
        
        # 计算MACD线
        macd_line = fast_ema - slow_ema
        
        # 计算信号线
        signal_line = macd_line.ewm(span=self.config.signal_period).mean()
        
        # 计算柱状图
        histogram = macd_line - signal_line
        
        # 缓存指标
        self.set_indicator('macd', macd_line)
        self.set_indicator('signal', signal_line)
        self.set_indicator('histogram', histogram)
        self.set_indicator('fast_ema', fast_ema)
        self.set_indicator('slow_ema', slow_ema)
        
        # 如果启用趋势过滤，计算趋势指标
        if self.config.use_trend_filter:
            trend_ma = close_prices.rolling(window=self.config.trend_period).mean()
            self.set_indicator('trend_ma', trend_ma)
        
        logger.debug("MACD指标预计算完成")
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成MACD交易信号
        
        Args:
            data: 价格数据DataFrame
            
        Returns:
            信号DataFrame
        """
        # 预计算指标
        self.precompute_indicators(data)
        
        # 获取指标
        macd_line = self.get_indicator('macd')
        signal_line = self.get_indicator('signal')
        histogram = self.get_indicator('histogram')
        
        # 初始化信号DataFrame
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['strength'] = 0.0
        signals['price'] = data['close']
        signals['macd'] = macd_line
        signals['signal_line'] = signal_line
        signals['histogram'] = histogram
        
        # 检测MACD线与信号线的交叉
        macd_cross_up = (
            (macd_line > signal_line) & 
            (macd_line.shift(1) <= signal_line.shift(1))
        )
        
        macd_cross_down = (
            (macd_line < signal_line) & 
            (macd_line.shift(1) >= signal_line.shift(1))
        )
        
        # 生成基础信号
        signals.loc[macd_cross_up, 'signal'] = 1  # 买入信号
        signals.loc[macd_cross_down, 'signal'] = -1  # 卖出信号
        
        # 计算信号强度
        signals['strength'] = self._calculate_signal_strength(data, signals)
        
        # 应用过滤器
        signals = self._apply_filters(data, signals)
        
        # 应用信号阈值
        weak_signals = signals['strength'] < self.config.min_signal_strength
        signals.loc[weak_signals, 'signal'] = 0
        signals.loc[weak_signals, 'strength'] = 0.0
        
        # 统计信号
        buy_signals = (signals['signal'] == 1).sum()
        sell_signals = (signals['signal'] == -1).sum()
        
        logger.info(f"MACD信号生成完成: 买入信号={buy_signals}, 卖出信号={sell_signals}")
        
        return signals
    
    def _calculate_signal_strength(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.Series:
        """计算信号强度"""
        strength = pd.Series(0.0, index=data.index)
        
        macd_line = signals['macd']
        signal_line = signals['signal_line']
        histogram = signals['histogram']
        
        # 基于MACD线与信号线的距离计算强度
        macd_distance = abs(macd_line - signal_line)
        max_distance = macd_distance.rolling(window=50).max()
        normalized_distance = macd_distance / (max_distance + 1e-8)
        
        # 基于柱状图的强度
        histogram_abs = abs(histogram)
        max_histogram = histogram_abs.rolling(window=50).max()
        normalized_histogram = histogram_abs / (max_histogram + 1e-8)
        
        # 综合强度计算
        base_strength = (normalized_distance + normalized_histogram) / 2
        
        # 只对有信号的点计算强度
        signal_mask = signals['signal'] != 0
        strength.loc[signal_mask] = base_strength.loc[signal_mask].clip(0.1, 1.0)
        
        return strength
    
    def _apply_filters(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.DataFrame:
        """应用信号过滤器"""
        filtered_signals = signals.copy()
        
        # MACD阈值过滤
        if self.config.macd_threshold != 0:
            macd_line = signals['macd']
            weak_macd = abs(macd_line) < self.config.macd_threshold
            filtered_signals.loc[weak_macd, 'signal'] = 0
            filtered_signals.loc[weak_macd, 'strength'] = 0.0
        
        # 柱状图过滤
        if self.config.use_histogram_filter:
            histogram = signals['histogram']
            
            # 买入信号：柱状图必须为正且大于阈值
            buy_mask = (filtered_signals['signal'] == 1) & (
                (histogram <= 0) | (histogram < self.config.histogram_threshold)
            )
            filtered_signals.loc[buy_mask, 'signal'] = 0
            filtered_signals.loc[buy_mask, 'strength'] = 0.0
            
            # 卖出信号：柱状图必须为负且小于阈值
            sell_mask = (filtered_signals['signal'] == -1) & (
                (histogram >= 0) | (histogram > -self.config.histogram_threshold)
            )
            filtered_signals.loc[sell_mask, 'signal'] = 0
            filtered_signals.loc[sell_mask, 'strength'] = 0.0
        
        # 趋势过滤
        if self.config.use_trend_filter:
            filtered_signals = self._apply_trend_filter(data, filtered_signals)
        
        # 成交量过滤
        if self.config.use_volume_filter:
            filtered_signals = self._apply_volume_filter(data, filtered_signals)
        
        return filtered_signals
    
    def _apply_trend_filter(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.DataFrame:
        """应用趋势过滤"""
        trend_ma = self.get_indicator('trend_ma')
        close_prices = data['close']
        
        # 只在趋势方向一致时保留信号
        uptrend = close_prices > trend_ma
        downtrend = close_prices < trend_ma
        
        # 买入信号只在上升趋势中有效
        buy_against_trend = (signals['signal'] == 1) & (~uptrend)
        signals.loc[buy_against_trend, 'signal'] = 0
        signals.loc[buy_against_trend, 'strength'] = 0.0
        
        # 卖出信号只在下降趋势中有效
        sell_against_trend = (signals['signal'] == -1) & (~downtrend)
        signals.loc[sell_against_trend, 'signal'] = 0
        signals.loc[sell_against_trend, 'strength'] = 0.0
        
        return signals
    
    def _apply_volume_filter(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.DataFrame:
        """应用成交量过滤"""
        volume = data['volume']
        avg_volume = volume.rolling(window=20).mean()
        
        # 只在成交量放大时保留信号
        low_volume = volume < (avg_volume * self.config.volume_threshold)
        
        volume_filtered = (signals['signal'] != 0) & low_volume
        signals.loc[volume_filtered, 'signal'] = 0
        signals.loc[volume_filtered, 'strength'] = 0.0
        
        return signals
