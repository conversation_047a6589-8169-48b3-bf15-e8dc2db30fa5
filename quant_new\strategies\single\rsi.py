"""
RSI策略

基于RSI指标的均值回归策略。
"""

from typing import Dict
import pandas as pd
import numpy as np
from pydantic import Field, validator

from ..base import BaseStrategy, StrategyConfig
from ...utils.indicators.momentum import rsi
from ...utils.indicators.trend import sma
from ...utils.logger import get_logger

logger = get_logger(__name__)


class RSIConfig(StrategyConfig):
    """RSI策略配置"""
    
    strategy_name: str = Field(default="RSI策略", description="策略名称")
    
    # RSI参数
    rsi_period: int = Field(default=14, description="RSI计算周期")
    
    # 信号阈值
    oversold_threshold: float = Field(default=30.0, description="超卖阈值")
    overbought_threshold: float = Field(default=70.0, description="超买阈值")
    
    # 极值阈值（更强的信号）
    extreme_oversold: float = Field(default=20.0, description="极度超卖阈值")
    extreme_overbought: float = Field(default=80.0, description="极度超买阈值")
    
    # 中性区间
    neutral_zone_lower: float = Field(default=40.0, description="中性区间下限")
    neutral_zone_upper: float = Field(default=60.0, description="中性区间上限")
    
    # 背离检测
    divergence_detection: bool = Field(default=True, description="是否检测背离")
    divergence_lookback: int = Field(default=20, description="背离检测回看期")
    
    # 趋势过滤
    trend_filter: bool = Field(default=True, description="是否启用趋势过滤")
    trend_ma_period: int = Field(default=50, description="趋势均线周期")
    
    # 多重时间框架
    multi_timeframe: bool = Field(default=False, description="是否使用多重时间框架")
    higher_tf_period: int = Field(default=3, description="高级时间框架倍数")
    
    @validator('oversold_threshold', 'extreme_oversold')
    def validate_oversold(cls, v, values):
        """验证超卖阈值"""
        if v >= 50:
            raise ValueError("超卖阈值必须小于50")
        return v
    
    @validator('overbought_threshold', 'extreme_overbought')
    def validate_overbought(cls, v, values):
        """验证超买阈值"""
        if v <= 50:
            raise ValueError("超买阈值必须大于50")
        return v
    
    @validator('neutral_zone_upper')
    def validate_neutral_zone(cls, v, values):
        """验证中性区间"""
        if 'neutral_zone_lower' in values and v <= values['neutral_zone_lower']:
            raise ValueError("中性区间上限必须大于下限")
        return v


class RSIStrategy(BaseStrategy):
    """RSI策略实现"""
    
    def __init__(self, config: RSIConfig):
        """
        初始化RSI策略
        
        Args:
            config: RSI策略配置
        """
        super().__init__(config)
        self.config: RSIConfig = config
    
    def calculate_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        计算技术指标
        
        Args:
            data: 价格数据
            
        Returns:
            技术指标字典
        """
        indicators = {}
        
        # 计算RSI
        indicators['rsi'] = rsi(data['close'], self.config.rsi_period)
        
        # 计算RSI的移动平均（平滑RSI）
        indicators['rsi_ma'] = indicators['rsi'].rolling(window=3).mean()
        
        # 计算趋势指标
        if self.config.trend_filter:
            indicators['trend_ma'] = sma(data['close'], self.config.trend_ma_period)
        
        # 多重时间框架RSI
        if self.config.multi_timeframe:
            # 重采样到更高时间框架
            higher_tf_data = self._resample_data(data, self.config.higher_tf_period)
            indicators['rsi_htf'] = rsi(higher_tf_data['close'], self.config.rsi_period)
            # 将高级时间框架数据对齐到原始时间框架
            indicators['rsi_htf'] = indicators['rsi_htf'].reindex(data.index, method='ffill')
        
        # 计算RSI背离指标
        if self.config.divergence_detection:
            indicators.update(self._calculate_divergence_indicators(data, indicators['rsi']))
        
        return indicators
    
    def _resample_data(self, data: pd.DataFrame, factor: int) -> pd.DataFrame:
        """
        重采样数据到更高时间框架
        
        Args:
            data: 原始数据
            factor: 重采样因子
            
        Returns:
            重采样后的数据
        """
        # 简单的重采样：每N个数据点取一个
        resampled = data.iloc[::factor].copy()
        return resampled
    
    def _calculate_divergence_indicators(self, data: pd.DataFrame, rsi_values: pd.Series) -> Dict[str, pd.Series]:
        """
        计算背离指标
        
        Args:
            data: 价格数据
            rsi_values: RSI值
            
        Returns:
            背离指标字典
        """
        indicators = {}
        
        # 计算价格和RSI的局部极值
        price_highs = data['close'].rolling(window=5, center=True).max() == data['close']
        price_lows = data['close'].rolling(window=5, center=True).min() == data['close']
        
        rsi_highs = rsi_values.rolling(window=5, center=True).max() == rsi_values
        rsi_lows = rsi_values.rolling(window=5, center=True).min() == rsi_values
        
        # 检测顶背离（价格创新高，RSI未创新高）
        bullish_divergence = pd.Series(False, index=data.index)
        bearish_divergence = pd.Series(False, index=data.index)
        
        lookback = self.config.divergence_lookback
        
        for i in range(lookback, len(data)):
            current_idx = data.index[i]
            
            # 检查是否在局部极值点
            if price_lows.iloc[i] and rsi_lows.iloc[i]:
                # 寻找前一个低点
                prev_period = data.iloc[i-lookback:i]
                prev_rsi_period = rsi_values.iloc[i-lookback:i]
                
                if len(prev_period) > 0:
                    prev_low_idx = prev_period['close'].idxmin()
                    prev_low_price = prev_period['close'].min()
                    prev_low_rsi = prev_rsi_period.loc[prev_low_idx]
                    
                    # 顶背离：价格创新低，RSI未创新低
                    if (data['close'].iloc[i] < prev_low_price and 
                        rsi_values.iloc[i] > prev_low_rsi):
                        bullish_divergence.loc[current_idx] = True
            
            elif price_highs.iloc[i] and rsi_highs.iloc[i]:
                # 寻找前一个高点
                prev_period = data.iloc[i-lookback:i]
                prev_rsi_period = rsi_values.iloc[i-lookback:i]
                
                if len(prev_period) > 0:
                    prev_high_idx = prev_period['close'].idxmax()
                    prev_high_price = prev_period['close'].max()
                    prev_high_rsi = prev_rsi_period.loc[prev_high_idx]
                    
                    # 底背离：价格创新高，RSI未创新高
                    if (data['close'].iloc[i] > prev_high_price and 
                        rsi_values.iloc[i] < prev_high_rsi):
                        bearish_divergence.loc[current_idx] = True
        
        indicators['bullish_divergence'] = bullish_divergence
        indicators['bearish_divergence'] = bearish_divergence
        
        return indicators
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            
        Returns:
            信号DataFrame
        """
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['strength'] = 0.0
        signals['price'] = data['close']
        
        rsi_values = self._indicators['rsi']
        
        # 基本RSI信号
        # 买入信号：RSI从超卖区域向上突破
        buy_condition = (
            (rsi_values > self.config.oversold_threshold) &
            (rsi_values.shift(1) <= self.config.oversold_threshold)
        )
        
        # 卖出信号：RSI从超买区域向下突破
        sell_condition = (
            (rsi_values < self.config.overbought_threshold) &
            (rsi_values.shift(1) >= self.config.overbought_threshold)
        )
        
        # 强信号：极值区域
        strong_buy = rsi_values <= self.config.extreme_oversold
        strong_sell = rsi_values >= self.config.extreme_overbought
        
        # 应用趋势过滤
        if self.config.trend_filter:
            trend_ma = self._indicators['trend_ma']
            trend_up = data['close'] > trend_ma
            trend_down = data['close'] < trend_ma
            
            # 只在趋势方向一致时发出信号
            buy_condition = buy_condition & trend_up
            sell_condition = sell_condition & trend_down
        
        # 多重时间框架确认
        if self.config.multi_timeframe:
            rsi_htf = self._indicators['rsi_htf']
            htf_oversold = rsi_htf <= self.config.oversold_threshold
            htf_overbought = rsi_htf >= self.config.overbought_threshold
            
            buy_condition = buy_condition & htf_oversold
            sell_condition = sell_condition & htf_overbought
        
        # 背离信号
        if self.config.divergence_detection:
            bullish_div = self._indicators['bullish_divergence']
            bearish_div = self._indicators['bearish_divergence']
            
            # 背离信号可以增强现有信号或独立产生信号
            buy_condition = buy_condition | (bullish_div & (rsi_values <= self.config.oversold_threshold))
            sell_condition = sell_condition | (bearish_div & (rsi_values >= self.config.overbought_threshold))
        
        # 设置信号
        signals.loc[buy_condition, 'signal'] = 1
        signals.loc[sell_condition, 'signal'] = -1
        
        # 强信号覆盖
        signals.loc[strong_buy, 'signal'] = 1
        signals.loc[strong_sell, 'signal'] = -1
        
        # 计算信号强度
        self._calculate_signal_strength(signals, data, rsi_values)
        
        # 过滤弱信号
        weak_signals = signals['strength'] < self.config.signal_threshold
        signals.loc[weak_signals, 'signal'] = 0
        signals.loc[weak_signals, 'strength'] = 0.0
        
        logger.debug(f"生成RSI信号: 买入{(signals['signal'] > 0).sum()}个, 卖出{(signals['signal'] < 0).sum()}个")
        
        return signals
    
    def _calculate_signal_strength(self, signals: pd.DataFrame, data: pd.DataFrame, rsi_values: pd.Series):
        """
        计算信号强度
        
        Args:
            signals: 信号DataFrame
            data: 价格数据
            rsi_values: RSI值
        """
        # 基于RSI距离极值的程度计算强度
        oversold_strength = np.maximum(0, (self.config.oversold_threshold - rsi_values) / self.config.oversold_threshold)
        overbought_strength = np.maximum(0, (rsi_values - self.config.overbought_threshold) / (100 - self.config.overbought_threshold))
        
        # 极值区域额外加成
        extreme_oversold_boost = np.where(rsi_values <= self.config.extreme_oversold, 0.3, 0)
        extreme_overbought_boost = np.where(rsi_values >= self.config.extreme_overbought, 0.3, 0)
        
        # 背离信号加成
        divergence_boost = 0
        if self.config.divergence_detection:
            bullish_div = self._indicators['bullish_divergence']
            bearish_div = self._indicators['bearish_divergence']
            divergence_boost = np.where(bullish_div | bearish_div, 0.2, 0)
        
        # 买入信号强度
        buy_strength = oversold_strength + extreme_oversold_boost + divergence_boost
        
        # 卖出信号强度
        sell_strength = overbought_strength + extreme_overbought_boost + divergence_boost
        
        # 限制强度在0-1之间
        buy_strength = np.minimum(buy_strength, 1.0)
        sell_strength = np.minimum(sell_strength, 1.0)
        
        # 设置信号强度
        buy_mask = signals['signal'] > 0
        sell_mask = signals['signal'] < 0
        
        signals.loc[buy_mask, 'strength'] = buy_strength[buy_mask]
        signals.loc[sell_mask, 'strength'] = sell_strength[sell_mask]
    
    @classmethod
    def get_param_description(cls) -> Dict[str, str]:
        """获取参数描述"""
        base_desc = super().get_param_description()
        rsi_desc = {
            'rsi_period': 'RSI计算周期',
            'oversold_threshold': '超卖阈值',
            'overbought_threshold': '超买阈值',
            'extreme_oversold': '极度超卖阈值',
            'extreme_overbought': '极度超买阈值',
            'neutral_zone_lower': '中性区间下限',
            'neutral_zone_upper': '中性区间上限',
            'divergence_detection': '背离检测',
            'divergence_lookback': '背离检测回看期',
            'trend_filter': '趋势过滤',
            'trend_ma_period': '趋势均线周期',
            'multi_timeframe': '多重时间框架',
            'higher_tf_period': '高级时间框架倍数'
        }
        return {**base_desc, **rsi_desc}
    
    @classmethod
    def get_param_constraints(cls) -> Dict[str, Dict]:
        """获取参数约束"""
        base_constraints = super().get_param_constraints()
        rsi_constraints = {
            'rsi_period': {'min': 5, 'max': 30, 'type': 'int'},
            'oversold_threshold': {'min': 10.0, 'max': 40.0, 'type': 'float'},
            'overbought_threshold': {'min': 60.0, 'max': 90.0, 'type': 'float'},
            'extreme_oversold': {'min': 5.0, 'max': 25.0, 'type': 'float'},
            'extreme_overbought': {'min': 75.0, 'max': 95.0, 'type': 'float'},
            'neutral_zone_lower': {'min': 30.0, 'max': 50.0, 'type': 'float'},
            'neutral_zone_upper': {'min': 50.0, 'max': 70.0, 'type': 'float'},
            'divergence_lookback': {'min': 10, 'max': 50, 'type': 'int'},
            'trend_ma_period': {'min': 20, 'max': 200, 'type': 'int'},
            'higher_tf_period': {'min': 2, 'max': 10, 'type': 'int'}
        }
        return {**base_constraints, **rsi_constraints}


__all__ = [
    "RSIStrategy",
    "RSIConfig"
]
