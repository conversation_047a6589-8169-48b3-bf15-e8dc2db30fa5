"""
策略基类

定义量化交易策略的统一接口和基础功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, Tuple
from datetime import datetime
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from pydantic import BaseModel, Field, validator
import uuid

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from core.data_structures.trading import Order, Position, Trade, OrderSide
from core.data_structures.market import OHLCV
from utils.logger import get_logger

logger = get_logger(__name__)


class StrategyConfig(BaseModel):
    """策略配置基类

    所有策略配置都应该继承此类。
    """

    # 基本信息
    strategy_name: str = Field(description="策略名称")
    strategy_id: Optional[str] = Field(default=None, description="策略ID")
    version: str = Field(default="1.0.0", description="策略版本")

    # 交易参数
    initial_capital: float = Field(default=1000000.0, description="初始资金")
    commission_rate: float = Field(default=0.0003, description="手续费率")
    slippage_rate: float = Field(default=0.0001, description="滑点率")

    # 风险参数
    max_position_size: float = Field(default=1.0, description="最大仓位比例")
    stop_loss_rate: Optional[float] = Field(default=None, description="止损比例")
    take_profit_rate: Optional[float] = Field(default=None, description="止盈比例")

    # 其他参数
    benchmark: str = Field(default="000300", description="基准指数")
    frequency: str = Field(default="1d", description="数据频率")

    # 元数据
    description: str = Field(default="", description="策略描述")
    tags: List[str] = Field(default_factory=list, description="策略标签")

    class Config:
        extra = "allow"
        use_enum_values = True

    def __post_init__(self):
        if self.strategy_id is None:
            self.strategy_id = f"{self.strategy_name}_{uuid.uuid4().hex[:8]}"


@dataclass
class StrategyResult:
    """策略结果数据结构"""

    # 基本信息
    strategy_id: str
    symbol: str
    start_date: datetime
    end_date: datetime

    # 收益指标
    total_return: float = 0.0
    annual_return: float = 0.0
    benchmark_return: float = 0.0
    excess_return: float = 0.0

    # 风险指标
    volatility: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0

    # 交易统计
    total_trades: int = 0
    win_trades: int = 0
    lose_trades: int = 0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_lose: float = 0.0
    profit_factor: float = 0.0

    # 持仓统计
    avg_holding_period: float = 0.0
    max_holding_period: float = 0.0
    turnover_rate: float = 0.0

    # 详细数据
    portfolio_value: pd.Series = field(default_factory=pd.Series)
    positions: pd.DataFrame = field(default_factory=pd.DataFrame)
    trades: List[Trade] = field(default_factory=list)
    signals: pd.DataFrame = field(default_factory=pd.DataFrame)

    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'strategy_id': self.strategy_id,
            'symbol': self.symbol,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'total_return': self.total_return,
            'annual_return': self.annual_return,
            'benchmark_return': self.benchmark_return,
            'excess_return': self.excess_return,
            'volatility': self.volatility,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'calmar_ratio': self.calmar_ratio,
            'total_trades': self.total_trades,
            'win_trades': self.win_trades,
            'lose_trades': self.lose_trades,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_lose': self.avg_lose,
            'profit_factor': self.profit_factor,
            'avg_holding_period': self.avg_holding_period,
            'max_holding_period': self.max_holding_period,
            'turnover_rate': self.turnover_rate,
            'metadata': self.metadata,
        }


class BaseStrategy(ABC):
    """策略抽象基类

    所有量化策略都应该继承此类。

    优化特性：
    - 标准化的策略接口
    - 完整的生命周期管理
    - 内置性能监控
    - 灵活的参数配置
    - 详细的执行日志
    """

    def __init__(self, config: StrategyConfig):
        """
        初始化策略

        Args:
            config: 策略配置
        """
        self.config = config
        self.name = config.strategy_name
        self.strategy_id = config.strategy_id

        # 内部状态
        self._initialized = False
        self._data = None
        self._current_position = 0.0
        self._cash = config.initial_capital
        self._portfolio_value = config.initial_capital

        # 交易记录
        self._trades: List[Trade] = []
        self._orders: List[Order] = []
        self._signals = pd.DataFrame()

        # 性能统计
        self._stats = {
            'signal_count': 0,
            'trade_count': 0,
            'execution_time': 0.0,
            'last_update': None
        }

        # 指标缓存
        self._indicators_cache: Dict[str, Any] = {}

        logger.info(f"策略初始化: {self.name} ({self.strategy_id})")

    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号

        Args:
            data: 价格数据DataFrame，包含OHLCV列

        Returns:
            信号DataFrame，包含以下列：
            - signal: 信号方向 (1: 买入, -1: 卖出, 0: 无信号)
            - strength: 信号强度 (0-1)
            - price: 信号价格
            - timestamp: 信号时间
        """
        pass

    def initialize(self, data: pd.DataFrame):
        """
        策略初始化

        Args:
            data: 历史数据
        """
        # 重置所有状态
        self._data = data
        self._initialized = True
        self._signals = pd.DataFrame()
        self._indicators_cache = {}  # 清空指标缓存
        self._stats = {
            'execution_time': 0,
            'last_update': None,
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0
        }

        # 预计算指标
        self._precompute_indicators(data)

        logger.info(f"策略初始化完成: {self.name}")

    def _precompute_indicators(self, data: pd.DataFrame):
        """预计算技术指标"""
        # 子类可以重写此方法来预计算指标
        pass

    def run(self, data: pd.DataFrame) -> StrategyResult:
        """
        运行策略

        Args:
            data: 价格数据

        Returns:
            策略结果
        """
        # 每次运行都重新初始化，确保状态干净
        self.initialize(data)

        start_time = datetime.now()

        try:
            # 生成信号
            signals = self.generate_signals(data)
            self._signals = signals

            # 模拟交易
            result = self._simulate_trading(data, signals)

            # 更新统计
            execution_time = (datetime.now() - start_time).total_seconds()
            self._stats['execution_time'] = execution_time
            self._stats['last_update'] = datetime.now()

            logger.info(f"策略运行完成: {self.name}, 耗时: {execution_time:.2f}秒")

            return result

        except Exception as e:
            logger.error(f"策略运行失败: {self.name}, 错误: {e}")
            raise

    def _simulate_trading(self, data: pd.DataFrame, signals: pd.DataFrame) -> StrategyResult:
        """模拟交易执行"""
        # 初始化
        portfolio_values = []
        positions = []
        trades = []

        current_position = 0.0
        cash = self.config.initial_capital

        # 遍历每个交易日
        for i, (date, row) in enumerate(data.iterrows()):
            # 获取当日信号
            if date in signals.index:
                signal_row = signals.loc[date]
                signal = signal_row.get('signal', 0)
                strength = signal_row.get('strength', 0)

                if signal != 0:
                    # 执行交易
                    trade_result = self._execute_trade(
                        signal, strength, row['close'], date, cash, current_position
                    )

                    if trade_result:
                        trade, new_position, new_cash = trade_result
                        trades.append(trade)
                        current_position = new_position
                        cash = new_cash

            # 计算组合价值
            portfolio_value = cash + current_position * row['close']
            portfolio_values.append(portfolio_value)

            # 记录持仓
            positions.append({
                'date': date,
                'position': current_position,
                'cash': cash,
                'portfolio_value': portfolio_value,
                'price': row['close']
            })

        # 构建结果
        portfolio_series = pd.Series(portfolio_values, index=data.index)
        positions_df = pd.DataFrame(positions)

        # 计算绩效指标
        result = self._calculate_performance(
            data, portfolio_series, positions_df, trades, signals
        )

        return result

    def _execute_trade(
        self,
        signal: int,
        strength: float,
        price: float,
        timestamp: datetime,
        cash: float,
        current_position: float
    ) -> Optional[Tuple[Trade, float, float]]:
        """执行交易"""
        # 计算交易数量
        if signal > 0:  # 买入
            # 计算可买数量
            available_cash = cash * self.config.max_position_size * strength
            commission = available_cash * self.config.commission_rate
            slippage = available_cash * self.config.slippage_rate

            trade_value = available_cash - commission - slippage
            quantity = trade_value / price

            if quantity > 0:
                # 创建交易记录
                trade = Trade(
                    trade_id=str(uuid.uuid4()),
                    symbol="",  # 在上层设置
                    side=OrderSide.BUY,
                    quantity=quantity,
                    price=price,
                    timestamp=timestamp,
                    commission=commission,
                    slippage=slippage
                )

                new_position = current_position + quantity
                new_cash = cash - trade_value - commission - slippage

                return trade, new_position, new_cash

        elif signal < 0:  # 卖出
            if current_position > 0:
                # 计算卖出数量
                quantity = current_position * strength
                trade_value = quantity * price
                commission = trade_value * self.config.commission_rate
                slippage = trade_value * self.config.slippage_rate

                # 创建交易记录
                trade = Trade(
                    trade_id=str(uuid.uuid4()),
                    symbol="",  # 在上层设置
                    side=OrderSide.SELL,
                    quantity=quantity,
                    price=price,
                    timestamp=timestamp,
                    commission=commission,
                    slippage=slippage
                )

                new_position = current_position - quantity
                new_cash = cash + trade_value - commission - slippage

                return trade, new_position, new_cash

        return None

    def _calculate_performance(
        self,
        data: pd.DataFrame,
        portfolio_values: pd.Series,
        positions: pd.DataFrame,
        trades: List[Trade],
        signals: pd.DataFrame
    ) -> StrategyResult:
        """计算绩效指标"""
        # 基本信息
        start_date = data.index[0]
        end_date = data.index[-1]

        # 收益计算
        total_return = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1
        days = (end_date - start_date).days
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0

        # 风险计算
        returns = portfolio_values.pct_change().dropna()
        volatility = returns.std() * np.sqrt(252) if len(returns) > 1 else 0

        # 最大回撤
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()

        # 夏普比率
        risk_free_rate = 0.03  # 假设无风险利率3%
        excess_returns = returns - risk_free_rate / 252
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0

        # 交易统计
        total_trades = len(trades)
        win_trades = sum(1 for t in trades if t.side == OrderSide.SELL and t.price > 0)  # 简化计算
        lose_trades = total_trades - win_trades
        win_rate = win_trades / total_trades if total_trades > 0 else 0

        # 构建结果
        result = StrategyResult(
            strategy_id=self.strategy_id,
            symbol="",  # 在上层设置
            start_date=start_date,
            end_date=end_date,
            total_return=total_return,
            annual_return=annual_return,
            volatility=volatility,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            total_trades=total_trades,
            win_trades=win_trades,
            lose_trades=lose_trades,
            win_rate=win_rate,
            portfolio_value=portfolio_values,
            positions=positions,
            trades=trades,
            signals=signals
        )

        return result

    def get_config(self) -> StrategyConfig:
        """获取策略配置"""
        return self.config

    def get_stats(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        return self._stats.copy()

    def get_indicators_cache(self) -> Dict[str, Any]:
        """获取指标缓存"""
        return self._indicators_cache.copy()

    def clear_cache(self):
        """清空缓存"""
        self._indicators_cache.clear()

    def __str__(self) -> str:
        return f"{self.name}({self.strategy_id})"

    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}: {self.name}>"
