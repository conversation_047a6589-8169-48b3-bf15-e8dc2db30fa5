"""
回测报告生成器模块

生成专业的回测报告。
"""

from typing import Dict, List, Any, Optional
import pandas as pd
from datetime import datetime
from pathlib import Path

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from .base import BaseReportGenerator, ReportConfig
from core.structures.portfolio import StrategyResult
from utils.logger import get_logger

logger = get_logger(__name__)


class BacktestReportGenerator(BaseReportGenerator):
    """回测报告生成器
    
    生成包含以下内容的专业回测报告：
    - 策略概览
    - 性能指标
    - 风险分析
    - 交易明细
    - 图表分析
    """
    
    def __init__(self, config: Optional[ReportConfig] = None):
        """
        初始化回测报告生成器
        
        Args:
            config: 报告配置
        """
        super().__init__(config)
        
        logger.info("回测报告生成器初始化完成")
    
    def generate(
        self,
        result: StrategyResult,
        benchmark_data: Optional[pd.DataFrame] = None,
        **kwargs
    ) -> str:
        """
        生成回测报告
        
        Args:
            result: 策略结果
            benchmark_data: 基准数据
            **kwargs: 其他参数
            
        Returns:
            报告文件路径
        """
        try:
            logger.info(f"开始生成回测报告: {result.strategy_id}")
            
            # 准备数据
            data = self._prepare_data(result)
            
            # 计算额外指标
            additional_metrics = self._calculate_additional_metrics(data)
            data['additional_metrics'] = additional_metrics
            
            # 基准分析
            if benchmark_data is not None:
                benchmark_analysis = self._analyze_benchmark(data, benchmark_data)
                data['benchmark_analysis'] = benchmark_analysis
            
            # 创建图表
            if self.config.include_charts:
                charts = self._create_charts(data)
                data['charts'] = charts
            
            # 生成报告内容
            if self.config.output_format == 'html':
                content = self._generate_html_report(data)
            elif self.config.output_format == 'markdown':
                content = self._generate_markdown_report(data)
            else:
                raise ValueError(f"不支持的输出格式: {self.config.output_format}")
            
            # 保存报告
            filename = self.config.get_output_filename(result.strategy_id)
            report_path = self._save_report(content, filename)
            
            logger.info(f"回测报告生成完成: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"回测报告生成失败: {e}")
            raise
    
    def _analyze_benchmark(self, data: Dict[str, Any], benchmark_data: pd.DataFrame) -> Dict[str, Any]:
        """分析基准表现"""
        equity_curve = data['equity_curve']
        
        # 对齐时间序列
        aligned_equity, aligned_benchmark = equity_curve.align(
            benchmark_data['close'], join='inner'
        )
        
        if len(aligned_equity) < 2:
            return {}
        
        # 计算收益率
        strategy_returns = aligned_equity.pct_change().dropna()
        benchmark_returns = aligned_benchmark.pct_change().dropna()
        
        # 基准分析
        analysis = {}
        
        if len(strategy_returns) > 0 and len(benchmark_returns) > 0:
            # 对齐收益率序列
            aligned_strategy, aligned_benchmark_ret = strategy_returns.align(
                benchmark_returns, join='inner'
            )
            
            if len(aligned_strategy) > 10:
                # 基本指标
                strategy_total = (1 + aligned_strategy).prod() - 1
                benchmark_total = (1 + aligned_benchmark_ret).prod() - 1
                
                analysis.update({
                    'strategy_total_return': strategy_total,
                    'benchmark_total_return': benchmark_total,
                    'excess_return': strategy_total - benchmark_total,
                    'strategy_volatility': aligned_strategy.std() * (252**0.5),
                    'benchmark_volatility': aligned_benchmark_ret.std() * (252**0.5),
                })
                
                # Beta和Alpha
                covariance = aligned_strategy.cov(aligned_benchmark_ret)
                benchmark_variance = aligned_benchmark_ret.var()
                
                if benchmark_variance > 0:
                    beta = covariance / benchmark_variance
                    alpha = aligned_strategy.mean() - beta * aligned_benchmark_ret.mean()
                    
                    analysis.update({
                        'beta': beta,
                        'alpha': alpha * 252,  # 年化alpha
                    })
                
                # 跟踪误差
                tracking_error = (aligned_strategy - aligned_benchmark_ret).std() * (252**0.5)
                analysis['tracking_error'] = tracking_error
                
                # 信息比率
                if tracking_error > 0:
                    information_ratio = (aligned_strategy.mean() - aligned_benchmark_ret.mean()) * 252 / tracking_error
                    analysis['information_ratio'] = information_ratio
        
        return analysis
    
    def _generate_html_report(self, data: Dict[str, Any]) -> str:
        """生成HTML报告"""
        # HTML模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 2px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #007acc;
            margin: 0;
            font-size: 2.5em;
        }}
        .header p {{
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-left: 4px solid #007acc;
            padding-left: 15px;
            margin-bottom: 20px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .metric-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007acc;
        }}
        .metric-card h3 {{
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1em;
        }}
        .metric-card .value {{
            font-size: 1.8em;
            font-weight: bold;
            color: #007acc;
        }}
        .chart-container {{
            margin: 20px 0;
            text-align: center;
        }}
        .table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .table th, .table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        .table th {{
            background-color: #007acc;
            color: white;
        }}
        .table tr:hover {{
            background-color: #f5f5f5;
        }}
        .positive {{
            color: #28a745;
        }}
        .negative {{
            color: #dc3545;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{title}</h1>
            <p>策略: {strategy_name} | 标的: {symbol} | 时间: {start_date} - {end_date}</p>
        </div>
        
        {summary_section}
        {performance_section}
        {risk_section}
        {charts_section}
        {trades_section}
        
        <div class="footer">
            <p>报告生成时间: {generation_time} | 生成器: Quant_02 回测系统</p>
        </div>
    </div>
</body>
</html>
        """
        
        # 构建各个部分
        sections = {}
        
        # 摘要部分
        if self.config.include_summary:
            sections['summary_section'] = self._build_summary_section(data)
        else:
            sections['summary_section'] = ""
        
        # 性能部分
        if self.config.include_performance:
            sections['performance_section'] = self._build_performance_section(data)
        else:
            sections['performance_section'] = ""
        
        # 风险部分
        if self.config.include_risk_analysis:
            sections['risk_section'] = self._build_risk_section(data)
        else:
            sections['risk_section'] = ""
        
        # 图表部分
        if self.config.include_charts and 'charts' in data:
            sections['charts_section'] = self._build_charts_section(data)
        else:
            sections['charts_section'] = ""
        
        # 交易部分
        if self.config.include_trades:
            sections['trades_section'] = self._build_trades_section(data)
        else:
            sections['trades_section'] = ""
        
        # 填充模板
        strategy_info = data['strategy_info']
        content = html_template.format(
            title=self.config.title,
            strategy_name=strategy_info['name'],
            symbol=strategy_info['symbol'],
            start_date=strategy_info['start_date'].strftime('%Y-%m-%d'),
            end_date=strategy_info['end_date'].strftime('%Y-%m-%d'),
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            **sections
        )
        
        return content
    
    def _build_summary_section(self, data: Dict[str, Any]) -> str:
        """构建摘要部分"""
        metrics = data['performance_metrics']
        additional = data.get('additional_metrics', {})
        
        summary_html = f"""
        <div class="section">
            <h2>策略概览</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>总收益率</h3>
                    <div class="value {'positive' if metrics['total_return'] > 0 else 'negative'}">
                        {self._format_number(metrics['total_return'], 'percentage')}
                    </div>
                </div>
                <div class="metric-card">
                    <h3>年化收益率</h3>
                    <div class="value {'positive' if metrics['annual_return'] > 0 else 'negative'}">
                        {self._format_number(metrics['annual_return'], 'percentage')}
                    </div>
                </div>
                <div class="metric-card">
                    <h3>夏普比率</h3>
                    <div class="value {'positive' if metrics['sharpe_ratio'] > 0 else 'negative'}">
                        {self._format_number(metrics['sharpe_ratio'], 'ratio')}
                    </div>
                </div>
                <div class="metric-card">
                    <h3>最大回撤</h3>
                    <div class="value negative">
                        {self._format_number(metrics['max_drawdown'], 'percentage')}
                    </div>
                </div>
                <div class="metric-card">
                    <h3>总交易次数</h3>
                    <div class="value">
                        {metrics['total_trades']}
                    </div>
                </div>
                <div class="metric-card">
                    <h3>胜率</h3>
                    <div class="value {'positive' if metrics['win_rate'] > 0.5 else 'negative'}">
                        {self._format_number(metrics['win_rate'], 'percentage')}
                    </div>
                </div>
            </div>
        </div>
        """
        
        return summary_html
    
    def _build_performance_section(self, data: Dict[str, Any]) -> str:
        """构建性能部分"""
        additional = data.get('additional_metrics', {})
        
        performance_html = f"""
        <div class="section">
            <h2>详细性能指标</h2>
            <table class="table">
                <tr><th>指标</th><th>数值</th></tr>
                <tr><td>交易天数</td><td>{additional.get('trading_days', 'N/A')}</td></tr>
                <tr><td>平均日收益率</td><td>{self._format_number(additional.get('avg_daily_return', 0), 'percentage')}</td></tr>
                <tr><td>日波动率</td><td>{self._format_number(additional.get('daily_volatility', 0), 'percentage')}</td></tr>
                <tr><td>偏度</td><td>{self._format_number(additional.get('skewness', 0), 'ratio')}</td></tr>
                <tr><td>峰度</td><td>{self._format_number(additional.get('kurtosis', 0), 'ratio')}</td></tr>
                <tr><td>最佳单日</td><td class="positive">{self._format_number(additional.get('best_day', 0), 'percentage')}</td></tr>
                <tr><td>最差单日</td><td class="negative">{self._format_number(additional.get('worst_day', 0), 'percentage')}</td></tr>
            </table>
        </div>
        """
        
        return performance_html
    
    def _build_risk_section(self, data: Dict[str, Any]) -> str:
        """构建风险部分"""
        additional = data.get('additional_metrics', {})
        
        risk_html = f"""
        <div class="section">
            <h2>风险分析</h2>
            <table class="table">
                <tr><th>风险指标</th><th>数值</th></tr>
                <tr><td>当前回撤</td><td class="negative">{self._format_number(additional.get('current_drawdown', 0), 'percentage')}</td></tr>
                <tr><td>平均回撤</td><td class="negative">{self._format_number(additional.get('avg_drawdown', 0), 'percentage')}</td></tr>
                <tr><td>VaR (95%)</td><td class="negative">{self._format_number(additional.get('var_95', 0), 'percentage')}</td></tr>
                <tr><td>VaR (99%)</td><td class="negative">{self._format_number(additional.get('var_99', 0), 'percentage')}</td></tr>
                <tr><td>回撤期数</td><td>{additional.get('drawdown_periods', 'N/A')}</td></tr>
            </table>
        </div>
        """
        
        return risk_html
    
    def _build_charts_section(self, data: Dict[str, Any]) -> str:
        """构建图表部分"""
        charts = data.get('charts', {})
        
        charts_html = '<div class="section"><h2>图表分析</h2>'
        
        for chart_name, chart_html in charts.items():
            charts_html += f'<div class="chart-container">{chart_html}</div>'
        
        charts_html += '</div>'
        
        return charts_html
    
    def _build_trades_section(self, data: Dict[str, Any]) -> str:
        """构建交易部分"""
        trades = data['trades']
        
        if not trades:
            return '<div class="section"><h2>交易明细</h2><p>无交易记录</p></div>'
        
        trades_html = """
        <div class="section">
            <h2>交易明细</h2>
            <table class="table">
                <tr>
                    <th>时间</th>
                    <th>标的</th>
                    <th>方向</th>
                    <th>数量</th>
                    <th>价格</th>
                    <th>金额</th>
                    <th>手续费</th>
                </tr>
        """
        
        for trade in trades[-20:]:  # 只显示最近20笔交易
            side_text = "买入" if trade.side.value == "buy" else "卖出"
            side_class = "positive" if trade.side.value == "buy" else "negative"
            
            trades_html += f"""
                <tr>
                    <td>{trade.timestamp.strftime('%Y-%m-%d %H:%M')}</td>
                    <td>{trade.symbol}</td>
                    <td class="{side_class}">{side_text}</td>
                    <td>{trade.quantity}</td>
                    <td>{trade.price:.2f}</td>
                    <td>{trade.quantity * trade.price:.2f}</td>
                    <td>{trade.commission:.2f}</td>
                </tr>
            """
        
        trades_html += """
            </table>
        </div>
        """
        
        return trades_html
    
    def _generate_markdown_report(self, data: Dict[str, Any]) -> str:
        """生成Markdown报告"""
        strategy_info = data['strategy_info']
        metrics = data['performance_metrics']
        
        markdown_content = f"""# {self.config.title}

## 策略概览

- **策略名称**: {strategy_info['name']}
- **交易标的**: {strategy_info['symbol']}
- **回测期间**: {strategy_info['start_date'].strftime('%Y-%m-%d')} 至 {strategy_info['end_date'].strftime('%Y-%m-%d')}
- **总天数**: {strategy_info['total_days']}天

## 核心指标

| 指标 | 数值 |
|------|------|
| 总收益率 | {self._format_number(metrics['total_return'], 'percentage')} |
| 年化收益率 | {self._format_number(metrics['annual_return'], 'percentage')} |
| 波动率 | {self._format_number(metrics['volatility'], 'percentage')} |
| 夏普比率 | {self._format_number(metrics['sharpe_ratio'], 'ratio')} |
| 最大回撤 | {self._format_number(metrics['max_drawdown'], 'percentage')} |
| 总交易次数 | {metrics['total_trades']} |
| 胜率 | {self._format_number(metrics['win_rate'], 'percentage')} |

## 风险分析

{self._build_markdown_risk_section(data)}

## 交易统计

{self._build_markdown_trades_section(data)}

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return markdown_content
    
    def _build_markdown_risk_section(self, data: Dict[str, Any]) -> str:
        """构建Markdown风险部分"""
        additional = data.get('additional_metrics', {})
        
        return f"""
| 风险指标 | 数值 |
|----------|------|
| 当前回撤 | {self._format_number(additional.get('current_drawdown', 0), 'percentage')} |
| 平均回撤 | {self._format_number(additional.get('avg_drawdown', 0), 'percentage')} |
| VaR (95%) | {self._format_number(additional.get('var_95', 0), 'percentage')} |
| VaR (99%) | {self._format_number(additional.get('var_99', 0), 'percentage')} |
| 最佳单日 | {self._format_number(additional.get('best_day', 0), 'percentage')} |
| 最差单日 | {self._format_number(additional.get('worst_day', 0), 'percentage')} |
"""
    
    def _build_markdown_trades_section(self, data: Dict[str, Any]) -> str:
        """构建Markdown交易部分"""
        additional = data.get('additional_metrics', {})
        
        return f"""
| 交易统计 | 数值 |
|----------|------|
| 平均交易收益 | {self._format_number(additional.get('avg_trade_return', 0), 'percentage')} |
| 平均盈利交易 | {self._format_number(additional.get('avg_winning_trade', 0), 'percentage')} |
| 平均亏损交易 | {self._format_number(additional.get('avg_losing_trade', 0), 'percentage')} |
| 最大盈利 | {self._format_number(additional.get('largest_win', 0), 'percentage')} |
| 最大亏损 | {self._format_number(additional.get('largest_loss', 0), 'percentage')} |
| 盈亏比 | {self._format_number(additional.get('profit_factor', 0), 'ratio')} |
"""
