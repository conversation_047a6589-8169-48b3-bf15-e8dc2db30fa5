"""
量化交易回测框架主程序

提供命令行接口和程序化接口，支持多种数据源、策略和配置选项。
"""

import argparse
from utils.logger import get_logger
import importlib
import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dataseed import Akshare, Database, Mock
from strategies import MACross, RSI
from core import QuantEngine, ConfigManager, ReportGenerator
from utils.logger import setup_logger, get_default_logger


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='量化交易回测框架')
    
    # 基本参数
    parser.add_argument('--symbol', type=str, required=True, help='证券代码')
    parser.add_argument('--start', type=str, required=True, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end', type=str, required=True, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--strategy', type=str, required=True, help='策略名称')
    parser.add_argument('--timeframe', type=str, default='1d', help='时间周期 (1d, 1h, 1m)')
    
    # 策略参数
    parser.add_argument('--params', type=str, help='策略参数 (JSON格式)')
    
    # 数据源参数
    parser.add_argument('--dataseed', type=str, default='akshare', 
                       choices=['akshare', 'database', 'mock'], help='数据源类型')
    
    # 回测参数
    parser.add_argument('--capital', type=float, default=100000.0, help='初始资金')
    parser.add_argument('--commission', type=float, default=0.0003, help='手续费率')
    parser.add_argument('--slippage', type=float, default=0.001, help='滑点率')
    parser.add_argument('--position-size', type=float, default=1.0, help='仓位比例')
    
    # 输出参数
    parser.add_argument('--output', type=str, default='./output', help='输出目录')
    parser.add_argument('--template', type=str, default='report_template_01.html', help='报告模板')
    parser.add_argument('--json-only', action='store_true', help='仅生成JSON报告')
    
    # 其他参数
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--log-level', type=str, default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='日志级别')
    parser.add_argument('--no-risk-management', action='store_true', help='禁用风险管理')
    
    # 多资产回测
    parser.add_argument('--symbols', type=str, nargs='+', help='多个证券代码')
    parser.add_argument('--parallel', action='store_true', help='并行处理多资产')
    
    # 参数优化
    parser.add_argument('--optimize', action='store_true', help='启用参数优化')
    parser.add_argument('--optimize-params', type=str, help='优化参数范围 (JSON格式)')
    parser.add_argument('--optimize-metric', type=str, default='sharpe_ratio', help='优化目标指标')

    return parser.parse_args()


def load_dataseed(dataseed_type: str, config: ConfigManager):
    """加载数据源"""
    if dataseed_type == 'akshare':
        return Akshare()
    elif dataseed_type == 'database':
        connection_string = config.get('dataseed.database.connection_string')
        return Database(connection_string)
    elif dataseed_type == 'mock':
        return Mock()
    else:
        raise ValueError(f"不支持的数据源类型: {dataseed_type}")


def load_strategy(strategy_name: str, params: Optional[Dict[str, Any]] = None):
    """加载策略"""
    strategy_map = {
        'ma_cross': MACross,
        'rsi': RSI
    }
    
    if strategy_name not in strategy_map:
        raise ValueError(f"不支持的策略: {strategy_name}")
    
    strategy_class = strategy_map[strategy_name]
    return strategy_class(params)


def clean_symbol(symbol: str) -> str:
    """清理证券代码格式"""
    # 自动添加市场前缀
    if not symbol.startswith(('sh', 'sz')):
        if symbol.startswith(('000', '001', '002', '300')):
            return 'sz' + symbol
        elif symbol.startswith(('600', '601', '603')):
            return 'sh' + symbol
        else:
            return symbol
    return symbol


def run_single_backtest(args, config: ConfigManager, logger):
    """运行单资产回测"""
    logger.info(f"开始单资产回测: {args.symbol}")
    
    # 加载数据源
    dataseed = load_dataseed(args.dataseed, config)
    
    # 解析策略参数
    strategy_params = {}
    if args.params:
        try:
            strategy_params = json.loads(args.params)
        except json.JSONDecodeError as e:
            logger.error(f"策略参数JSON格式错误: {e}")
            return None
    
    # 加载策略
    strategy = load_strategy(args.strategy, strategy_params)
    
    # 创建回测引擎
    engine = QuantEngine(
        dataseed=dataseed,
        strategy=strategy,
        initial_capital=args.capital,
        commission=args.commission,
        slippage=args.slippage,
        position_size=args.position_size,
        config=config
    )
    
    # 清理证券代码
    symbol = clean_symbol(args.symbol)
    
    # 参数优化
    if args.optimize:
        if not args.optimize_params:
            logger.error("启用参数优化时必须提供优化参数范围")
            return None
        
        try:
            param_ranges = json.loads(args.optimize_params)
            result = engine.optimize_parameters(
                symbol=symbol,
                start_date=args.start,
                end_date=args.end,
                param_ranges=param_ranges,
                timeframe=args.timeframe,
                metric=args.optimize_metric
            )
            
            logger.info(f"参数优化完成，最优参数: {result['best_params']}")
            logger.info(f"最优{args.optimize_metric}: {result['best_score']:.4f}")
            
            # 使用最优参数重新运行回测
            strategy.set_params(result['best_params'])
        
        except json.JSONDecodeError as e:
            logger.error(f"优化参数JSON格式错误: {e}")
            return None
    
    # 运行回测
    portfolio, metrics = engine.run(
        symbol=symbol,
        start_date=args.start,
        end_date=args.end,
        timeframe=args.timeframe,
        apply_risk_management=not args.no_risk_management
    )
    
    # 生成报告
    report_generator = ReportGenerator(portfolio, symbol, args.strategy, config)
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 保存JSON报告
    json_file = report_generator.save_report_json(args.output)
    logger.info(f"JSON报告已保存: {json_file}")
    
    # 输出关键指标
    logger.info(f"回测完成 - 总收益率: {metrics['total_return']:.2%}, "
               f"夏普比率: {metrics['sharpe_ratio']:.2f}, "
               f"最大回撤: {metrics['max_drawdown']:.2%}")
    
    return portfolio, metrics


def run_multiple_backtest(args, config: ConfigManager, logger):
    """运行多资产回测"""
    logger.info(f"开始多资产回测: {args.symbols}")
    
    # 加载数据源和策略
    dataseed = load_dataseed(args.dataseed, config)
    
    strategy_params = {}
    if args.params:
        try:
            strategy_params = json.loads(args.params)
        except json.JSONDecodeError as e:
            logger.error(f"策略参数JSON格式错误: {e}")
            return None
    
    strategy = load_strategy(args.strategy, strategy_params)
    
    # 创建回测引擎
    engine = QuantEngine(
        dataseed=dataseed,
        strategy=strategy,
        initial_capital=args.capital,
        commission=args.commission,
        slippage=args.slippage,
        position_size=args.position_size,
        config=config
    )
    
    # 清理证券代码
    symbols = [clean_symbol(symbol) for symbol in args.symbols]
    
    # 运行多资产回测
    portfolio, all_metrics = engine.run_multiple(
        symbols=symbols,
        start_date=args.start,
        end_date=args.end,
        timeframe=args.timeframe,
        apply_risk_management=not args.no_risk_management,
        parallel=args.parallel
    )
    
    # 生成组合报告
    report_generator = ReportGenerator(portfolio, '+'.join(symbols), args.strategy, config)
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 保存JSON报告
    json_file = report_generator.save_report_json(args.output)
    logger.info(f"组合报告已保存: {json_file}")
    
    # 输出每个资产的关键指标
    for symbol, metrics in all_metrics.items():
        logger.info(f"{symbol} - 总收益率: {metrics['total_return']:.2%}, "
                   f"夏普比率: {metrics['sharpe_ratio']:.2f}")
    
    return portfolio, all_metrics


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志
    logger = setup_logger('quant', args.log_level.upper())
    
    # 加载配置
    config = ConfigManager(args.config)
    
    try:
        # 根据参数选择运行模式
        if args.symbols:
            # 多资产回测
            result = run_multiple_backtest(args, config, logger)
        else:
            # 单资产回测
            result = run_single_backtest(args, config, logger)
        
        if result is None:
            logger.error("回测失败")
            sys.exit(1)
        
        logger.info("回测完成")
        
    except Exception as e:
        logger.error(f"回测过程中发生错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
