"""
量化回测引擎

基于VectorBT的高性能量化回测系统，支持A股、基金、可转债等多种资产类别。

主要特性：
- 模块化设计，易于扩展
- 高性能向量化计算
- 多种策略类型支持
- 完整的风险管理
- 丰富的可视化报告
"""

__version__ = "1.0.0"
__author__ = "Quant Team"
__email__ = "<EMAIL>"

# 导入核心模块
from .core.engine.base import BacktestEngine
from .core.config import GlobalConfig
from .dataseed.base import DataSeed
from .strategies.base import BaseStrategy

# 导入常用策略
from .strategies.single.macd import MACDStrategy
from .strategies.single.rsi import RSIStrategy

# 导入数据源
from .dataseed.akshare import AkShareDataSeed
from .dataseed.mock import MockDataSeed

__all__ = [
    "BacktestEngine",
    "GlobalConfig", 
    "DataSeed",
    "BaseStrategy",
    "MACDStrategy",
    "RSIStrategy",
    "AkShareDataSeed",
    "MockDataSeed",
]
