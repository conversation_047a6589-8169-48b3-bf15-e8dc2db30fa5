"""
交易数据结构模块

定义量化交易系统中使用的交易相关数据结构。
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum
import uuid


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    PARTIAL_FILLED = "partial_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class PositionSide(Enum):
    """持仓方向"""
    LONG = "long"
    SHORT = "short"
    FLAT = "flat"


@dataclass
class Order:
    """订单数据结构
    
    优化特性：
    - 完整的订单生命周期管理
    - 支持多种订单类型
    - 详细的成交信息记录
    - 风险控制字段
    """
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    timestamp: datetime
    
    # 价格信息
    price: Optional[float] = None  # 限价单价格
    stop_price: Optional[float] = None  # 止损价格
    
    # 执行信息
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    remaining_quantity: Optional[float] = None
    
    # 费用信息
    commission: float = 0.0
    slippage: float = 0.0
    total_cost: float = 0.0
    
    # 策略信息
    strategy_id: Optional[str] = None
    signal_id: Optional[str] = None
    
    # 风控信息
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # 时间信息
    created_time: Optional[datetime] = None
    updated_time: Optional[datetime] = None
    filled_time: Optional[datetime] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.order_id:
            self.order_id = str(uuid.uuid4())
        
        if self.remaining_quantity is None:
            self.remaining_quantity = self.quantity
        
        if self.created_time is None:
            self.created_time = self.timestamp
    
    @property
    def is_buy(self) -> bool:
        """是否买单"""
        return self.side == OrderSide.BUY
    
    @property
    def is_sell(self) -> bool:
        """是否卖单"""
        return self.side == OrderSide.SELL
    
    @property
    def is_market_order(self) -> bool:
        """是否市价单"""
        return self.order_type == OrderType.MARKET
    
    @property
    def is_limit_order(self) -> bool:
        """是否限价单"""
        return self.order_type == OrderType.LIMIT
    
    @property
    def is_pending(self) -> bool:
        """是否待成交"""
        return self.status == OrderStatus.PENDING
    
    @property
    def is_filled(self) -> bool:
        """是否已成交"""
        return self.status == OrderStatus.FILLED
    
    @property
    def is_cancelled(self) -> bool:
        """是否已取消"""
        return self.status == OrderStatus.CANCELLED
    
    @property
    def fill_ratio(self) -> float:
        """成交比例"""
        if self.quantity == 0:
            return 0.0
        return self.filled_quantity / self.quantity
    
    @property
    def notional_value(self) -> float:
        """名义价值"""
        price = self.price if self.price is not None else self.avg_fill_price
        return self.quantity * price if price > 0 else 0.0
    
    def update_fill(self, fill_quantity: float, fill_price: float):
        """更新成交信息"""
        if fill_quantity <= 0:
            return
        
        # 更新成交数量和平均价格
        total_filled = self.filled_quantity + fill_quantity
        if total_filled > 0:
            self.avg_fill_price = (
                (self.avg_fill_price * self.filled_quantity + fill_price * fill_quantity) 
                / total_filled
            )
        
        self.filled_quantity = total_filled
        self.remaining_quantity = max(0, self.quantity - self.filled_quantity)
        
        # 更新状态
        if self.remaining_quantity <= 1e-8:
            self.status = OrderStatus.FILLED
            self.filled_time = datetime.now()
        elif self.filled_quantity > 0:
            self.status = OrderStatus.PARTIAL_FILLED
        
        self.updated_time = datetime.now()
    
    def cancel(self):
        """取消订单"""
        if self.status in [OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED]:
            self.status = OrderStatus.CANCELLED
            self.updated_time = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'status': self.status.value,
            'filled_quantity': self.filled_quantity,
            'avg_fill_price': self.avg_fill_price,
            'remaining_quantity': self.remaining_quantity,
            'commission': self.commission,
            'slippage': self.slippage,
            'total_cost': self.total_cost,
            'strategy_id': self.strategy_id,
            'signal_id': self.signal_id,
            'timestamp': self.timestamp,
            'created_time': self.created_time,
            'updated_time': self.updated_time,
            'filled_time': self.filled_time,
        }


@dataclass
class Position:
    """持仓数据结构
    
    优化特性：
    - 实时盈亏计算
    - 持仓成本跟踪
    - 风险指标监控
    """
    symbol: str
    side: PositionSide
    quantity: float
    avg_price: float
    timestamp: datetime
    
    # 成本信息
    total_cost: float = 0.0
    commission: float = 0.0
    
    # 盈亏信息
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    
    # 风险信息
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # 策略信息
    strategy_id: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_long(self) -> bool:
        """是否多头持仓"""
        return self.side == PositionSide.LONG
    
    @property
    def is_short(self) -> bool:
        """是否空头持仓"""
        return self.side == PositionSide.SHORT
    
    @property
    def is_flat(self) -> bool:
        """是否空仓"""
        return self.side == PositionSide.FLAT or abs(self.quantity) < 1e-8
    
    @property
    def market_value(self) -> float:
        """市值（需要当前价格）"""
        # 这里需要外部提供当前价格
        return self.quantity * self.avg_price
    
    @property
    def notional_value(self) -> float:
        """名义价值"""
        return abs(self.quantity) * self.avg_price
    
    def update_unrealized_pnl(self, current_price: float):
        """更新未实现盈亏"""
        if self.is_flat:
            self.unrealized_pnl = 0.0
            return
        
        if self.is_long:
            self.unrealized_pnl = (current_price - self.avg_price) * self.quantity
        else:
            self.unrealized_pnl = (self.avg_price - current_price) * abs(self.quantity)
    
    def add_trade(self, trade_quantity: float, trade_price: float, commission: float = 0.0):
        """添加交易到持仓"""
        if trade_quantity == 0:
            return
        
        # 计算新的平均价格和数量
        if self.quantity == 0:
            # 新开仓
            self.quantity = trade_quantity
            self.avg_price = trade_price
            self.side = PositionSide.LONG if trade_quantity > 0 else PositionSide.SHORT
        else:
            # 加仓或减仓
            new_quantity = self.quantity + trade_quantity
            
            if new_quantity == 0:
                # 平仓
                self.quantity = 0
                self.side = PositionSide.FLAT
                self.avg_price = 0.0
            elif (self.quantity > 0 and new_quantity > 0) or (self.quantity < 0 and new_quantity < 0):
                # 同向加仓
                total_cost = self.avg_price * abs(self.quantity) + trade_price * abs(trade_quantity)
                total_quantity = abs(self.quantity) + abs(trade_quantity)
                self.avg_price = total_cost / total_quantity
                self.quantity = new_quantity
            else:
                # 反向交易
                if abs(new_quantity) < abs(self.quantity):
                    # 部分平仓
                    self.quantity = new_quantity
                else:
                    # 反向开仓
                    self.quantity = new_quantity
                    self.avg_price = trade_price
                    self.side = PositionSide.LONG if new_quantity > 0 else PositionSide.SHORT
        
        # 更新成本和手续费
        self.total_cost += abs(trade_quantity) * trade_price + commission
        self.commission += commission
        self.timestamp = datetime.now()


@dataclass
class Trade:
    """交易记录数据结构"""
    trade_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: datetime
    
    # 关联信息
    order_id: Optional[str] = None
    strategy_id: Optional[str] = None
    
    # 费用信息
    commission: float = 0.0
    slippage: float = 0.0
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.trade_id:
            self.trade_id = str(uuid.uuid4())
    
    @property
    def is_buy(self) -> bool:
        return self.side == OrderSide.BUY
    
    @property
    def is_sell(self) -> bool:
        return self.side == OrderSide.SELL
    
    @property
    def total_value(self) -> float:
        """交易总价值"""
        return self.quantity * self.price
    
    @property
    def total_cost(self) -> float:
        """交易总成本（包含费用）"""
        return self.total_value + self.commission + self.slippage
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'trade_id': self.trade_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': self.quantity,
            'price': self.price,
            'timestamp': self.timestamp,
            'order_id': self.order_id,
            'strategy_id': self.strategy_id,
            'commission': self.commission,
            'slippage': self.slippage,
            'total_value': self.total_value,
            'total_cost': self.total_cost,
        }
