"""
时间相关装饰器模块

提供性能计时和超时控制装饰器。
"""

import time
import signal
import threading
from functools import wraps
from typing import Optional, Callable, Any

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from utils.logger import get_logger

logger = get_logger(__name__)


def timing(log_level: str = 'info', include_args: bool = False):
    """
    函数执行时间计时装饰器
    
    Args:
        log_level: 日志级别
        include_args: 是否包含参数信息
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 构建日志消息
                msg = f"{func.__name__} 执行时间: {execution_time:.4f}秒"
                
                if include_args:
                    args_str = ', '.join([str(arg)[:50] for arg in args])
                    kwargs_str = ', '.join([f"{k}={str(v)[:50]}" for k, v in kwargs.items()])
                    if args_str or kwargs_str:
                        msg += f" (参数: {args_str}, {kwargs_str})"
                
                # 记录日志
                if log_level.lower() == 'debug':
                    logger.debug(msg)
                elif log_level.lower() == 'info':
                    logger.info(msg)
                elif log_level.lower() == 'warning':
                    logger.warning(msg)
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{func.__name__} 执行失败，耗时: {execution_time:.4f}秒, 错误: {e}")
                raise
        
        return wrapper
    return decorator


class TimeoutError(Exception):
    """超时异常"""
    pass


def timeout(seconds: float):
    """
    函数执行超时装饰器
    
    Args:
        seconds: 超时时间（秒）
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 使用线程实现超时控制
            result = [None]
            exception = [None]
            
            def target():
                try:
                    result[0] = func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
            
            thread = threading.Thread(target=target)
            thread.daemon = True
            thread.start()
            thread.join(seconds)
            
            if thread.is_alive():
                # 超时了，但无法强制终止线程
                logger.warning(f"{func.__name__} 执行超时 ({seconds}秒)")
                raise TimeoutError(f"函数 {func.__name__} 执行超时 ({seconds}秒)")
            
            if exception[0]:
                raise exception[0]
            
            return result[0]
        
        return wrapper
    return decorator


def rate_limit(calls_per_second: float):
    """
    速率限制装饰器
    
    Args:
        calls_per_second: 每秒允许的调用次数
    """
    min_interval = 1.0 / calls_per_second
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        
        return wrapper
    return decorator


def debounce(wait_time: float):
    """
    防抖装饰器 - 在指定时间内只执行最后一次调用
    
    Args:
        wait_time: 等待时间（秒）
    """
    def decorator(func):
        timer = [None]
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            def call_func():
                func(*args, **kwargs)
            
            # 取消之前的定时器
            if timer[0] is not None:
                timer[0].cancel()
            
            # 设置新的定时器
            timer[0] = threading.Timer(wait_time, call_func)
            timer[0].start()
        
        return wrapper
    return decorator


def throttle(wait_time: float):
    """
    节流装饰器 - 在指定时间内最多执行一次
    
    Args:
        wait_time: 等待时间（秒）
    """
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            now = time.time()
            
            if now - last_called[0] >= wait_time:
                last_called[0] = now
                return func(*args, **kwargs)
            else:
                logger.debug(f"{func.__name__} 被节流限制跳过")
        
        return wrapper
    return decorator


def benchmark(iterations: int = 1):
    """
    基准测试装饰器
    
    Args:
        iterations: 执行次数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            times = []
            result = None
            
            for i in range(iterations):
                start_time = time.time()
                result = func(*args, **kwargs)
                end_time = time.time()
                times.append(end_time - start_time)
            
            # 计算统计信息
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            logger.info(f"{func.__name__} 基准测试结果:")
            logger.info(f"  执行次数: {iterations}")
            logger.info(f"  平均时间: {avg_time:.4f}秒")
            logger.info(f"  最短时间: {min_time:.4f}秒")
            logger.info(f"  最长时间: {max_time:.4f}秒")
            
            return result
        
        return wrapper
    return decorator


def profile_memory():
    """
    内存使用分析装饰器
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                import psutil
                import os
                
                process = psutil.Process(os.getpid())
                
                # 执行前的内存使用
                mem_before = process.memory_info().rss / 1024 / 1024  # MB
                
                result = func(*args, **kwargs)
                
                # 执行后的内存使用
                mem_after = process.memory_info().rss / 1024 / 1024  # MB
                mem_diff = mem_after - mem_before
                
                logger.info(f"{func.__name__} 内存使用:")
                logger.info(f"  执行前: {mem_before:.2f} MB")
                logger.info(f"  执行后: {mem_after:.2f} MB")
                logger.info(f"  差异: {mem_diff:+.2f} MB")
                
                return result
                
            except ImportError:
                logger.warning("psutil未安装，无法进行内存分析")
                return func(*args, **kwargs)
        
        return wrapper
    return decorator
