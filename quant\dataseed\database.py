"""
数据库数据源

基于数据库的数据源实现，支持从SQL数据库获取历史数据。
"""

import pandas as pd
import sqlite3
from sqlalchemy import create_engine
from dataseed.base import BaseDataSeed
from utils.logger import get_default_logger


class Database(BaseDataSeed):
    """基于数据库的数据源实现"""
    
    def __init__(self, connection_string: str = "sqlite:///market_data.db"):
        """
        初始化数据库数据源
        
        参数:
            connection_string (str): 数据库连接字符串
        """
        self.connection_string = connection_string
        self.engine = create_engine(connection_string)
        self.logger = get_default_logger()
    
    def get_data(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d') -> pd.DataFrame:
        """
        从数据库获取数据
        
        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期，格式：YYYY-MM-DD
            end_date (str): 结束日期，格式：YYYY-MM-DD
            timeframe (str): 时间周期，支持 '1d', '1h', '1m'
            
        返回:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        try:
            self.logger.info(f"正在从数据库获取数据: {symbol}, {start_date} 到 {end_date}")
            
            # 根据时间周期选择不同的表
            table_name = self._get_table_name(timeframe)
            
            # 构建查询语句
            query = self._build_query(table_name, symbol, start_date, end_date)
            
            # 执行查询
            data = pd.read_sql(query, self.engine)
            
            if data.empty:
                self.logger.warning(f"未获取到数据: {symbol}")
                return pd.DataFrame()
            
            # 标准化数据格式
            data = self.standardize_columns(data)
            
            # 设置日期索引
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                data.set_index('date', inplace=True)
            
            # 验证数据
            if not self.validate_data(data):
                self.logger.error(f"数据验证失败: {symbol}")
                return pd.DataFrame()
            
            self.logger.info(f"成功获取数据: {len(data)} 条记录")
            return data
            
        except Exception as e:
            self.logger.error(f"从数据库获取数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _get_table_name(self, timeframe: str) -> str:
        """
        根据时间周期获取表名
        
        参数:
            timeframe (str): 时间周期
            
        返回:
            str: 表名
        """
        table_mapping = {
            '1d': 'market_data_daily',
            '1h': 'market_data_hourly', 
            '1m': 'market_data_minute'
        }
        
        return table_mapping.get(timeframe, 'market_data_daily')
    
    def _build_query(self, table_name: str, symbol: str, start_date: str, end_date: str) -> str:
        """
        构建SQL查询语句
        
        参数:
            table_name (str): 表名
            symbol (str): 证券代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            
        返回:
            str: SQL查询语句
        """
        query = f"""
        SELECT * FROM {table_name}
        WHERE symbol = '{symbol}'
        AND date >= '{start_date}'
        AND date <= '{end_date}'
        ORDER BY date
        """
        return query
    
    def insert_data(self, data: pd.DataFrame, symbol: str, timeframe: str = '1d'):
        """
        向数据库插入数据
        
        参数:
            data (pd.DataFrame): 要插入的数据
            symbol (str): 证券代码
            timeframe (str): 时间周期
        """
        try:
            table_name = self._get_table_name(timeframe)
            
            # 添加symbol列
            data_copy = data.copy()
            data_copy['symbol'] = symbol
            
            # 重置索引，将日期作为列
            if data_copy.index.name == 'date' or 'date' in str(data_copy.index.name):
                data_copy.reset_index(inplace=True)
            
            # 插入数据
            data_copy.to_sql(table_name, self.engine, if_exists='append', index=False)
            
            self.logger.info(f"成功插入数据: {len(data_copy)} 条记录到表 {table_name}")
            
        except Exception as e:
            self.logger.error(f"插入数据失败: {str(e)}")
    
    def create_tables(self):
        """创建数据表"""
        try:
            # 创建日线数据表
            create_daily_table = """
            CREATE TABLE IF NOT EXISTS market_data_daily (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date DATE NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume INTEGER NOT NULL,
                UNIQUE(symbol, date)
            )
            """
            
            # 创建小时线数据表
            create_hourly_table = """
            CREATE TABLE IF NOT EXISTS market_data_hourly (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date DATETIME NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume INTEGER NOT NULL,
                UNIQUE(symbol, date)
            )
            """
            
            # 创建分钟线数据表
            create_minute_table = """
            CREATE TABLE IF NOT EXISTS market_data_minute (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                date DATETIME NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume INTEGER NOT NULL,
                UNIQUE(symbol, date)
            )
            """
            
            with self.engine.connect() as conn:
                conn.execute(create_daily_table)
                conn.execute(create_hourly_table)
                conn.execute(create_minute_table)
            
            self.logger.info("数据表创建成功")
            
        except Exception as e:
            self.logger.error(f"创建数据表失败: {str(e)}")
