"""
策略模块

提供量化交易策略的开发框架和常用策略实现。
"""

from .base import BaseStrategy, StrategyConfig, StrategyResult
from .factory import (
    StrategyFactory, strategy_factory,
    create_strategy, list_strategies, register_strategy
)
from .registry import (
    StrategyRegistry, strategy_registry,
    get_strategy, list_strategies_by_category, search_strategies
)

# 导入具体策略
try:
    from .single.macd import MACDStrategy, MACDConfig
    _MACD_AVAILABLE = True
except ImportError:
    MACDStrategy = None
    MACDConfig = None
    _MACD_AVAILABLE = False

__all__ = [
    # 基础类
    "BaseStrategy",
    "StrategyConfig",
    "StrategyResult",
    
    # 工厂和注册器
    "StrategyFactory",
    "strategy_factory",
    "create_strategy",
    "list_strategies",
    "register_strategy",
    
    "StrategyRegistry", 
    "strategy_registry",
    "get_strategy",
    "list_strategies_by_category",
    "search_strategies",
]

# 动态添加可用的策略到__all__
if _MACD_AVAILABLE:
    __all__.extend(["MACDStrategy", "MACDConfig"])


def get_available_strategies():
    """获取可用的策略列表"""
    strategies = []
    
    if _MACD_AVAILABLE:
        strategies.append("macd")
    
    return strategies


def check_strategy_dependencies():
    """检查策略依赖项"""
    status = {
        "macd": _MACD_AVAILABLE,
    }
    
    return status
