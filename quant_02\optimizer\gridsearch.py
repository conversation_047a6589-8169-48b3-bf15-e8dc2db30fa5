"""
网格搜索优化器模块

实现网格搜索参数优化算法。
"""

from typing import Dict, List, Any, Optional
import itertools
import time
from datetime import datetime
import pandas as pd

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from .base import BaseOptimizer, OptimizationResult
from core.interfaces import IStrategy
from utils.logger import get_logger

logger = get_logger(__name__)


class GridSearchOptimizer(BaseOptimizer):
    """网格搜索优化器
    
    通过遍历所有参数组合来寻找最优参数：
    - 全面搜索所有组合
    - 结果可重现
    - 适合参数空间较小的情况
    - 支持并行计算
    """
    
    def __init__(
        self,
        objective_function=None,
        maximize: bool = True,
        random_state: Optional[int] = None,
        n_jobs: int = 1
    ):
        """
        初始化网格搜索优化器
        
        Args:
            objective_function: 目标函数
            maximize: 是否最大化目标函数
            random_state: 随机种子
            n_jobs: 并行作业数
        """
        super().__init__(objective_function, maximize, random_state)
        self.n_jobs = n_jobs
        
        logger.info(f"网格搜索优化器初始化完成，并行作业数: {n_jobs}")
    
    def optimize(
        self,
        strategy: IStrategy,
        data: pd.DataFrame,
        max_iterations: Optional[int] = None,
        verbose: bool = True,
        **kwargs
    ) -> OptimizationResult:
        """
        执行网格搜索优化
        
        Args:
            strategy: 策略对象
            data: 历史数据
            max_iterations: 最大迭代次数（对网格搜索无效）
            verbose: 是否显示详细信息
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        start_time = time.time()
        self._stats['start_time'] = datetime.now()
        
        try:
            # 生成所有参数组合
            param_combinations = self._generate_parameter_combinations()
            
            if not param_combinations:
                raise ValueError("没有参数空间定义")
            
            total_combinations = len(param_combinations)
            logger.info(f"开始网格搜索，总组合数: {total_combinations}")
            
            # 限制最大迭代次数
            if max_iterations and max_iterations < total_combinations:
                param_combinations = param_combinations[:max_iterations]
                logger.info(f"限制搜索组合数为: {max_iterations}")
            
            # 执行搜索
            if self.n_jobs == 1:
                self._sequential_search(strategy, data, param_combinations, verbose)
            else:
                self._parallel_search(strategy, data, param_combinations, verbose)
            
            # 计算优化时间
            optimization_time = time.time() - start_time
            self._stats['end_time'] = datetime.now()
            
            # 创建结果对象
            result = OptimizationResult(
                best_params=self._best_params,
                best_score=self._best_score,
                all_results=self._results,
                optimization_time=optimization_time,
                total_iterations=len(param_combinations),
                convergence_info={
                    'method': 'grid_search',
                    'total_combinations': total_combinations,
                    'evaluated_combinations': len(self._results),
                    'completion_rate': len(self._results) / total_combinations
                }
            )
            
            logger.info(f"网格搜索完成，最佳分数: {self._best_score:.4f}")
            logger.info(f"最佳参数: {self._best_params}")
            
            return result
            
        except Exception as e:
            logger.error(f"网格搜索失败: {e}")
            raise
    
    def _generate_parameter_combinations(self) -> List[Dict[str, Any]]:
        """生成所有参数组合"""
        if not self._parameter_spaces:
            return []
        
        # 为每个参数生成值列表
        param_values = {}
        for name, space in self._parameter_spaces.items():
            param_values[name] = space.generate_values()
        
        # 生成所有组合
        param_names = list(param_values.keys())
        value_combinations = itertools.product(*[param_values[name] for name in param_names])
        
        combinations = []
        for values in value_combinations:
            combination = dict(zip(param_names, values))
            combinations.append(combination)
        
        logger.debug(f"生成参数组合: {len(combinations)}个")
        return combinations
    
    def _sequential_search(
        self,
        strategy: IStrategy,
        data: pd.DataFrame,
        param_combinations: List[Dict[str, Any]],
        verbose: bool
    ):
        """顺序搜索"""
        total = len(param_combinations)
        
        for i, params in enumerate(param_combinations):
            try:
                score = self.evaluate_parameters(params, strategy, data)
                
                if verbose and (i + 1) % max(1, total // 10) == 0:
                    progress = (i + 1) / total * 100
                    logger.info(f"搜索进度: {progress:.1f}% ({i + 1}/{total}), 当前最佳: {self._best_score:.4f}")
                
            except Exception as e:
                logger.warning(f"参数组合评估失败: {params} - {e}")
                continue
    
    def _parallel_search(
        self,
        strategy: IStrategy,
        data: pd.DataFrame,
        param_combinations: List[Dict[str, Any]],
        verbose: bool
    ):
        """并行搜索"""
        try:
            from concurrent.futures import ProcessPoolExecutor, as_completed
            
            logger.info(f"使用并行搜索，进程数: {self.n_jobs}")
            
            with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
                # 提交所有任务
                future_to_params = {
                    executor.submit(self._evaluate_single_combination, strategy, data, params): params
                    for params in param_combinations
                }
                
                # 收集结果
                completed = 0
                total = len(param_combinations)
                
                for future in as_completed(future_to_params):
                    params = future_to_params[future]
                    
                    try:
                        score = future.result()
                        
                        # 手动更新结果（因为并行执行）
                        result = {
                            'params': params.copy(),
                            'score': score,
                            'timestamp': datetime.now()
                        }
                        self._results.append(result)
                        
                        # 更新最佳结果
                        if self._is_better_score(score):
                            self._best_score = score
                            self._best_params = params.copy()
                        
                        completed += 1
                        
                        if verbose and completed % max(1, total // 10) == 0:
                            progress = completed / total * 100
                            logger.info(f"搜索进度: {progress:.1f}% ({completed}/{total}), 当前最佳: {self._best_score:.4f}")
                    
                    except Exception as e:
                        logger.warning(f"并行任务失败: {params} - {e}")
                        completed += 1
                        continue
        
        except ImportError:
            logger.warning("无法使用并行处理，回退到顺序搜索")
            self._sequential_search(strategy, data, param_combinations, verbose)
    
    def _evaluate_single_combination(
        self,
        strategy: IStrategy,
        data: pd.DataFrame,
        params: Dict[str, Any]
    ) -> float:
        """评估单个参数组合（用于并行处理）"""
        # 创建策略副本以避免并行冲突
        strategy_copy = self._copy_strategy(strategy)
        
        # 更新参数
        for param_name, param_value in params.items():
            strategy_copy.set_parameter(param_name, param_value)
        
        # 计算分数
        if self.objective_function:
            return self.objective_function(strategy_copy, data, params)
        else:
            return self._default_objective_function(strategy_copy, data)
    
    def _copy_strategy(self, strategy: IStrategy) -> IStrategy:
        """创建策略副本"""
        # 简化实现：返回原策略（实际应用中需要深拷贝）
        return strategy
    
    def get_parameter_importance(self) -> Dict[str, float]:
        """计算参数重要性"""
        if not self._results:
            return {}
        
        df = pd.DataFrame(self._results)
        importance = {}
        
        for param_name in self._parameter_spaces.keys():
            if param_name in df.columns:
                # 计算参数与分数的相关性
                param_values = [result['params'][param_name] for result in self._results]
                scores = [result['score'] for result in self._results]
                
                correlation = pd.Series(param_values).corr(pd.Series(scores))
                importance[param_name] = abs(correlation) if not pd.isna(correlation) else 0.0
        
        return importance
    
    def get_parameter_sensitivity(self, param_name: str) -> Dict[str, Any]:
        """分析参数敏感性"""
        if param_name not in self._parameter_spaces or not self._results:
            return {}
        
        # 提取该参数的所有值和对应分数
        param_scores = {}
        for result in self._results:
            param_value = result['params'][param_name]
            score = result['score']
            
            if param_value not in param_scores:
                param_scores[param_value] = []
            param_scores[param_value].append(score)
        
        # 计算统计信息
        sensitivity = {}
        for value, scores in param_scores.items():
            sensitivity[value] = {
                'mean_score': np.mean(scores),
                'std_score': np.std(scores),
                'count': len(scores),
                'best_score': max(scores) if self.maximize else min(scores)
            }
        
        return sensitivity
    
    def plot_parameter_space(self, param1: str, param2: str, save_path: Optional[str] = None):
        """绘制二维参数空间图"""
        try:
            import matplotlib.pyplot as plt
            
            if not self._results:
                logger.warning("没有结果数据可绘制")
                return
            
            # 提取数据
            x_values = [result['params'][param1] for result in self._results if param1 in result['params']]
            y_values = [result['params'][param2] for result in self._results if param2 in result['params']]
            scores = [result['score'] for result in self._results]
            
            if not x_values or not y_values:
                logger.warning(f"参数 {param1} 或 {param2} 的数据不足")
                return
            
            # 创建散点图
            plt.figure(figsize=(10, 8))
            scatter = plt.scatter(x_values, y_values, c=scores, cmap='viridis', alpha=0.7)
            plt.colorbar(scatter, label='Score')
            plt.xlabel(param1)
            plt.ylabel(param2)
            plt.title(f'Parameter Space: {param1} vs {param2}')
            
            # 标记最佳点
            if self._best_params:
                best_x = self._best_params.get(param1)
                best_y = self._best_params.get(param2)
                if best_x is not None and best_y is not None:
                    plt.scatter([best_x], [best_y], c='red', s=100, marker='*', label='Best')
                    plt.legend()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"参数空间图已保存: {save_path}")
            else:
                plt.show()
            
            plt.close()
            
        except ImportError:
            logger.warning("matplotlib未安装，无法绘制图表")
        except Exception as e:
            logger.error(f"绘制参数空间图失败: {e}")
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化摘要"""
        if not self._results:
            return {}
        
        scores = [result['score'] for result in self._results]
        
        summary = {
            'total_evaluations': len(self._results),
            'best_score': self._best_score,
            'worst_score': min(scores) if self.maximize else max(scores),
            'mean_score': np.mean(scores),
            'std_score': np.std(scores),
            'parameter_importance': self.get_parameter_importance(),
            'convergence_info': {
                'improvement_iterations': len(self._stats['best_score_history']),
                'final_improvement_iteration': self._stats['best_score_history'][-1]['iteration'] if self._stats['best_score_history'] else 0
            }
        }
        
        return summary
