"""
多资产回测引擎

支持多个资产的组合策略回测。
"""

from typing import Dict, List, Optional, Any, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd
import numpy as np

from .base import BacktestEngine
from ...strategies.base import BaseStrategy, StrategyResult
from ...utils.logger import get_logger, log_execution_time

logger = get_logger(__name__)


class MultiAssetEngine(BacktestEngine):
    """多资产回测引擎"""
    
    def __init__(self, *args, max_workers: int = 4, **kwargs):
        """
        初始化多资产回测引擎
        
        Args:
            max_workers: 最大并行工作线程数
        """
        super().__init__(*args, **kwargs)
        self.max_workers = max_workers
    
    @log_execution_time()
    def run_strategy(
        self,
        strategy: BaseStrategy,
        symbols: List[str],
        parallel: bool = True,
        **kwargs
    ) -> Dict[str, StrategyResult]:
        """
        运行多资产策略回测
        
        Args:
            strategy: 策略实例
            symbols: 股票代码列表
            parallel: 是否并行执行
            **kwargs: 其他参数
            
        Returns:
            {symbol: StrategyResult} 字典
        """
        logger.info(f"开始多资产回测: {len(symbols)} 个标的, 策略: {strategy.name}")
        
        if parallel and len(symbols) > 1:
            return self._run_parallel(strategy, symbols, **kwargs)
        else:
            return self._run_sequential(strategy, symbols, **kwargs)
    
    def _run_sequential(
        self,
        strategy: BaseStrategy,
        symbols: List[str],
        **kwargs
    ) -> Dict[str, StrategyResult]:
        """顺序执行回测"""
        results = {}
        
        for i, symbol in enumerate(symbols):
            try:
                logger.info(f"回测进度: {i+1}/{len(symbols)} - {symbol}")
                
                # 获取数据
                data = self.get_data(symbol)
                
                if data.empty:
                    logger.warning(f"股票 {symbol} 数据为空，跳过")
                    continue
                
                # 运行策略
                result = strategy.run(data, self.backtest_config)
                
                # 增强结果
                result = self._enhance_result(result, data, symbol)
                
                results[symbol] = result
                
                logger.debug(f"{symbol} 回测完成: 收益 {result.total_return:.2%}")
                
            except Exception as e:
                logger.error(f"股票 {symbol} 回测失败: {e}")
                continue
        
        # 保存结果
        self._results.update(results)
        
        logger.info(f"多资产回测完成: 成功 {len(results)}/{len(symbols)}")
        
        return results
    
    def _run_parallel(
        self,
        strategy: BaseStrategy,
        symbols: List[str],
        **kwargs
    ) -> Dict[str, StrategyResult]:
        """并行执行回测"""
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_symbol = {
                executor.submit(self._run_single_symbol, strategy, symbol): symbol
                for symbol in symbols
            }
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                completed += 1
                
                try:
                    result = future.result()
                    if result is not None:
                        results[symbol] = result
                        logger.debug(f"{symbol} 回测完成: 收益 {result.total_return:.2%}")
                    
                    if completed % 10 == 0:
                        logger.info(f"并行回测进度: {completed}/{len(symbols)}")
                        
                except Exception as e:
                    logger.error(f"股票 {symbol} 并行回测失败: {e}")
        
        # 保存结果
        self._results.update(results)
        
        logger.info(f"并行回测完成: 成功 {len(results)}/{len(symbols)}")
        
        return results
    
    def _run_single_symbol(
        self,
        strategy: BaseStrategy,
        symbol: str
    ) -> Optional[StrategyResult]:
        """运行单个标的回测（用于并行执行）"""
        try:
            # 获取数据
            data = self.get_data(symbol)
            
            if data.empty:
                logger.warning(f"股票 {symbol} 数据为空")
                return None
            
            # 运行策略
            result = strategy.run(data, self.backtest_config)
            
            # 增强结果
            result = self._enhance_result(result, data, symbol)
            
            return result
            
        except Exception as e:
            logger.error(f"股票 {symbol} 单独回测失败: {e}")
            return None
    
    def _enhance_result(
        self,
        result: StrategyResult,
        data: pd.DataFrame,
        symbol: str
    ) -> StrategyResult:
        """增强策略结果（与单资产引擎相同的逻辑）"""
        try:
            # 计算基准收益
            benchmark_returns = data['close'].pct_change().dropna()
            
            # 如果策略有持仓数据，计算策略收益
            if not result.positions.empty and 'position' in result.positions.columns:
                strategy_returns = self._calculate_strategy_returns(result.positions, data)
                
                # 重新计算绩效指标
                performance_metrics = self.calculate_performance_metrics(
                    strategy_returns, 
                    benchmark_returns
                )
                
                # 更新结果
                for key, value in performance_metrics.items():
                    if hasattr(result, key):
                        setattr(result, key, value)
            
            # 计算交易统计
            if not result.signals.empty:
                trade_stats = self.calculate_trade_statistics(result.signals)
                
                result.total_trades = trade_stats.get('total_trades', 0)
                result.winning_trades = trade_stats.get('buy_trades', 0)
                result.losing_trades = trade_stats.get('sell_trades', 0)
                
                result.metadata.update({
                    'data_points': len(data),
                    'benchmark_return': (1 + benchmark_returns).prod() - 1,
                    'trade_statistics': trade_stats
                })
            
            return result
            
        except Exception as e:
            logger.warning(f"结果增强失败: {e}")
            return result
    
    def _calculate_strategy_returns(
        self,
        positions: pd.DataFrame,
        data: pd.DataFrame
    ) -> pd.Series:
        """计算策略收益"""
        aligned_positions, aligned_prices = positions.align(data['close'], join='inner')
        
        if aligned_positions.empty or aligned_prices.empty:
            return pd.Series(dtype=float)
        
        price_returns = aligned_prices.pct_change()
        
        if 'position' in aligned_positions.columns:
            position_signals = aligned_positions['position'].shift(1).fillna(0)
        else:
            position_signals = pd.Series(0, index=aligned_positions.index)
        
        strategy_returns = position_signals * price_returns
        
        return strategy_returns.dropna()
    
    def calculate_portfolio_metrics(
        self,
        results: Dict[str, StrategyResult],
        weights: Optional[Dict[str, float]] = None
    ) -> Dict[str, float]:
        """
        计算投资组合级别的绩效指标
        
        Args:
            results: 各标的回测结果
            weights: 权重分配，默认等权重
            
        Returns:
            组合绩效指标
        """
        if not results:
            return {}
        
        # 默认等权重
        if weights is None:
            n_assets = len(results)
            weights = {symbol: 1.0 / n_assets for symbol in results.keys()}
        
        # 收集各标的收益
        returns_data = {}
        for symbol, result in results.items():
            if not result.positions.empty and 'position' in result.positions.columns:
                # 这里需要重新计算每日收益，简化处理
                returns_data[symbol] = result.total_return
        
        if not returns_data:
            return {}
        
        # 计算加权组合收益
        portfolio_return = sum(
            weights.get(symbol, 0) * ret 
            for symbol, ret in returns_data.items()
        )
        
        # 计算其他组合指标
        individual_sharpe = [result.sharpe_ratio for result in results.values()]
        individual_drawdown = [result.max_drawdown for result in results.values()]
        
        portfolio_metrics = {
            'portfolio_return': portfolio_return,
            'avg_sharpe_ratio': np.mean(individual_sharpe),
            'avg_max_drawdown': np.mean(individual_drawdown),
            'n_assets': len(results),
            'success_rate': len([r for r in results.values() if r.total_return > 0]) / len(results)
        }
        
        return portfolio_metrics
    
    def generate_portfolio_report(
        self,
        results: Dict[str, StrategyResult],
        top_n: int = 10
    ) -> Dict[str, Any]:
        """
        生成投资组合报告
        
        Args:
            results: 回测结果
            top_n: 显示前N个最佳表现
            
        Returns:
            组合报告
        """
        if not results:
            return {}
        
        # 按收益率排序
        sorted_results = sorted(
            results.items(),
            key=lambda x: x[1].total_return,
            reverse=True
        )
        
        # 统计信息
        returns = [result.total_return for result in results.values()]
        sharpe_ratios = [result.sharpe_ratio for result in results.values()]
        max_drawdowns = [result.max_drawdown for result in results.values()]
        
        # 生成报告
        report = {
            'summary': {
                'total_assets': len(results),
                'profitable_assets': len([r for r in returns if r > 0]),
                'avg_return': np.mean(returns),
                'median_return': np.median(returns),
                'std_return': np.std(returns),
                'avg_sharpe': np.mean(sharpe_ratios),
                'avg_max_drawdown': np.mean(max_drawdowns),
                'best_return': max(returns),
                'worst_return': min(returns)
            },
            'top_performers': [
                {
                    'symbol': symbol,
                    'total_return': result.total_return,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'total_trades': result.total_trades
                }
                for symbol, result in sorted_results[:top_n]
            ],
            'worst_performers': [
                {
                    'symbol': symbol,
                    'total_return': result.total_return,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'total_trades': result.total_trades
                }
                for symbol, result in sorted_results[-top_n:]
            ]
        }
        
        return report
    
    def run_sector_analysis(
        self,
        results: Dict[str, StrategyResult],
        sector_mapping: Dict[str, str]
    ) -> Dict[str, Dict[str, float]]:
        """
        运行行业分析
        
        Args:
            results: 回测结果
            sector_mapping: {symbol: sector} 映射
            
        Returns:
            行业分析结果
        """
        sector_results = {}
        
        # 按行业分组
        for symbol, result in results.items():
            sector = sector_mapping.get(symbol, 'Unknown')
            
            if sector not in sector_results:
                sector_results[sector] = []
            
            sector_results[sector].append(result)
        
        # 计算行业统计
        sector_stats = {}
        for sector, sector_result_list in sector_results.items():
            returns = [r.total_return for r in sector_result_list]
            sharpe_ratios = [r.sharpe_ratio for r in sector_result_list]
            
            sector_stats[sector] = {
                'count': len(sector_result_list),
                'avg_return': np.mean(returns),
                'median_return': np.median(returns),
                'std_return': np.std(returns),
                'avg_sharpe': np.mean(sharpe_ratios),
                'success_rate': len([r for r in returns if r > 0]) / len(returns),
                'best_return': max(returns),
                'worst_return': min(returns)
            }
        
        return sector_stats


__all__ = [
    "MultiAssetEngine"
]
