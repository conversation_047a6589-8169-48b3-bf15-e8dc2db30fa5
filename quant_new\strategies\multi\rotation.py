"""
轮动策略

基于动量或其他因子进行标的轮动的策略。
"""

from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import numpy as np
from pydantic import Field, validator

from ..base import BaseStrategy, StrategyConfig, StrategyResult
from ...utils.logger import get_logger

logger = get_logger(__name__)


class RotationConfig(StrategyConfig):
    """轮动策略配置"""
    
    strategy_name: str = Field(default="轮动策略", description="策略名称")
    
    # 标的池参数
    symbols: List[str] = Field(default_factory=list, description="股票代码列表")
    top_n: int = Field(default=3, description="选择前N个标的")
    
    # 排序参数
    ranking_method: str = Field(default="momentum", description="排序方法")
    ranking_period: int = Field(default=20, description="排序周期")
    
    # 轮动参数
    rotation_frequency: str = Field(default="monthly", description="轮动频率")
    min_holding_period: int = Field(default=5, description="最小持有期")
    
    # 动量参数
    momentum_lookback: int = Field(default=20, description="动量回看期")
    momentum_skip: int = Field(default=1, description="动量跳过期")
    
    # 波动率参数
    volatility_period: int = Field(default=20, description="波动率计算周期")
    volatility_weight: float = Field(default=0.3, description="波动率权重")
    
    # 风控参数
    max_volatility: float = Field(default=0.5, description="最大波动率限制")
    min_volume: float = Field(default=1000000, description="最小成交量要求")
    
    @validator('top_n')
    def validate_top_n(cls, v, values):
        """验证选择数量"""
        symbols = values.get('symbols', [])
        if symbols and v > len(symbols):
            raise ValueError("选择数量不能超过标的池大小")
        return v


class RotationStrategy(BaseStrategy):
    """轮动策略实现"""
    
    def __init__(self, config: RotationConfig):
        """
        初始化轮动策略
        
        Args:
            config: 轮动策略配置
        """
        super().__init__(config)
        self.config: RotationConfig = config
        
        # 内部状态
        self._price_data = {}
        self._returns_data = {}
        self._volume_data = {}
        self._current_holdings = set()
        self._holding_start_dates = {}
    
    def set_data(self, data_dict: Dict[str, pd.DataFrame]):
        """
        设置多标的数据
        
        Args:
            data_dict: {symbol: DataFrame} 数据字典
        """
        self._price_data = data_dict
        
        # 提取收益率和成交量数据
        self._returns_data = {}
        self._volume_data = {}
        
        for symbol, data in data_dict.items():
            if not data.empty:
                if 'close' in data.columns:
                    self._returns_data[symbol] = data['close'].pct_change().dropna()
                if 'volume' in data.columns:
                    self._volume_data[symbol] = data['volume']
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成轮动信号
        
        Args:
            data: 主要标的数据（用于时间索引）
            
        Returns:
            轮动信号DataFrame
        """
        if not self._price_data:
            raise ValueError("请先使用set_data()设置多标的数据")
        
        # 创建信号DataFrame
        signals = pd.DataFrame(index=data.index)
        
        # 初始化持仓列
        for symbol in self.config.symbols:
            signals[f'{symbol}_position'] = 0.0
        
        signals['rotation'] = False
        signals['selected_symbols'] = ''
        
        # 获取轮动日期
        rotation_dates = self._get_rotation_dates(data.index)
        
        current_holdings = set()
        holding_start_dates = {}
        
        for date in data.index:
            # 检查是否需要轮动
            if date in rotation_dates:
                # 计算排序得分
                scores = self._calculate_ranking_scores(date)
                
                # 选择前N个标的
                selected_symbols = self._select_top_symbols(scores, current_holdings, holding_start_dates, date)
                
                if selected_symbols != current_holdings:
                    signals.loc[date, 'rotation'] = True
                    signals.loc[date, 'selected_symbols'] = ','.join(selected_symbols)
                    
                    # 更新持仓
                    current_holdings = selected_symbols
                    
                    # 更新持有开始日期
                    for symbol in selected_symbols:
                        if symbol not in holding_start_dates:
                            holding_start_dates[symbol] = date
                    
                    # 清理不再持有的标的
                    symbols_to_remove = []
                    for symbol in list(holding_start_dates.keys()):
                        if symbol not in current_holdings:
                            symbols_to_remove.append(symbol)
                    
                    for symbol in symbols_to_remove:
                        del holding_start_dates[symbol]
            
            # 设置当前持仓
            position_weight = 1.0 / len(current_holdings) if current_holdings else 0.0
            
            for symbol in self.config.symbols:
                if symbol in current_holdings:
                    signals.loc[date, f'{symbol}_position'] = position_weight
                else:
                    signals.loc[date, f'{symbol}_position'] = 0.0
        
        logger.debug(f"生成轮动信号: {len(rotation_dates)} 个轮动点")
        
        return signals
    
    def _calculate_ranking_scores(self, date) -> Dict[str, float]:
        """计算排序得分"""
        scores = {}
        
        for symbol in self.config.symbols:
            try:
                score = 0.0
                
                if self.config.ranking_method == "momentum":
                    score = self._calculate_momentum_score(symbol, date)
                elif self.config.ranking_method == "volatility":
                    score = self._calculate_volatility_score(symbol, date)
                elif self.config.ranking_method == "combined":
                    momentum_score = self._calculate_momentum_score(symbol, date)
                    volatility_score = self._calculate_volatility_score(symbol, date)
                    score = momentum_score * (1 - self.config.volatility_weight) + volatility_score * self.config.volatility_weight
                else:
                    score = self._calculate_momentum_score(symbol, date)
                
                # 应用过滤条件
                if self._apply_filters(symbol, date):
                    scores[symbol] = score
                else:
                    scores[symbol] = float('-inf')  # 不符合条件的标的得分设为负无穷
                    
            except Exception as e:
                logger.warning(f"计算 {symbol} 得分失败: {e}")
                scores[symbol] = float('-inf')
        
        return scores
    
    def _calculate_momentum_score(self, symbol: str, date) -> float:
        """计算动量得分"""
        if symbol not in self._returns_data:
            return 0.0
        
        returns = self._returns_data[symbol]
        returns_to_date = returns[returns.index <= date]
        
        if len(returns_to_date) < self.config.momentum_lookback + self.config.momentum_skip:
            return 0.0
        
        # 计算动量（跳过最近几天以避免反转效应）
        momentum_returns = returns_to_date.iloc[-(self.config.momentum_lookback + self.config.momentum_skip):-self.config.momentum_skip]
        
        if len(momentum_returns) == 0:
            return 0.0
        
        # 累积收益作为动量得分
        momentum_score = (1 + momentum_returns).prod() - 1
        
        return momentum_score
    
    def _calculate_volatility_score(self, symbol: str, date) -> float:
        """计算波动率得分（波动率越低得分越高）"""
        if symbol not in self._returns_data:
            return 0.0
        
        returns = self._returns_data[symbol]
        returns_to_date = returns[returns.index <= date]
        
        if len(returns_to_date) < self.config.volatility_period:
            return 0.0
        
        # 计算波动率
        volatility = returns_to_date.tail(self.config.volatility_period).std()
        
        # 波动率得分（波动率越低得分越高）
        volatility_score = -volatility if volatility > 0 else 0.0
        
        return volatility_score
    
    def _apply_filters(self, symbol: str, date) -> bool:
        """应用过滤条件"""
        try:
            # 波动率过滤
            if symbol in self._returns_data:
                returns = self._returns_data[symbol]
                returns_to_date = returns[returns.index <= date]
                
                if len(returns_to_date) >= self.config.volatility_period:
                    volatility = returns_to_date.tail(self.config.volatility_period).std() * np.sqrt(252)
                    if volatility > self.config.max_volatility:
                        return False
            
            # 成交量过滤
            if symbol in self._volume_data:
                volume_data = self._volume_data[symbol]
                volume_to_date = volume_data[volume_data.index <= date]
                
                if len(volume_to_date) >= 5:
                    avg_volume = volume_to_date.tail(5).mean()
                    if avg_volume < self.config.min_volume:
                        return False
            
            return True
            
        except Exception as e:
            logger.warning(f"过滤条件检查失败 {symbol}: {e}")
            return False
    
    def _select_top_symbols(
        self, 
        scores: Dict[str, float], 
        current_holdings: set, 
        holding_start_dates: Dict[str, pd.Timestamp], 
        date: pd.Timestamp
    ) -> set:
        """选择前N个标的"""
        # 按得分排序
        sorted_symbols = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 应用最小持有期限制
        protected_symbols = set()
        for symbol in current_holdings:
            if symbol in holding_start_dates:
                holding_days = (date - holding_start_dates[symbol]).days
                if holding_days < self.config.min_holding_period:
                    protected_symbols.add(symbol)
        
        # 选择标的
        selected_symbols = set()
        
        # 首先保留受保护的标的
        for symbol in protected_symbols:
            if len(selected_symbols) < self.config.top_n:
                selected_symbols.add(symbol)
        
        # 然后按得分选择剩余标的
        for symbol, score in sorted_symbols:
            if len(selected_symbols) >= self.config.top_n:
                break
            
            if symbol not in selected_symbols and score > float('-inf'):
                selected_symbols.add(symbol)
        
        return selected_symbols
    
    def _get_rotation_dates(self, date_index: pd.DatetimeIndex) -> List:
        """获取轮动日期"""
        if self.config.rotation_frequency == "daily":
            return date_index.tolist()
        elif self.config.rotation_frequency == "weekly":
            return date_index[date_index.weekday == 0].tolist()  # 每周一
        elif self.config.rotation_frequency == "monthly":
            return date_index[date_index.is_month_end].tolist()  # 月末
        elif self.config.rotation_frequency == "quarterly":
            return date_index[date_index.is_quarter_end].tolist()  # 季末
        else:
            # 默认月度轮动
            return date_index[date_index.is_month_end].tolist()
    
    def calculate_rotation_returns(self, signals: pd.DataFrame) -> pd.Series:
        """
        计算轮动策略收益
        
        Args:
            signals: 信号DataFrame
            
        Returns:
            轮动策略收益序列
        """
        rotation_returns = pd.Series(0.0, index=signals.index)
        
        for date in signals.index:
            daily_return = 0.0
            
            for symbol in self.config.symbols:
                position = signals.loc[date, f'{symbol}_position']
                
                if position > 0 and symbol in self._returns_data and date in self._returns_data[symbol].index:
                    asset_return = self._returns_data[symbol].loc[date]
                    daily_return += position * asset_return
            
            rotation_returns.loc[date] = daily_return
        
        return rotation_returns
    
    def get_rotation_metrics(self, signals: pd.DataFrame) -> Dict[str, Any]:
        """
        获取轮动策略指标
        
        Args:
            signals: 信号DataFrame
            
        Returns:
            轮动策略指标字典
        """
        rotation_returns = self.calculate_rotation_returns(signals)
        
        # 基础指标
        total_return = (1 + rotation_returns).prod() - 1
        annual_return = (1 + total_return) ** (252 / len(rotation_returns)) - 1
        volatility = rotation_returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # 最大回撤
        cumulative = (1 + rotation_returns).cumprod()
        peak = cumulative.expanding().max()
        drawdown = (cumulative - peak) / peak
        max_drawdown = drawdown.min()
        
        # 轮动统计
        rotation_count = signals['rotation'].sum()
        
        # 持仓统计
        position_columns = [col for col in signals.columns if col.endswith('_position')]
        avg_positions = signals[position_columns].mean()
        
        # 选择频率统计
        symbol_selection_count = {}
        for symbol in self.config.symbols:
            symbol_selection_count[symbol] = (signals[f'{symbol}_position'] > 0).sum()
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'rotation_count': rotation_count,
            'avg_positions': avg_positions.to_dict(),
            'symbol_selection_count': symbol_selection_count,
            'rotation_returns': rotation_returns
        }
    
    @classmethod
    def get_param_description(cls) -> Dict[str, str]:
        """获取参数描述"""
        base_desc = super().get_param_description()
        rotation_desc = {
            'symbols': '股票代码列表',
            'top_n': '选择前N个标的',
            'ranking_method': '排序方法',
            'ranking_period': '排序周期',
            'rotation_frequency': '轮动频率',
            'min_holding_period': '最小持有期',
            'momentum_lookback': '动量回看期',
            'momentum_skip': '动量跳过期',
            'volatility_period': '波动率计算周期',
            'volatility_weight': '波动率权重',
            'max_volatility': '最大波动率限制',
            'min_volume': '最小成交量要求'
        }
        return {**base_desc, **rotation_desc}


__all__ = [
    "RotationStrategy",
    "RotationConfig"
]
