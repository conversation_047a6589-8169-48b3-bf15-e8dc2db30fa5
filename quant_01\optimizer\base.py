"""
优化器基类

定义所有优化器的统一接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
import pandas as pd
import numpy as np
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..core.data_structures.trading import StrategyResult

logger = get_logger(__name__)


@dataclass
class OptimizationResult:
    """优化结果数据类"""
    best_params: Dict[str, Any]
    best_score: float
    objective: str
    direction: str
    n_trials: int
    optimization_time: float
    all_results: List[Dict[str, Any]]
    validation_score: Optional[float] = None
    overfitting_score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'objective': self.objective,
            'direction': self.direction,
            'n_trials': self.n_trials,
            'optimization_time': self.optimization_time,
            'validation_score': self.validation_score,
            'overfitting_score': self.overfitting_score,
            'total_combinations': len(self.all_results)
        }


class BaseOptimizer(ABC):
    """优化器基类"""
    
    def __init__(
        self,
        strategy_class: type,
        param_space: Dict[str, List[Any]],
        objective: str = "sharpe_ratio",
        direction: str = "maximize",
        n_jobs: int = 1,
        random_state: Optional[int] = None
    ):
        """
        初始化优化器
        
        Args:
            strategy_class: 策略类
            param_space: 参数空间
            objective: 优化目标
            direction: 优化方向 ('maximize' 或 'minimize')
            n_jobs: 并行任务数
            random_state: 随机种子
        """
        self.strategy_class = strategy_class
        self.param_space = param_space
        self.objective = objective
        self.direction = direction
        self.n_jobs = n_jobs
        self.random_state = random_state
        
        # 验证参数
        self._validate_params()
        
        # 设置随机种子
        if random_state is not None:
            np.random.seed(random_state)
        
        logger.info(f"{self.__class__.__name__} 初始化完成")
    
    def _validate_params(self):
        """验证参数"""
        if not self.param_space:
            raise ValueError("参数空间不能为空")
        
        if self.direction not in ['maximize', 'minimize']:
            raise ValueError("direction 必须是 'maximize' 或 'minimize'")
        
        if self.n_jobs < 1:
            raise ValueError("n_jobs 必须大于等于1")
        
        # 验证参数空间格式
        for param, values in self.param_space.items():
            if not isinstance(values, list) or len(values) == 0:
                raise ValueError(f"参数 {param} 的值必须是非空列表")
    
    @abstractmethod
    def optimize(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame] = None,
        **kwargs
    ) -> OptimizationResult:
        """
        执行优化
        
        Args:
            data: 训练数据
            validation_data: 验证数据
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        pass
    
    def _evaluate_strategy(
        self,
        params: Dict[str, Any],
        data: pd.DataFrame
    ) -> float:
        """
        评估单个参数组合
        
        Args:
            params: 参数组合
            data: 数据
            
        Returns:
            目标函数值
        """
        try:
            # 创建策略配置
            config_class = getattr(self.strategy_class, '__init__').__annotations__.get('config', None)
            
            if config_class:
                config = config_class(**params)
                strategy = self.strategy_class(config)
            else:
                # 如果没有配置类，直接传递参数
                strategy = self.strategy_class(params)
            
            # 运行策略
            result = strategy.run(data)
            
            # 获取目标值
            score = getattr(result, self.objective, 0)
            
            return float(score)
            
        except Exception as e:
            logger.warning(f"参数组合 {params} 评估失败: {e}")
            return float('-inf') if self.direction == 'maximize' else float('inf')
    
    def _is_better_score(self, new_score: float, best_score: float) -> bool:
        """判断新分数是否更好"""
        if self.direction == 'maximize':
            return new_score > best_score
        else:
            return new_score < best_score
    
    def _get_initial_best_score(self) -> float:
        """获取初始最佳分数"""
        return float('-inf') if self.direction == 'maximize' else float('inf')
    
    def _calculate_overfitting_score(
        self,
        train_score: float,
        val_score: float
    ) -> float:
        """计算过拟合分数"""
        if val_score == 0:
            return float('inf')
        
        if self.direction == 'maximize':
            return (train_score - val_score) / abs(val_score)
        else:
            return (val_score - train_score) / abs(val_score)
    
    def get_param_importance(self, results: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        计算参数重要性
        
        Args:
            results: 优化结果列表
            
        Returns:
            参数重要性字典
        """
        if not results:
            return {}
        
        importance = {}
        
        for param in self.param_space.keys():
            # 计算每个参数值对应的平均分数
            param_scores = {}
            for result in results:
                param_value = result['params'][param]
                score = result['score']
                
                if param_value not in param_scores:
                    param_scores[param_value] = []
                param_scores[param_value].append(score)
            
            # 计算方差作为重要性指标
            avg_scores = [np.mean(scores) for scores in param_scores.values()]
            importance[param] = np.var(avg_scores) if len(avg_scores) > 1 else 0
        
        # 归一化
        total_importance = sum(importance.values())
        if total_importance > 0:
            importance = {k: v / total_importance for k, v in importance.items()}
        
        return importance
