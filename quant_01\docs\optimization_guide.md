# 策略参数优化指南

## 概述

Quant_01 量化回测引擎提供了完整的策略参数优化功能，支持多种优化算法，帮助用户找到最佳的策略参数组合。

## 优化器类型

### 1. 网格搜索 (Grid Search)
- **适用场景**: 小参数空间，需要全面搜索
- **优点**: 保证找到全局最优解
- **缺点**: 计算量大，不适合大参数空间

```python
from quant_01 import optimization_manager, MACDStrategy

# 定义参数空间
param_space = {
    'fast_period': [8, 12, 16],
    'slow_period': [21, 26, 35],
    'signal_period': [5, 9, 12]
}

# 执行网格搜索
result = optimization_manager.optimize(
    strategy_class=MACDStrategy,
    param_space=param_space,
    data=data,
    method='grid',
    objective='sharpe_ratio',
    direction='maximize'
)
```

### 2. 随机搜索 (Random Search)
- **适用场景**: 大参数空间，快速探索
- **优点**: 计算效率高，适合连续参数
- **缺点**: 可能错过最优解

```python
# 支持连续分布的参数空间
param_space = {
    'fast_period': {
        'type': 'uniform',
        'low': 5,
        'high': 20,
        'dtype': 'int'
    },
    'slow_period': {
        'type': 'uniform',
        'low': 20,
        'high': 50,
        'dtype': 'int'
    }
}

result = optimization_manager.optimize(
    strategy_class=MACDStrategy,
    param_space=param_space,
    data=data,
    method='random',
    n_trials=100
)
```

### 3. 贝叶斯优化 (Bayesian Optimization)
- **适用场景**: 昂贵的目标函数，智能搜索
- **优点**: 高效利用历史信息，收敛快
- **缺点**: 需要额外依赖库

```python
result = optimization_manager.optimize(
    strategy_class=MACDStrategy,
    param_space=param_space,
    data=data,
    method='bayesian',
    n_trials=50,
    acquisition_function='EI'
)
```

### 4. 遗传算法 (Genetic Algorithm)
- **适用场景**: 复杂非线性问题，全局优化
- **优点**: 全局搜索能力强，适合复杂问题
- **缺点**: 收敛速度较慢

```python
result = optimization_manager.optimize(
    strategy_class=MACDStrategy,
    param_space=param_space,
    data=data,
    method='genetic',
    n_generations=30,
    population_size=50,
    mutation_rate=0.1
)
```

### 5. 多目标优化 (Multi-Objective)
- **适用场景**: 需要同时优化多个指标
- **优点**: 提供帕累托前沿解集
- **缺点**: 结果解释复杂

```python
result = optimization_manager.optimize(
    strategy_class=MACDStrategy,
    param_space=param_space,
    data=data,
    method='multi_objective',
    objectives=['sharpe_ratio', 'total_return', 'max_drawdown'],
    directions={
        'sharpe_ratio': 'maximize',
        'total_return': 'maximize',
        'max_drawdown': 'minimize'
    }
)
```

## 参数空间定义

### 离散参数
```python
param_space = {
    'fast_period': [8, 12, 16, 20],  # 离散值列表
    'use_filter': [True, False]      # 布尔值
}
```

### 连续参数
```python
param_space = {
    'fast_period': {
        'type': 'uniform',    # 均匀分布
        'low': 5,
        'high': 20,
        'dtype': 'int'        # 整数类型
    },
    'threshold': {
        'type': 'normal',     # 正态分布
        'mean': 0.5,
        'std': 0.1
    }
}
```

## 优化目标

支持的优化目标包括：
- `sharpe_ratio`: 夏普比率
- `total_return`: 总收益率
- `annual_return`: 年化收益率
- `max_drawdown`: 最大回撤
- `volatility`: 波动率
- `win_rate`: 胜率

## 验证和过拟合检测

```python
result = optimization_manager.optimize(
    strategy_class=MACDStrategy,
    param_space=param_space,
    data=data,
    method='grid',
    validation_split=0.2,  # 20%数据用于验证
    objective='sharpe_ratio'
)

# 检查过拟合
if result.overfitting_score > 0.5:
    print("警告: 可能存在过拟合")
```

## 方法比较

```python
# 比较不同优化方法
results = optimization_manager.compare_methods(
    strategy_class=MACDStrategy,
    param_space=param_space,
    data=data,
    methods=['grid', 'random', 'bayesian'],
    n_trials=50
)

# 查看比较结果
for method, result in results.items():
    print(f"{method}: {result.best_score:.4f}")
```

## 最佳实践

### 1. 参数空间设计
- 根据策略特性合理设置参数范围
- 避免过大的参数空间导致计算量爆炸
- 考虑参数之间的约束关系

### 2. 优化方法选择
- 小参数空间(<1000组合): 网格搜索
- 大参数空间: 随机搜索或贝叶斯优化
- 复杂非线性问题: 遗传算法
- 多目标问题: 多目标优化

### 3. 过拟合防范
- 使用验证集评估泛化能力
- 监控训练集和验证集性能差异
- 考虑使用交叉验证

### 4. 结果解释
- 分析参数重要性
- 检查最优参数的合理性
- 进行稳健性测试

## 示例：完整优化流程

```python
from quant_01 import QuickStart

# 创建快速开始实例
qs = QuickStart(data_source="akshare")

# 执行参数优化
result = qs.optimize_strategy(
    method="grid",
    param_space={
        'fast_period': [8, 12, 16],
        'slow_period': [21, 26, 35],
        'signal_period': [5, 9, 12]
    },
    symbol="000001",
    validation_split=0.2,
    objective="sharpe_ratio"
)

print(f"最佳参数: {result['best_params']}")
print(f"最佳分数: {result['best_score']}")
```

## 性能监控

```python
# 查看优化历史
history = optimization_manager.get_optimization_history()
print(f"总共执行了 {len(history)} 次优化")

# 获取方法信息
info = optimization_manager.get_method_info('bayesian')
print(f"方法描述: {info['description']}")
```

## 注意事项

1. **计算资源**: 大参数空间的网格搜索可能需要大量计算时间
2. **数据质量**: 确保数据质量，避免垃圾进垃圾出
3. **参数约束**: 注意参数之间的逻辑约束关系
4. **结果验证**: 在不同市场环境下验证优化结果的稳定性
5. **定期重优化**: 市场环境变化时需要重新优化参数
