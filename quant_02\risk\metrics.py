"""
风险指标计算模块

提供各种风险指标的计算功能。
"""

from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.structures import Portfolio
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class RiskMetricsResult:
    """风险指标计算结果"""
    
    # 基本指标
    total_return: float = 0.0
    annual_return: float = 0.0
    volatility: float = 0.0
    
    # 风险调整收益指标
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # 回撤指标
    max_drawdown: float = 0.0
    current_drawdown: float = 0.0
    drawdown_duration: int = 0
    recovery_time: int = 0
    
    # VaR指标
    var_95: float = 0.0
    var_99: float = 0.0
    cvar_95: float = 0.0
    cvar_99: float = 0.0
    
    # 其他风险指标
    beta: float = 0.0
    alpha: float = 0.0
    information_ratio: float = 0.0
    tracking_error: float = 0.0
    
    # 尾部风险
    skewness: float = 0.0
    kurtosis: float = 0.0
    tail_ratio: float = 0.0
    
    # 计算时间
    calculation_time: datetime = None


class RiskMetricsCalculator:
    """风险指标计算器
    
    提供全面的风险指标计算功能。
    """
    
    def __init__(self, risk_free_rate: float = 0.03):
        """
        初始化风险指标计算器
        
        Args:
            risk_free_rate: 无风险利率
        """
        self.risk_free_rate = risk_free_rate
        
        logger.info("风险指标计算器初始化完成")
    
    def calculate_portfolio_metrics(
        self,
        portfolio: Portfolio,
        benchmark_returns: Optional[pd.Series] = None,
        period_days: int = 252
    ) -> RiskMetricsResult:
        """
        计算投资组合风险指标
        
        Args:
            portfolio: 投资组合
            benchmark_returns: 基准收益率序列
            period_days: 计算周期天数
            
        Returns:
            风险指标结果
        """
        try:
            result = RiskMetricsResult()
            result.calculation_time = datetime.now()
            
            if len(portfolio.value_history) < 2:
                logger.warning("投资组合历史数据不足，无法计算风险指标")
                return result
            
            # 计算收益率序列
            returns = portfolio.value_history.pct_change().dropna()
            
            if len(returns) == 0:
                logger.warning("收益率序列为空")
                return result
            
            # 基本收益指标
            self._calculate_return_metrics(result, returns, portfolio, period_days)
            
            # 风险调整收益指标
            self._calculate_risk_adjusted_metrics(result, returns, period_days)
            
            # 回撤指标
            self._calculate_drawdown_metrics(result, portfolio.value_history)
            
            # VaR指标
            self._calculate_var_metrics(result, returns)
            
            # 基准相关指标
            if benchmark_returns is not None:
                self._calculate_benchmark_metrics(result, returns, benchmark_returns, period_days)
            
            # 尾部风险指标
            self._calculate_tail_risk_metrics(result, returns)
            
            logger.debug("风险指标计算完成")
            return result
            
        except Exception as e:
            logger.error(f"风险指标计算失败: {e}")
            return RiskMetricsResult(calculation_time=datetime.now())
    
    def _calculate_return_metrics(
        self,
        result: RiskMetricsResult,
        returns: pd.Series,
        portfolio: Portfolio,
        period_days: int
    ):
        """计算收益指标"""
        # 总收益率
        result.total_return = (portfolio.value_history.iloc[-1] / portfolio.value_history.iloc[0]) - 1
        
        # 年化收益率
        days = len(returns)
        if days > 0:
            result.annual_return = (1 + result.total_return) ** (period_days / days) - 1
        
        # 波动率
        result.volatility = returns.std() * np.sqrt(period_days)
    
    def _calculate_risk_adjusted_metrics(
        self,
        result: RiskMetricsResult,
        returns: pd.Series,
        period_days: int
    ):
        """计算风险调整收益指标"""
        if result.volatility == 0:
            return
        
        # 夏普比率
        excess_return = result.annual_return - self.risk_free_rate
        result.sharpe_ratio = excess_return / result.volatility
        
        # 索提诺比率（只考虑下行波动）
        downside_returns = returns[returns < 0]
        if len(downside_returns) > 0:
            downside_volatility = downside_returns.std() * np.sqrt(period_days)
            if downside_volatility > 0:
                result.sortino_ratio = excess_return / downside_volatility
        
        # 卡玛比率
        if result.max_drawdown > 0:
            result.calmar_ratio = result.annual_return / result.max_drawdown
    
    def _calculate_drawdown_metrics(self, result: RiskMetricsResult, value_series: pd.Series):
        """计算回撤指标"""
        if len(value_series) < 2:
            return
        
        # 计算回撤序列
        peak = value_series.cummax()
        drawdown = (value_series - peak) / peak
        
        # 最大回撤
        result.max_drawdown = abs(drawdown.min())
        
        # 当前回撤
        result.current_drawdown = abs(drawdown.iloc[-1])
        
        # 回撤持续时间
        if result.current_drawdown > 0:
            # 找到当前回撤开始的位置
            current_peak_idx = peak.iloc[:-1].idxmax()
            current_idx = value_series.index[-1]
            
            if current_peak_idx in value_series.index:
                peak_pos = value_series.index.get_loc(current_peak_idx)
                current_pos = value_series.index.get_loc(current_idx)
                result.drawdown_duration = current_pos - peak_pos
        
        # 恢复时间（简化计算）
        max_dd_idx = drawdown.idxmin()
        if max_dd_idx in value_series.index:
            max_dd_pos = value_series.index.get_loc(max_dd_idx)
            
            # 寻找恢复点
            recovery_series = value_series.iloc[max_dd_pos:]
            peak_value = peak.loc[max_dd_idx]
            
            recovery_points = recovery_series[recovery_series >= peak_value]
            if len(recovery_points) > 0:
                recovery_idx = recovery_points.index[0]
                recovery_pos = value_series.index.get_loc(recovery_idx)
                result.recovery_time = recovery_pos - max_dd_pos
    
    def _calculate_var_metrics(self, result: RiskMetricsResult, returns: pd.Series):
        """计算VaR指标"""
        if len(returns) < 20:
            return
        
        # 历史法VaR
        result.var_95 = abs(returns.quantile(0.05))
        result.var_99 = abs(returns.quantile(0.01))
        
        # 条件VaR (CVaR)
        var_95_threshold = returns.quantile(0.05)
        var_99_threshold = returns.quantile(0.01)
        
        tail_95 = returns[returns <= var_95_threshold]
        tail_99 = returns[returns <= var_99_threshold]
        
        if len(tail_95) > 0:
            result.cvar_95 = abs(tail_95.mean())
        
        if len(tail_99) > 0:
            result.cvar_99 = abs(tail_99.mean())
    
    def _calculate_benchmark_metrics(
        self,
        result: RiskMetricsResult,
        returns: pd.Series,
        benchmark_returns: pd.Series,
        period_days: int
    ):
        """计算基准相关指标"""
        # 对齐时间序列
        aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
        
        if len(aligned_returns) < 10:
            return
        
        # Beta
        covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
        benchmark_variance = np.var(aligned_benchmark)
        
        if benchmark_variance > 0:
            result.beta = covariance / benchmark_variance
            
            # Alpha
            benchmark_annual_return = (1 + aligned_benchmark.mean()) ** period_days - 1
            result.alpha = result.annual_return - (self.risk_free_rate + result.beta * (benchmark_annual_return - self.risk_free_rate))
        
        # 跟踪误差
        tracking_diff = aligned_returns - aligned_benchmark
        result.tracking_error = tracking_diff.std() * np.sqrt(period_days)
        
        # 信息比率
        if result.tracking_error > 0:
            result.information_ratio = tracking_diff.mean() * period_days / result.tracking_error
    
    def _calculate_tail_risk_metrics(self, result: RiskMetricsResult, returns: pd.Series):
        """计算尾部风险指标"""
        if len(returns) < 20:
            return
        
        # 偏度
        result.skewness = returns.skew()
        
        # 峰度
        result.kurtosis = returns.kurtosis()
        
        # 尾部比率（95%分位数与5%分位数的比率）
        q95 = returns.quantile(0.95)
        q05 = returns.quantile(0.05)
        
        if q05 != 0:
            result.tail_ratio = abs(q95 / q05)
    
    def calculate_rolling_metrics(
        self,
        value_series: pd.Series,
        window: int = 30,
        metrics: List[str] = None
    ) -> pd.DataFrame:
        """
        计算滚动风险指标
        
        Args:
            value_series: 价值序列
            window: 滚动窗口
            metrics: 要计算的指标列表
            
        Returns:
            滚动指标DataFrame
        """
        if metrics is None:
            metrics = ['volatility', 'sharpe_ratio', 'max_drawdown']
        
        returns = value_series.pct_change().dropna()
        rolling_metrics = pd.DataFrame(index=returns.index)
        
        if 'volatility' in metrics:
            rolling_metrics['volatility'] = returns.rolling(window).std() * np.sqrt(252)
        
        if 'sharpe_ratio' in metrics:
            rolling_returns = returns.rolling(window).mean() * 252
            rolling_vol = returns.rolling(window).std() * np.sqrt(252)
            rolling_metrics['sharpe_ratio'] = (rolling_returns - self.risk_free_rate) / rolling_vol
        
        if 'max_drawdown' in metrics:
            rolling_metrics['max_drawdown'] = self._calculate_rolling_max_drawdown(value_series, window)
        
        return rolling_metrics.dropna()
    
    def _calculate_rolling_max_drawdown(self, value_series: pd.Series, window: int) -> pd.Series:
        """计算滚动最大回撤"""
        rolling_max_dd = pd.Series(index=value_series.index, dtype=float)
        
        for i in range(window, len(value_series)):
            window_data = value_series.iloc[i-window:i+1]
            peak = window_data.cummax()
            drawdown = (window_data - peak) / peak
            rolling_max_dd.iloc[i] = abs(drawdown.min())
        
        return rolling_max_dd
    
    def calculate_correlation_matrix(self, returns_dict: Dict[str, pd.Series]) -> pd.DataFrame:
        """
        计算收益率相关性矩阵
        
        Args:
            returns_dict: 收益率字典
            
        Returns:
            相关性矩阵
        """
        returns_df = pd.DataFrame(returns_dict)
        return returns_df.corr()
    
    def calculate_portfolio_var(
        self,
        weights: np.ndarray,
        returns_matrix: np.ndarray,
        confidence_level: float = 0.05,
        method: str = 'historical'
    ) -> float:
        """
        计算投资组合VaR
        
        Args:
            weights: 权重向量
            returns_matrix: 收益率矩阵
            confidence_level: 置信水平
            method: 计算方法
            
        Returns:
            VaR值
        """
        if method == 'historical':
            portfolio_returns = np.dot(returns_matrix, weights)
            return abs(np.percentile(portfolio_returns, confidence_level * 100))
        
        elif method == 'parametric':
            portfolio_mean = np.dot(weights, np.mean(returns_matrix, axis=0))
            portfolio_var = np.dot(weights, np.dot(np.cov(returns_matrix.T), weights))
            portfolio_std = np.sqrt(portfolio_var)
            
            from scipy.stats import norm
            z_score = norm.ppf(confidence_level)
            return abs(portfolio_mean + z_score * portfolio_std)
        
        else:
            raise ValueError(f"不支持的VaR计算方法: {method}")
    
    def stress_test(
        self,
        portfolio_value: float,
        stress_scenarios: Dict[str, float]
    ) -> Dict[str, float]:
        """
        压力测试
        
        Args:
            portfolio_value: 投资组合价值
            stress_scenarios: 压力情景字典
            
        Returns:
            压力测试结果
        """
        results = {}
        
        for scenario_name, shock in stress_scenarios.items():
            stressed_value = portfolio_value * (1 + shock)
            loss = portfolio_value - stressed_value
            loss_ratio = loss / portfolio_value
            
            results[scenario_name] = {
                'shocked_value': stressed_value,
                'absolute_loss': loss,
                'loss_ratio': loss_ratio
            }
        
        return results
