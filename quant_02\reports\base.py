"""
报告生成基类模块

定义报告生成的基础接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import pandas as pd

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.structures.portfolio import Portfolio, StrategyResult
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ReportConfig:
    """报告配置"""
    
    # 基本设置
    title: str = "量化交易回测报告"
    subtitle: str = ""
    author: str = "Quant System"
    
    # 输出设置
    output_format: str = "html"  # html, pdf, markdown
    output_path: str = "reports"
    filename: Optional[str] = None
    
    # 内容设置
    include_summary: bool = True
    include_performance: bool = True
    include_trades: bool = True
    include_charts: bool = True
    include_risk_analysis: bool = True
    
    # 图表设置
    chart_width: int = 800
    chart_height: int = 600
    chart_theme: str = "plotly_white"
    
    # 样式设置
    template: str = "default"
    css_file: Optional[str] = None
    
    # 语言设置
    language: str = "zh"  # zh, en
    
    def get_output_filename(self, strategy_name: str = "strategy") -> str:
        """获取输出文件名"""
        if self.filename:
            return self.filename
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{strategy_name}_report_{timestamp}.{self.output_format}"


class BaseReportGenerator(ABC):
    """报告生成器基类
    
    所有报告生成器都应该继承此类。
    """
    
    def __init__(self, config: Optional[ReportConfig] = None):
        """
        初始化报告生成器
        
        Args:
            config: 报告配置
        """
        self.config = config or ReportConfig()
        
        # 确保输出目录存在
        output_dir = Path(self.config.output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"报告生成器初始化: {self.__class__.__name__}")
    
    @abstractmethod
    def generate(
        self,
        result: StrategyResult,
        benchmark_data: Optional[pd.DataFrame] = None,
        **kwargs
    ) -> str:
        """
        生成报告
        
        Args:
            result: 策略结果
            benchmark_data: 基准数据
            **kwargs: 其他参数
            
        Returns:
            报告文件路径
        """
        pass
    
    def _prepare_data(self, result: StrategyResult) -> Dict[str, Any]:
        """准备报告数据"""
        data = {
            'strategy_info': {
                'name': result.strategy_id,
                'symbol': result.symbol,
                'start_date': result.start_date,
                'end_date': result.end_date,
                'total_days': (result.end_date - result.start_date).days,
            },
            'performance_metrics': {
                'total_return': result.total_return,
                'annual_return': result.annual_return,
                'volatility': result.volatility,
                'sharpe_ratio': result.sharpe_ratio,
                'max_drawdown': result.max_drawdown,
                'total_trades': result.total_trades,
                'win_rate': result.win_rate,
            },
            'portfolio': result.portfolio,
            'equity_curve': result.equity_curve,
            'trades': result.trades,
            'signals': result.signals
        }
        
        return data
    
    def _calculate_additional_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """计算额外的性能指标"""
        portfolio = data['portfolio']
        equity_curve = data['equity_curve']
        
        additional_metrics = {}
        
        if len(equity_curve) > 1:
            # 计算收益率序列
            returns = equity_curve.pct_change().dropna()
            
            if len(returns) > 0:
                # 基本统计
                additional_metrics.update({
                    'total_days': len(equity_curve),
                    'trading_days': len(returns),
                    'avg_daily_return': returns.mean(),
                    'daily_volatility': returns.std(),
                    'skewness': returns.skew(),
                    'kurtosis': returns.kurtosis(),
                })
                
                # 风险指标
                if len(returns) >= 20:
                    additional_metrics.update({
                        'var_95': returns.quantile(0.05),
                        'var_99': returns.quantile(0.01),
                        'best_day': returns.max(),
                        'worst_day': returns.min(),
                    })
                
                # 回撤分析
                peak = equity_curve.cummax()
                drawdown = (equity_curve - peak) / peak
                additional_metrics.update({
                    'current_drawdown': abs(drawdown.iloc[-1]),
                    'avg_drawdown': abs(drawdown[drawdown < 0].mean()) if (drawdown < 0).any() else 0,
                    'drawdown_periods': (drawdown < -0.05).sum(),  # 超过5%回撤的天数
                })
        
        # 交易分析
        trades = data['trades']
        if trades:
            trade_returns = []
            for trade in trades:
                if hasattr(trade, 'pnl'):
                    trade_returns.append(trade.pnl)
            
            if trade_returns:
                winning_trades = [r for r in trade_returns if r > 0]
                losing_trades = [r for r in trade_returns if r < 0]
                
                additional_metrics.update({
                    'avg_trade_return': sum(trade_returns) / len(trade_returns),
                    'avg_winning_trade': sum(winning_trades) / len(winning_trades) if winning_trades else 0,
                    'avg_losing_trade': sum(losing_trades) / len(losing_trades) if losing_trades else 0,
                    'largest_win': max(trade_returns) if trade_returns else 0,
                    'largest_loss': min(trade_returns) if trade_returns else 0,
                    'profit_factor': abs(sum(winning_trades) / sum(losing_trades)) if losing_trades else float('inf'),
                })
        
        return additional_metrics
    
    def _create_charts(self, data: Dict[str, Any]) -> Dict[str, str]:
        """创建图表"""
        charts = {}
        
        try:
            import plotly.graph_objects as go
            import plotly.express as px
            from plotly.subplots import make_subplots
            
            # 权益曲线图
            equity_curve = data['equity_curve']
            if len(equity_curve) > 0:
                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=equity_curve.index,
                    y=equity_curve.values,
                    mode='lines',
                    name='权益曲线',
                    line=dict(color='blue', width=2)
                ))
                
                fig.update_layout(
                    title='权益曲线',
                    xaxis_title='日期',
                    yaxis_title='权益价值',
                    template=self.config.chart_theme,
                    width=self.config.chart_width,
                    height=self.config.chart_height
                )
                
                charts['equity_curve'] = fig.to_html(include_plotlyjs='cdn')
            
            # 回撤图
            if len(equity_curve) > 1:
                peak = equity_curve.cummax()
                drawdown = (equity_curve - peak) / peak * 100
                
                fig = go.Figure()
                fig.add_trace(go.Scatter(
                    x=drawdown.index,
                    y=drawdown.values,
                    mode='lines',
                    name='回撤',
                    fill='tonexty',
                    line=dict(color='red', width=1)
                ))
                
                fig.update_layout(
                    title='回撤分析',
                    xaxis_title='日期',
                    yaxis_title='回撤 (%)',
                    template=self.config.chart_theme,
                    width=self.config.chart_width,
                    height=self.config.chart_height
                )
                
                charts['drawdown'] = fig.to_html(include_plotlyjs='cdn')
            
            # 月度收益热力图
            if len(equity_curve) > 30:
                monthly_returns = equity_curve.resample('M').last().pct_change().dropna()
                
                if len(monthly_returns) > 0:
                    # 创建年月矩阵
                    monthly_data = monthly_returns.to_frame('returns')
                    monthly_data['year'] = monthly_data.index.year
                    monthly_data['month'] = monthly_data.index.month
                    
                    pivot_table = monthly_data.pivot_table(
                        values='returns', 
                        index='year', 
                        columns='month', 
                        fill_value=0
                    ) * 100
                    
                    fig = px.imshow(
                        pivot_table,
                        labels=dict(x="月份", y="年份", color="收益率(%)"),
                        title="月度收益率热力图",
                        color_continuous_scale='RdYlGn'
                    )
                    
                    fig.update_layout(
                        template=self.config.chart_theme,
                        width=self.config.chart_width,
                        height=self.config.chart_height
                    )
                    
                    charts['monthly_returns'] = fig.to_html(include_plotlyjs='cdn')
            
        except ImportError:
            logger.warning("plotly未安装，无法生成图表")
        except Exception as e:
            logger.error(f"图表生成失败: {e}")
        
        return charts
    
    def _format_number(self, value: float, format_type: str = 'default') -> str:
        """格式化数字"""
        if pd.isna(value):
            return "N/A"
        
        if format_type == 'percentage':
            return f"{value:.2%}"
        elif format_type == 'currency':
            return f"¥{value:,.2f}"
        elif format_type == 'ratio':
            return f"{value:.2f}"
        else:
            return f"{value:.4f}"
    
    def _get_template_path(self) -> Path:
        """获取模板路径"""
        template_dir = Path(__file__).parent / "templates"
        template_file = f"{self.config.template}.html"
        return template_dir / template_file
    
    def _save_report(self, content: str, filename: str) -> str:
        """保存报告"""
        output_path = Path(self.config.output_path) / filename
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"报告已保存: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
            raise
    
    def set_config(self, config: ReportConfig):
        """设置报告配置"""
        self.config = config
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的输出格式"""
        return ['html', 'markdown']  # 基类支持的格式
