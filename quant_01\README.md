# Quant_01 - 高性能量化回测引擎

基于quant_new优化的下一代量化回测系统，专注于高性能、可扩展性和易管理性。

## 🚀 核心特性

### 架构优化
- **模块化设计**：清晰的分层架构，高度解耦
- **插件化系统**：策略、数据源、指标可插拔
- **依赖注入**：提高模块间的灵活性
- **工厂模式**：支持动态组件加载

### 性能优化
- **向量化计算**：基于NumPy/Pandas的高效计算
- **智能缓存**：多级缓存系统，支持分布式
- **并行处理**：异步数据获取，并行回测
- **内存优化**：懒加载和数据分页

### 可管理性
- **配置中心**：统一的配置管理系统
- **监控系统**：实时性能和资源监控
- **日志系统**：结构化日志，支持分析
- **错误处理**：完善的异常处理和恢复

## 📁 项目结构

```
quant_01/
├── core/                    # 核心引擎
│   ├── engine/             # 回测引擎
│   │   ├── base.py         # 引擎基类
│   │   ├── single.py       # 单资产引擎
│   │   ├── multi.py        # 多资产引擎
│   │   └── parallel.py     # 并行引擎
│   ├── config/             # 配置管理
│   │   ├── base.py         # 配置基类
│   │   ├── backtest.py     # 回测配置
│   │   ├── risk.py         # 风险配置
│   │   └── global_config.py # 全局配置
│   ├── data_structures/    # 数据结构
│   │   ├── market.py       # 市场数据结构
│   │   ├── trading.py      # 交易数据结构
│   │   └── portfolio.py    # 组合数据结构
│   ├── exceptions/         # 异常定义
│   └── interfaces/         # 接口定义
├── dataseed/               # 数据源模块
│   ├── base.py             # 数据源基类
│   ├── akshare_source.py   # AkShare数据源
│   ├── database_source.py  # 数据库数据源
│   ├── mock_source.py      # 模拟数据源
│   ├── adapter.py          # 数据适配器
│   └── factory.py          # 数据源工厂
├── strategies/             # 策略模块
│   ├── base/               # 策略基类
│   ├── single/             # 单标策略
│   ├── multi/              # 多标策略
│   ├── dynamic/            # 动态策略
│   ├── factory.py          # 策略工厂
│   └── registry.py         # 策略注册器
├── indicators/             # 技术指标库
│   ├── trend/              # 趋势指标
│   ├── momentum/           # 动量指标
│   ├── volatility/         # 波动率指标
│   ├── volume/             # 成交量指标
│   ├── base.py             # 指标基类
│   └── factory.py          # 指标工厂
├── risk/                   # 风险管理
│   ├── manager.py          # 风险管理器
│   ├── rules/              # 风险规则
│   ├── metrics.py          # 风险指标
│   └── monitor.py          # 风险监控
├── optimizer/              # 参数优化
│   ├── grid_search.py      # 网格搜索
│   ├── bayesian.py         # 贝叶斯优化
│   ├── genetic.py          # 遗传算法
│   └── multi_objective.py  # 多目标优化
├── reports/                # 报告生成
│   ├── generators/         # 报告生成器
│   ├── templates/          # 报告模板
│   └── exporters/          # 导出器
├── utils/                  # 工具模块
│   ├── cache/              # 缓存系统
│   ├── logger/             # 日志系统
│   ├── decorators/         # 装饰器
│   ├── validators/         # 验证器
│   └── helpers/            # 辅助函数
├── tests/                  # 测试模块
│   ├── unit/               # 单元测试
│   ├── integration/        # 集成测试
│   └── performance/        # 性能测试
├── templates/              # 配置模板
├── docs/                   # 文档
├── examples/               # 示例代码
├── requirements.txt        # 依赖文件
├── setup.py               # 安装脚本
└── main.py                # 主入口
```

## 🔧 技术栈

### 核心技术
- **Python 3.8+**：主要开发语言
- **Pandas/NumPy**：数据处理和计算
- **VectorBT**：高性能回测引擎
- **Pydantic v2**：配置管理和数据验证

### 性能优化
- **Numba**：JIT编译加速
- **Redis**：分布式缓存
- **asyncio**：异步编程
- **multiprocessing**：并行计算

### 可选组件
- **FastAPI**：API接口
- **Streamlit**：监控界面
- **SQLAlchemy 2.0**：数据库ORM
- **Loguru**：高性能日志

## 🚀 快速开始

### 安装依赖
```bash
cd quant_01
pip install -r requirements.txt
```

### 基本使用
```python
from quant_01 import QuantEngine
from quant_01.strategies import MACDStrategy
from quant_01.dataseed import MockDataSource

# 创建引擎
engine = QuantEngine()

# 配置数据源
engine.set_data_source(MockDataSource())

# 运行策略
result = engine.run_strategy(
    strategy=MACDStrategy(),
    symbol="000001",
    start_date="2023-01-01",
    end_date="2023-12-31"
)

print(f"总收益率: {result.total_return:.2%}")
```

## 📈 性能目标

- **数据处理**：百万级K线数据 < 0.5秒
- **策略回测**：单策略年度回测 < 0.05秒
- **批量回测**：100只股票并行 < 5秒
- **参数优化**：100个参数组合 < 10秒
- **内存使用**：优化50%以上
- **测试覆盖率**：90%+

## 🎯 优化亮点

1. **插件化架构**：支持动态加载策略和数据源
2. **智能缓存**：多级缓存，自动失效管理
3. **并行优化**：充分利用多核CPU资源
4. **内存优化**：懒加载和智能数据管理
5. **监控系统**：实时性能和资源监控
6. **错误恢复**：完善的异常处理机制

## 📚 文档

- [架构设计](docs/architecture.md)
- [API参考](docs/api.md)
- [策略开发指南](docs/strategy_guide.md)
- [性能优化指南](docs/performance.md)
- [部署指南](docs/deployment.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

MIT License
