"""
回测配置

定义回测相关的配置参数。
"""

from typing import Optional, List
from pydantic import Field, validator
from .base import BaseConfig


class BacktestConfig(BaseConfig):
    """回测配置"""
    
    config_name: str = Field(default="backtest", description="配置名称")
    
    # 基本参数
    initial_capital: float = Field(default=1000000.0, description="初始资金", gt=0)
    commission_rate: float = Field(default=0.0003, description="手续费率", ge=0, le=0.1)
    slippage_rate: float = Field(default=0.0001, description="滑点率", ge=0, le=0.1)
    
    # 交易参数
    max_position_size: float = Field(default=1.0, description="最大仓位比例", ge=0, le=1)
    min_trade_amount: float = Field(default=1000.0, description="最小交易金额", ge=0)
    
    # 时间参数
    start_date: Optional[str] = Field(default=None, description="回测开始日期")
    end_date: Optional[str] = Field(default=None, description="回测结束日期")
    frequency: str = Field(default="1d", description="数据频率")
    
    # 基准参数
    benchmark: str = Field(default="000300", description="基准指数")
    
    # 风险参数
    enable_risk_management: bool = Field(default=True, description="是否启用风险管理")
    max_drawdown_limit: Optional[float] = Field(default=None, description="最大回撤限制")
    
    @validator('frequency')
    def validate_frequency(cls, v):
        valid_frequencies = ['1m', '5m', '15m', '30m', '1h', '1d', '1w', '1M']
        if v not in valid_frequencies:
            raise ValueError(f"数据频率必须是: {valid_frequencies}")
        return v
