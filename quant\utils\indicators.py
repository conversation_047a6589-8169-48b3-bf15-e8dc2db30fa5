"""
技术指标计算模块

提供各种常用技术指标的计算函数。
"""

import pandas as pd
import numpy as np
import vectorbt as vbt
from typing import Tuple, Union, Optional


def moving_average(data: pd.DataFrame, window: int, column: str = 'close') -> pd.Series:
    """
    计算移动平均线
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        window (int): 移动平均窗口大小
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        pd.Series: 移动平均线
    """
    return data[column].rolling(window=window).mean()


def exponential_moving_average(data: pd.DataFrame, window: int, column: str = 'close') -> pd.Series:
    """
    计算指数移动平均线
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        window (int): 移动平均窗口大小
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        pd.Series: 指数移动平均线
    """
    return data[column].ewm(span=window, adjust=False).mean()


def relative_strength_index(data: pd.DataFrame, window: int = 14, column: str = 'close') -> pd.Series:
    """
    计算相对强弱指标(RSI)
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        window (int): 计算窗口大小，默认为14
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        pd.Series: RSI值
    """
    delta = data[column].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=window).mean()
    avg_loss = loss.rolling(window=window).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def bollinger_bands(data: pd.DataFrame, window: int = 20, num_std: float = 2, 
                   column: str = 'close') -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    计算布林带
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        window (int): 移动平均窗口大小，默认为20
        num_std (float): 标准差的倍数，默认为2
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        tuple: (中轨, 上轨, 下轨)
    """
    middle_band = data[column].rolling(window=window).mean()
    std = data[column].rolling(window=window).std()
    
    upper_band = middle_band + (std * num_std)
    lower_band = middle_band - (std * num_std)
    
    return middle_band, upper_band, lower_band


def macd(data: pd.DataFrame, fast_period: int = 12, slow_period: int = 26, 
         signal_period: int = 9, column: str = 'close') -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    计算MACD指标
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame
        fast_period (int): 快线周期，默认为12
        slow_period (int): 慢线周期，默认为26
        signal_period (int): 信号线周期，默认为9
        column (str): 用于计算的列名，默认为'close'
        
    返回:
        tuple: (MACD线, 信号线, 柱状图)
    """
    # 计算快速和慢速EMA
    ema_fast = data[column].ewm(span=fast_period).mean()
    ema_slow = data[column].ewm(span=slow_period).mean()
    
    # 计算MACD线
    macd_line = ema_fast - ema_slow
    
    # 计算信号线
    signal_line = macd_line.ewm(span=signal_period).mean()
    
    # 计算柱状图
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram


def stochastic_oscillator(data: pd.DataFrame, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
    """
    计算随机振荡器(KD指标)
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame，必须包含'high', 'low', 'close'列
        k_period (int): K值计算周期，默认为14
        d_period (int): D值计算周期，默认为3
        
    返回:
        tuple: (K值, D值)
    """
    # 计算最高价和最低价的滚动窗口
    lowest_low = data['low'].rolling(window=k_period).min()
    highest_high = data['high'].rolling(window=k_period).max()
    
    # 计算K值
    k_percent = 100 * ((data['close'] - lowest_low) / (highest_high - lowest_low))
    
    # 计算D值（K值的移动平均）
    d_percent = k_percent.rolling(window=d_period).mean()
    
    return k_percent, d_percent


def average_true_range(data: pd.DataFrame, window: int = 14) -> pd.Series:
    """
    计算平均真实范围(ATR)指标
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame，必须包含'high', 'low', 'close'列
        window (int): 计算周期，默认为14
        
    返回:
        pd.Series: ATR值
    """
    # 计算真实范围(TR)
    high = data['high']
    low = data['low']
    close = data['close']
    
    # 第一种情况：当日最高价 - 当日最低价
    tr1 = high - low
    
    # 第二种情况：|当日最高价 - 昨日收盘价|
    tr2 = abs(high - close.shift(1))
    
    # 第三种情况：|当日最低价 - 昨日收盘价|
    tr3 = abs(low - close.shift(1))
    
    # 取三种情况的最大值作为真实范围
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算ATR
    atr = tr.rolling(window=window).mean()
    
    return atr


def williams_r(data: pd.DataFrame, window: int = 14) -> pd.Series:
    """
    计算威廉指标(%R)
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame，必须包含'high', 'low', 'close'列
        window (int): 计算周期，默认为14
        
    返回:
        pd.Series: 威廉指标值
    """
    highest_high = data['high'].rolling(window=window).max()
    lowest_low = data['low'].rolling(window=window).min()
    
    wr = -100 * ((highest_high - data['close']) / (highest_high - lowest_low))
    
    return wr


def commodity_channel_index(data: pd.DataFrame, window: int = 20) -> pd.Series:
    """
    计算商品通道指数(CCI)
    
    参数:
        data (pd.DataFrame): 包含价格数据的DataFrame，必须包含'high', 'low', 'close'列
        window (int): 计算周期，默认为20
        
    返回:
        pd.Series: CCI值
    """
    # 计算典型价格
    typical_price = (data['high'] + data['low'] + data['close']) / 3
    
    # 计算典型价格的移动平均
    sma_tp = typical_price.rolling(window=window).mean()
    
    # 计算平均绝对偏差
    mad = typical_price.rolling(window=window).apply(lambda x: np.mean(np.abs(x - x.mean())))
    
    # 计算CCI
    cci = (typical_price - sma_tp) / (0.015 * mad)
    
    return cci
