"""
数据适配器

提供标准的数据格式转换功能。
"""

from typing import Any, Dict, List, Optional
import pandas as pd
import numpy as np
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from dataseed.base import DataAdapter
from utils.logger import get_logger

logger = get_logger(__name__)


class StandardDataAdapter(DataAdapter):
    """标准数据适配器

    将各种格式的数据转换为标准的OHLCV格式。
    """

    def __init__(self, column_mapping: Optional[Dict[str, str]] = None):
        """
        初始化标准数据适配器

        Args:
            column_mapping: 列名映射字典
        """
        self.column_mapping = column_mapping or self._get_default_mapping()

    def _get_default_mapping(self) -> Dict[str, str]:
        """获取默认列名映射"""
        return {
            # 中文列名映射
            '日期': 'date',
            '时间': 'date',
            '开盘': 'open',
            '开盘价': 'open',
            '最高': 'high',
            '最高价': 'high',
            '最低': 'low',
            '最低价': 'low',
            '收盘': 'close',
            '收盘价': 'close',
            '成交量': 'volume',
            '成交额': 'amount',
            '涨跌幅': 'pct_change',
            '涨跌额': 'change',
            '振幅': 'amplitude',
            '换手率': 'turnover',

            # 英文列名映射
            'Date': 'date',
            'Time': 'date',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'Amount': 'amount',
            'Adj Close': 'adj_close',
            'Adj_Close': 'adj_close',

            # 其他常见格式
            'o': 'open',
            'h': 'high',
            'l': 'low',
            'c': 'close',
            'v': 'volume',
            'vol': 'volume',
            'amt': 'amount',
        }

    def adapt(self, data: Any) -> pd.DataFrame:
        """
        适配数据格式

        Args:
            data: 原始数据

        Returns:
            标准格式的DataFrame
        """
        if isinstance(data, pd.DataFrame):
            return self._adapt_dataframe(data)
        elif isinstance(data, dict):
            return self._adapt_dict(data)
        elif isinstance(data, list):
            return self._adapt_list(data)
        else:
            raise ValueError(f"不支持的数据类型: {type(data)}")

    def _adapt_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """适配DataFrame格式"""
        if df.empty:
            return df

        # 复制数据
        result = df.copy()

        # 重命名列
        result = result.rename(columns=self.column_mapping)

        # 确保必要列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in result.columns]

        if missing_columns:
            logger.warning(f"缺少必要列: {missing_columns}")
            # 尝试从现有列推断
            result = self._infer_missing_columns(result, missing_columns)

        # 数据类型转换
        result = self._convert_data_types(result)

        # 处理日期索引
        result = self._process_date_index(result)

        # 数据清洗
        result = self._clean_data(result)

        return result

    def _adapt_dict(self, data: dict) -> pd.DataFrame:
        """适配字典格式"""
        # 将字典转换为DataFrame
        df = pd.DataFrame(data)
        return self._adapt_dataframe(df)

    def _adapt_list(self, data: list) -> pd.DataFrame:
        """适配列表格式"""
        if not data:
            return pd.DataFrame()

        # 检查列表元素类型
        if isinstance(data[0], dict):
            # 字典列表
            df = pd.DataFrame(data)
        elif isinstance(data[0], (list, tuple)):
            # 嵌套列表
            df = pd.DataFrame(data)
        else:
            raise ValueError("不支持的列表格式")

        return self._adapt_dataframe(df)

    def _infer_missing_columns(self, df: pd.DataFrame, missing_columns: List[str]) -> pd.DataFrame:
        """推断缺失列"""
        result = df.copy()

        for col in missing_columns:
            if col == 'volume' and 'vol' in df.columns:
                result['volume'] = df['vol']
            elif col == 'volume' and '成交量' in df.columns:
                result['volume'] = df['成交量']
            elif col in ['open', 'high', 'low', 'close']:
                # 如果只有收盘价，用收盘价填充其他价格
                if 'close' in df.columns:
                    result[col] = df['close']
                elif '收盘' in df.columns:
                    result[col] = df['收盘']
                else:
                    logger.warning(f"无法推断列: {col}")

        return result

    def _convert_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """转换数据类型"""
        result = df.copy()

        # 数值列
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount',
                          'pct_change', 'change', 'amplitude', 'turnover']

        for col in numeric_columns:
            if col in result.columns:
                result[col] = pd.to_numeric(result[col], errors='coerce')

        return result

    def _process_date_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理日期索引"""
        result = df.copy()

        # 查找日期列
        date_columns = ['date', 'Date', '日期', 'time', 'Time', '时间']
        date_col = None

        for col in date_columns:
            if col in result.columns:
                date_col = col
                break

        if date_col:
            # 转换为日期时间
            result[date_col] = pd.to_datetime(result[date_col], errors='coerce')

            # 设置为索引
            if not result[date_col].isna().all():
                result.set_index(date_col, inplace=True)
                result.index.name = 'date'

        return result

    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        result = df.copy()

        # 移除无效行
        required_columns = ['open', 'high', 'low', 'close']
        available_columns = [col for col in required_columns if col in result.columns]

        if available_columns:
            # 移除价格为0或负数的行
            for col in available_columns:
                result = result[result[col] > 0]

            # 移除价格逻辑错误的行
            if all(col in result.columns for col in ['open', 'high', 'low', 'close']):
                valid_rows = (
                    (result['high'] >= result[['open', 'close']].max(axis=1)) &
                    (result['low'] <= result[['open', 'close']].min(axis=1))
                )
                result = result[valid_rows]

        # 移除成交量为负的行
        if 'volume' in result.columns:
            result = result[result['volume'] >= 0]

        # 按日期排序
        if isinstance(result.index, pd.DatetimeIndex):
            result = result.sort_index()

        return result

    def validate(self, data: pd.DataFrame) -> bool:
        """
        验证数据格式

        Args:
            data: 数据DataFrame

        Returns:
            是否符合标准格式
        """
        if data.empty:
            return False

        # 检查必要列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]

        if missing_columns:
            logger.error(f"缺少必要列: {missing_columns}")
            return False

        # 检查数据类型
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(data[col]):
                logger.error(f"列 {col} 不是数值类型")
                return False

        # 检查价格逻辑
        if len(data) > 0:
            invalid_high = (data['high'] < data[['open', 'close']].max(axis=1)).any()
            invalid_low = (data['low'] > data[['open', 'close']].min(axis=1)).any()
            negative_volume = (data['volume'] < 0).any()

            if invalid_high:
                logger.error("存在无效的最高价")
                return False

            if invalid_low:
                logger.error("存在无效的最低价")
                return False

            if negative_volume:
                logger.error("存在负成交量")
                return False

        return True

    def get_column_mapping(self) -> Dict[str, str]:
        """获取列名映射"""
        return self.column_mapping.copy()

    def add_column_mapping(self, mapping: Dict[str, str]):
        """添加列名映射"""
        self.column_mapping.update(mapping)

    def set_column_mapping(self, mapping: Dict[str, str]):
        """设置列名映射"""
        self.column_mapping = mapping
