2025-05-28 07:59:41 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 07:59:41 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 07:59:41 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 07:59:41 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 07:59:41 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 07:59:49 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 07:59:49 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 07:59:49 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 07:59:49 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 07:59:49 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 07:59:49 - __main__ - [32mINFO[0m - 开始演示回测功能
2025-05-28 07:59:49 - __main__ - [32mINFO[0m - 配置创建完成: Quant_02 v2.0.0
2025-05-28 07:59:49 - dataseed.base.mock - [32mINFO[0m - 数据源初始化完成: mock
2025-05-28 07:59:49 - dataseed.base.mock - [32mINFO[0m - Mock数据源初始化完成，市场状态: normal
2025-05-28 07:59:49 - dataseed.factory - [32mINFO[0m - 数据源实例已创建: mock
2025-05-28 07:59:49 - __main__ - [32mINFO[0m - Mock数据源创建完成
2025-05-28 07:59:49 - __main__ - [32mINFO[0m - 获取数据: 000001, 2024-05-28 到 2025-05-28
2025-05-28 07:59:49 - __main__ - [32mINFO[0m - 数据获取完成，共 262 条记录
2025-05-28 07:59:49 - __main__ - [32mINFO[0m - 数据范围: 2024-05-28 00:00:00 到 2025-05-28 00:00:00
2025-05-28 07:59:49 - __main__ - [32mINFO[0m - 价格范围: 99.93 - 199.83
2025-05-28 07:59:49 - __main__ - [32mINFO[0m - 演示完成
2025-05-28 07:59:57 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 07:59:57 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 07:59:57 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 07:59:57 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 07:59:57 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:00:07 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:00:07 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:00:07 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:00:07 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:00:07 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:09:09 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:09:09 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:09:09 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:09:09 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:09:09 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:09:09 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:09:09 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:09:09 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:09:09 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:09:09 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:09:09 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:09:09 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:09:09 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:09:09 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:09:09 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:09:18 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:09:18 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:09:18 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:09:18 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:09:18 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:09:18 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:09:18 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:09:18 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:09:18 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:09:18 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:09:18 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:09:18 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:09:18 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:09:18 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:09:18 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:09:53 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:09:53 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:09:53 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:09:53 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:09:53 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:09:53 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:09:53 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:09:53 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:09:53 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:09:53 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:09:53 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:09:53 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:09:53 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:09:53 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:09:53 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - 开始演示回测功能
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - 配置创建完成: Quant_02 v2.0.0
2025-05-28 08:09:53 - dataseed.base.mock - [32mINFO[0m - 数据源初始化完成: mock
2025-05-28 08:09:53 - dataseed.base.mock - [32mINFO[0m - Mock数据源初始化完成，市场状态: normal
2025-05-28 08:09:53 - dataseed.factory - [32mINFO[0m - 数据源实例已创建: mock
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - Mock数据源创建完成
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - 获取数据: 000001, 2024-05-28 到 2025-05-28
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - 数据获取完成，共 262 条记录
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - 数据范围: 2024-05-28 00:00:00 到 2025-05-28 00:00:00
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - 价格范围: 95.13 - 172.61
2025-05-28 08:09:53 - strategies.base.strategy - [32mINFO[0m - 策略初始化: macd_demo (4af4c84e-a846-4e84-a6e4-48858524118a)
2025-05-28 08:09:53 - strategies.single.macd - [32mINFO[0m - MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - MACD策略创建完成
2025-05-28 08:09:53 - strategies.single.macd - [32mINFO[0m - MACD信号生成完成: 买入信号=4, 卖出信号=4
2025-05-28 08:09:53 - __main__ - [33mWARNING[0m - 技术指标演示失败: 'MACD' object has no attribute 'fast_period'
2025-05-28 08:09:53 - __main__ - [32mINFO[0m - 演示完成
2025-05-28 08:26:58 - utils.cache - [32mINFO[0m - 缓存管理器初始化: max_size=1000, policy=lru
2025-05-28 08:26:58 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:26:58 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:26:58 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:26:58 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:26:58 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:26:58 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:26:58 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:26:58 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:26:58 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:26:58 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:26:58 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:26:58 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:26:58 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:26:58 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:26:58 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - 开始演示回测功能
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - 配置创建完成: Quant_02 v2.0.0
2025-05-28 08:26:58 - dataseed.base.mock - [32mINFO[0m - 数据源初始化完成: mock
2025-05-28 08:26:58 - dataseed.base.mock - [32mINFO[0m - Mock数据源初始化完成，市场状态: normal
2025-05-28 08:26:58 - dataseed.factory - [32mINFO[0m - 数据源实例已创建: mock
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - Mock数据源创建完成
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - 获取数据: 000001, 2024-05-28 到 2025-05-28
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - 数据获取完成，共 262 条记录
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - 数据范围: 2024-05-28 00:00:00 到 2025-05-28 00:00:00
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - 价格范围: 76.25 - 107.17
2025-05-28 08:26:58 - strategies.base.strategy - [32mINFO[0m - 策略初始化: macd_demo (a2a578f5-90b5-4fcf-82f4-5d4c67bf48a4)
2025-05-28 08:26:58 - strategies.single.macd - [32mINFO[0m - MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - MACD策略创建完成
2025-05-28 08:26:58 - strategies.single.macd - [32mINFO[0m - MACD信号生成完成: 买入信号=3, 卖出信号=3
2025-05-28 08:26:58 - __main__ - [33mWARNING[0m - 技术指标演示失败: 'MACD' object has no attribute 'fast_period'
2025-05-28 08:26:58 - __main__ - [32mINFO[0m - 演示完成
2025-05-28 08:27:10 - utils.cache - [32mINFO[0m - 缓存管理器初始化: max_size=1000, policy=lru
2025-05-28 08:27:10 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:27:10 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:27:10 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:27:10 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:27:10 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:27:10 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:27:10 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:27:10 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:27:10 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:27:10 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:27:10 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:27:10 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:27:10 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:27:10 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:27:10 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:27:55 - utils.cache - [32mINFO[0m - 缓存管理器初始化: max_size=1000, policy=lru
2025-05-28 08:27:55 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:27:55 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:27:55 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:27:55 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:27:55 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:27:55 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:27:55 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:27:55 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:27:55 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:27:55 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:27:55 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:27:55 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:27:55 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:27:55 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:27:55 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:28:20 - utils.cache - [32mINFO[0m - 缓存管理器初始化: max_size=1000, policy=lru
2025-05-28 08:28:20 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:28:20 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:28:20 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:28:20 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:28:20 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:28:20 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:28:20 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:28:20 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:28:20 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:28:20 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:28:20 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:28:20 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:28:20 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:28:20 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:28:20 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:28:20 - risk.manager - [32mINFO[0m - 风险管理器初始化完成
2025-05-28 08:28:39 - utils.cache - [32mINFO[0m - 缓存管理器初始化: max_size=1000, policy=lru
2025-05-28 08:28:39 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:28:39 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:28:39 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:28:39 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:28:39 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:28:39 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:28:39 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:28:39 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:28:39 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:28:39 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:28:39 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:28:39 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:28:39 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:28:39 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:28:39 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:28:39 - risk.manager - [32mINFO[0m - 风险管理器初始化完成
2025-05-28 08:28:39 - risk.metrics - [32mINFO[0m - 风险指标计算器初始化完成
2025-05-28 08:29:40 - utils.cache - [32mINFO[0m - 缓存管理器初始化: max_size=1000, policy=lru
2025-05-28 08:29:40 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:29:40 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:29:40 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:29:40 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:29:40 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:29:40 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:29:40 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:29:40 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:29:40 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:29:40 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:29:40 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:29:40 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:29:40 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:29:40 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:29:40 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:29:52 - utils.cache - [32mINFO[0m - 缓存管理器初始化: max_size=1000, policy=lru
2025-05-28 08:29:52 - dataseed.factory - [32mINFO[0m - 数据源已注册: mock
2025-05-28 08:29:52 - dataseed.factory - [33mWARNING[0m - AkShare数据源不可用，请安装akshare: pip install akshare
2025-05-28 08:29:52 - dataseed.factory - [33mWARNING[0m - 数据库数据源不可用，请安装相关依赖
2025-05-28 08:29:52 - dataseed.factory - [32mINFO[0m - 内置数据源注册完成
2025-05-28 08:29:52 - dataseed.factory - [32mINFO[0m - 数据源工厂初始化完成
2025-05-28 08:29:52 - strategies.factory - [32mINFO[0m - 策略工厂初始化完成
2025-05-28 08:29:52 - strategies.registry - [32mINFO[0m - 策略注册器初始化完成
2025-05-28 08:29:52 - strategies.factory - [32mINFO[0m - 策略已注册: macd
2025-05-28 08:29:52 - strategies.registry - [32mINFO[0m - 策略已注册到注册器: macd (分类: trend)
2025-05-28 08:29:52 - indicators.factory - [32mINFO[0m - 技术指标已注册: sma (分类: trend)
2025-05-28 08:29:52 - indicators.factory - [32mINFO[0m - 技术指标已注册: ema (分类: trend)
2025-05-28 08:29:52 - indicators.factory - [32mINFO[0m - 技术指标已注册: wma (分类: trend)
2025-05-28 08:29:52 - indicators.factory - [32mINFO[0m - 技术指标已注册: macd (分类: trend)
2025-05-28 08:29:52 - indicators.factory - [33mWARNING[0m - 部分技术指标不可用: No module named 'indicators.momentum.stochastic'
2025-05-28 08:29:52 - indicators.factory - [32mINFO[0m - 技术指标工厂初始化完成
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - 开始演示回测功能
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - 配置创建完成: Quant_02 v2.0.0
2025-05-28 08:29:52 - dataseed.base.mock - [32mINFO[0m - 数据源初始化完成: mock
2025-05-28 08:29:52 - dataseed.base.mock - [32mINFO[0m - Mock数据源初始化完成，市场状态: normal
2025-05-28 08:29:52 - dataseed.factory - [32mINFO[0m - 数据源实例已创建: mock
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - Mock数据源创建完成
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - 获取数据: 000001, 2024-05-28 到 2025-05-28
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - 数据获取完成，共 262 条记录
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - 数据范围: 2024-05-28 00:00:00 到 2025-05-28 00:00:00
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - 价格范围: 44.37 - 100.00
2025-05-28 08:29:52 - strategies.base.strategy - [32mINFO[0m - 策略初始化: macd_demo (86ad9e19-4598-455e-bf6f-0399acb6324b)
2025-05-28 08:29:52 - strategies.single.macd - [32mINFO[0m - MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - MACD策略创建完成
2025-05-28 08:29:52 - strategies.single.macd - [32mINFO[0m - MACD信号生成完成: 买入信号=4, 卖出信号=5
2025-05-28 08:29:52 - __main__ - [33mWARNING[0m - 技术指标演示失败: 'MACD' object has no attribute 'fast_period'
2025-05-28 08:29:52 - __main__ - [32mINFO[0m - 演示完成
