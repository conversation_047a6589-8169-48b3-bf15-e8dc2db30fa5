"""
日志系统模块

基于Loguru的高性能日志系统，支持结构化日志、多级别输出、自动轮转等功能。
"""

import sys
from pathlib import Path
from typing import Optional, Dict, Any, Union
from loguru import logger
from functools import wraps
import time
import traceback

from ..core.config import config


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self._initialized = False
        self._loggers: Dict[str, Any] = {}
        
    def initialize(self, log_config: Optional[Dict[str, Any]] = None):
        """初始化日志系统"""
        if self._initialized:
            return
            
        # 移除默认处理器
        logger.remove()
        
        # 使用配置或默认配置
        if log_config is None:
            log_config = {
                'level': config.log.level,
                'format': config.log.format,
                'rotation': config.log.rotation,
                'retention': config.log.retention,
                'log_dir': config.get_log_dir()
            }
        
        # 控制台输出
        logger.add(
            sys.stdout,
            level=log_config['level'],
            format=log_config['format'],
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # 文件输出 - 所有日志
        log_dir = Path(log_config['log_dir'])
        logger.add(
            log_dir / "app.log",
            level="DEBUG",
            format=log_config['format'],
            rotation=log_config['rotation'],
            retention=log_config['retention'],
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # 文件输出 - 错误日志
        logger.add(
            log_dir / "error.log",
            level="ERROR",
            format=log_config['format'],
            rotation=log_config['rotation'],
            retention=log_config['retention'],
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # 文件输出 - 交易日志
        logger.add(
            log_dir / "trading.log",
            level="INFO",
            format=log_config['format'],
            rotation=log_config['rotation'],
            retention=log_config['retention'],
            compression="zip",
            filter=lambda record: "trading" in record["extra"]
        )
        
        # 文件输出 - 性能日志
        logger.add(
            log_dir / "performance.log",
            level="INFO", 
            format=log_config['format'],
            rotation=log_config['rotation'],
            retention=log_config['retention'],
            compression="zip",
            filter=lambda record: "performance" in record["extra"]
        )
        
        self._initialized = True
        logger.info("日志系统初始化完成")
    
    def get_logger(self, name: str) -> Any:
        """获取命名日志器"""
        if name not in self._loggers:
            self._loggers[name] = logger.bind(name=name)
        return self._loggers[name]
    
    def get_trading_logger(self) -> Any:
        """获取交易日志器"""
        return logger.bind(trading=True)
    
    def get_performance_logger(self) -> Any:
        """获取性能日志器"""
        return logger.bind(performance=True)


# 全局日志管理器实例
log_manager = LoggerManager()


def init_logger(log_config: Optional[Dict[str, Any]] = None):
    """初始化日志系统"""
    log_manager.initialize(log_config)


def get_logger(name: str = "quant") -> Any:
    """获取日志器"""
    if not log_manager._initialized:
        log_manager.initialize()
    return log_manager.get_logger(name)


def get_trading_logger() -> Any:
    """获取交易日志器"""
    if not log_manager._initialized:
        log_manager.initialize()
    return log_manager.get_trading_logger()


def get_performance_logger() -> Any:
    """获取性能日志器"""
    if not log_manager._initialized:
        log_manager.initialize()
    return log_manager.get_performance_logger()


def log_execution_time(func_name: Optional[str] = None):
    """记录函数执行时间的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            name = func_name or f"{func.__module__}.{func.__name__}"
            perf_logger = get_performance_logger()
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                perf_logger.info(
                    f"函数执行完成: {name}",
                    execution_time=execution_time,
                    args_count=len(args),
                    kwargs_count=len(kwargs)
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                perf_logger.error(
                    f"函数执行失败: {name}",
                    execution_time=execution_time,
                    error=str(e),
                    traceback=traceback.format_exc()
                )
                raise
        return wrapper
    return decorator


def log_trading_action(action_type: str):
    """记录交易行为的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            trading_logger = get_trading_logger()
            
            try:
                result = func(*args, **kwargs)
                trading_logger.info(
                    f"交易行为: {action_type}",
                    function=func.__name__,
                    args=str(args)[:200],  # 限制参数长度
                    result=str(result)[:200] if result else None
                )
                return result
            except Exception as e:
                trading_logger.error(
                    f"交易行为失败: {action_type}",
                    function=func.__name__,
                    error=str(e),
                    traceback=traceback.format_exc()
                )
                raise
        return wrapper
    return decorator


def log_method_call(logger_name: str = "quant", level: str = "DEBUG"):
    """记录方法调用的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            method_logger = get_logger(logger_name)
            
            # 记录方法调用
            getattr(method_logger, level.lower())(
                f"调用方法: {func.__module__}.{func.__name__}",
                args_count=len(args),
                kwargs_keys=list(kwargs.keys())
            )
            
            try:
                result = func(*args, **kwargs)
                getattr(method_logger, level.lower())(
                    f"方法完成: {func.__module__}.{func.__name__}"
                )
                return result
            except Exception as e:
                method_logger.error(
                    f"方法异常: {func.__module__}.{func.__name__}",
                    error=str(e)
                )
                raise
        return wrapper
    return decorator


class ContextLogger:
    """上下文日志器"""
    
    def __init__(self, logger_name: str = "quant", **context):
        self.logger = get_logger(logger_name)
        self.context = context
    
    def __enter__(self):
        self.logger.info("开始执行", **self.context)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.logger.info("执行完成", **self.context)
        else:
            self.logger.error(
                "执行异常",
                error_type=exc_type.__name__,
                error_message=str(exc_val),
                **self.context
            )


# 便捷函数
def log_info(message: str, **kwargs):
    """记录信息日志"""
    get_logger().info(message, **kwargs)


def log_warning(message: str, **kwargs):
    """记录警告日志"""
    get_logger().warning(message, **kwargs)


def log_error(message: str, **kwargs):
    """记录错误日志"""
    get_logger().error(message, **kwargs)


def log_debug(message: str, **kwargs):
    """记录调试日志"""
    get_logger().debug(message, **kwargs)


__all__ = [
    "LoggerManager",
    "init_logger",
    "get_logger",
    "get_trading_logger", 
    "get_performance_logger",
    "log_execution_time",
    "log_trading_action",
    "log_method_call",
    "ContextLogger",
    "log_info",
    "log_warning",
    "log_error",
    "log_debug"
]
