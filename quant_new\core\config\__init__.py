"""
配置管理模块

提供全局配置、回测配置、风险配置等统一管理。
基于Pydantic实现类型安全的配置验证。
"""

from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings
import os


class CacheConfig(BaseModel):
    """缓存配置"""
    enabled: bool = True
    memory_cache_size: int = Field(default=1000, description="内存缓存大小(MB)")
    disk_cache_size: int = Field(default=10000, description="磁盘缓存大小(MB)")
    cache_dir: str = Field(default="cache", description="缓存目录")
    default_ttl: int = Field(default=3600, description="默认缓存时间(秒)")


class LogConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}",
        description="日志格式"
    )
    rotation: str = Field(default="10 MB", description="日志轮转大小")
    retention: str = Field(default="30 days", description="日志保留时间")
    log_dir: str = Field(default="logs", description="日志目录")


class DataConfig(BaseModel):
    """数据配置"""
    default_source: str = Field(default="akshare", description="默认数据源")
    cache_enabled: bool = Field(default=True, description="是否启用数据缓存")
    data_dir: str = Field(default="data", description="数据存储目录")
    
    # 数据源配置
    akshare_config: Dict[str, Any] = Field(default_factory=dict)
    database_config: Dict[str, Any] = Field(default_factory=dict)


class PerformanceConfig(BaseModel):
    """性能配置"""
    parallel_enabled: bool = Field(default=True, description="是否启用并行计算")
    max_workers: Optional[int] = Field(default=None, description="最大工作线程数")
    chunk_size: int = Field(default=10000, description="数据分块大小")
    use_numba: bool = Field(default=True, description="是否使用Numba加速")


class GlobalConfig(BaseSettings):
    """全局配置"""
    
    # 基础配置
    project_name: str = Field(default="量化回测引擎", description="项目名称")
    version: str = Field(default="1.0.0", description="版本号")
    debug: bool = Field(default=False, description="调试模式")
    
    # 工作目录
    work_dir: str = Field(default=".", description="工作目录")
    output_dir: str = Field(default="output", description="输出目录")
    
    # 子配置
    cache: CacheConfig = Field(default_factory=CacheConfig)
    log: LogConfig = Field(default_factory=LogConfig)
    data: DataConfig = Field(default_factory=DataConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    
    class Config:
        env_prefix = "QUANT_"
        env_file = ".env"
        case_sensitive = False
    
    @validator('work_dir', 'output_dir')
    def validate_directories(cls, v):
        """验证目录路径"""
        path = Path(v)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
        return str(path.absolute())
    
    def get_cache_dir(self) -> Path:
        """获取缓存目录路径"""
        cache_dir = Path(self.work_dir) / self.cache.cache_dir
        cache_dir.mkdir(parents=True, exist_ok=True)
        return cache_dir
    
    def get_log_dir(self) -> Path:
        """获取日志目录路径"""
        log_dir = Path(self.work_dir) / self.log.log_dir
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir
    
    def get_data_dir(self) -> Path:
        """获取数据目录路径"""
        data_dir = Path(self.work_dir) / self.data.data_dir
        data_dir.mkdir(parents=True, exist_ok=True)
        return data_dir
    
    def get_output_dir(self) -> Path:
        """获取输出目录路径"""
        output_dir = Path(self.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        return output_dir


# 全局配置实例
config = GlobalConfig()

__all__ = [
    "GlobalConfig",
    "CacheConfig", 
    "LogConfig",
    "DataConfig",
    "PerformanceConfig",
    "config"
]
