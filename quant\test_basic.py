"""
基本功能测试脚本

用于验证重构后的框架是否正常工作。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块是否可以正常导入"""
    print("测试模块导入...")
    
    try:
        # 测试数据源模块
        from dataseed import BaseDataSeed, Akshare, Database, Mock
        print("✓ 数据源模块导入成功")
        
        # 测试策略模块
        from strategies import BaseStrategy, MACross, RSI
        print("✓ 策略模块导入成功")
        
        # 测试核心模块
        from core import QuantEngine, ConfigManager, ReportGenerator, RiskManager
        print("✓ 核心模块导入成功")
        
        # 测试工具模块
        from utils import DataCache, setup_logger, indicators
        print("✓ 工具模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        # 测试配置管理
        from core import ConfigManager
        config = ConfigManager()
        print("✓ 配置管理器创建成功")
        
        # 测试数据源
        from dataseed import Mock
        dataseed = Mock()
        data = dataseed.get_data('TEST', '2023-01-01', '2023-01-31')
        print(f"✓ 模拟数据源工作正常，获取到 {len(data)} 条数据")
        
        # 测试策略
        from strategies import MACross
        strategy = MACross({'fast_window': 5, 'slow_window': 20})
        entries, exits = strategy.run(data)
        print(f"✓ 均线交叉策略工作正常，生成 {entries.sum()} 个入场信号")
        
        # 测试回测引擎
        from core import QuantEngine
        engine = QuantEngine(dataseed, strategy, config=config)
        print("✓ 回测引擎创建成功")
        
        # 测试报告生成器
        from core import ReportGenerator
        import vectorbt as vbt
        
        # 创建一个简单的投资组合用于测试
        portfolio = vbt.Portfolio.from_signals(
            data['close'], entries, exits, init_cash=100000
        )
        
        report = ReportGenerator(portfolio, 'TEST', 'MACross', config)
        report_data = report.generate_report_data()
        print("✓ 报告生成器工作正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("Quant 框架基本功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_basic_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！框架重构成功！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
