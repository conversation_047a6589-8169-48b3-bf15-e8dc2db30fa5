"""
测试运行器

运行所有测试并生成测试报告。
"""

import unittest
import sys
import time
from pathlib import Path
from io import StringIO

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger

logger = get_logger(__name__)


class TestResult:
    """测试结果统计"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.execution_time = 0.0
        self.failures = []
        self.errors = []
    
    def add_result(self, result: unittest.TestResult):
        """添加测试结果"""
        self.total_tests += result.testsRun
        self.failed_tests += len(result.failures)
        self.error_tests += len(result.errors)
        self.skipped_tests += len(result.skipped)
        self.passed_tests = self.total_tests - self.failed_tests - self.error_tests - self.skipped_tests
        
        self.failures.extend(result.failures)
        self.errors.extend(result.errors)
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_tests == 0:
            return 0.0
        return self.passed_tests / self.total_tests
    
    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("测试结果摘要")
        print("="*60)
        print(f"总测试数: {self.total_tests}")
        print(f"通过: {self.passed_tests}")
        print(f"失败: {self.failed_tests}")
        print(f"错误: {self.error_tests}")
        print(f"跳过: {self.skipped_tests}")
        print(f"成功率: {self.get_success_rate():.1%}")
        print(f"执行时间: {self.execution_time:.2f}秒")
        
        if self.failures:
            print(f"\n失败的测试 ({len(self.failures)}):")
            for test, traceback in self.failures:
                print(f"  - {test}")
        
        if self.errors:
            print(f"\n错误的测试 ({len(self.errors)}):")
            for test, traceback in self.errors:
                print(f"  - {test}")
        
        print("="*60)


def discover_tests(test_dir: Path) -> unittest.TestSuite:
    """发现测试用例"""
    loader = unittest.TestLoader()
    
    # 发现所有测试
    suite = unittest.TestSuite()
    
    for test_file in test_dir.glob("test_*.py"):
        try:
            # 导入测试模块
            module_name = test_file.stem
            spec = unittest.util.spec_from_file_location(module_name, test_file)
            module = unittest.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 加载测试
            module_suite = loader.loadTestsFromModule(module)
            suite.addTest(module_suite)
            
            logger.info(f"加载测试模块: {module_name}")
            
        except Exception as e:
            logger.error(f"加载测试模块失败: {test_file} - {e}")
    
    return suite


def run_specific_tests(test_patterns: list) -> TestResult:
    """运行特定的测试"""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    for pattern in test_patterns:
        try:
            # 加载特定测试
            tests = loader.loadTestsFromName(pattern)
            suite.addTest(tests)
        except Exception as e:
            logger.error(f"加载测试失败: {pattern} - {e}")
    
    return run_test_suite(suite)


def run_test_suite(suite: unittest.TestSuite) -> TestResult:
    """运行测试套件"""
    # 创建测试运行器
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        buffer=True
    )
    
    # 运行测试
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # 创建结果对象
    test_result = TestResult()
    test_result.add_result(result)
    test_result.execution_time = end_time - start_time
    
    # 打印详细输出
    output = stream.getvalue()
    print(output)
    
    return test_result


def run_all_tests() -> TestResult:
    """运行所有测试"""
    logger.info("开始运行所有测试")
    
    test_dir = Path(__file__).parent
    suite = discover_tests(test_dir)
    
    return run_test_suite(suite)


def run_coverage_analysis():
    """运行覆盖率分析"""
    try:
        import coverage
        
        # 创建覆盖率对象
        cov = coverage.Coverage()
        cov.start()
        
        # 运行测试
        result = run_all_tests()
        
        # 停止覆盖率收集
        cov.stop()
        cov.save()
        
        # 生成报告
        print("\n" + "="*60)
        print("代码覆盖率报告")
        print("="*60)
        
        cov.report()
        
        # 生成HTML报告
        html_dir = Path(__file__).parent.parent / "coverage_html"
        cov.html_report(directory=str(html_dir))
        
        print(f"\nHTML覆盖率报告已生成: {html_dir}")
        
        return result
        
    except ImportError:
        logger.warning("coverage包未安装，跳过覆盖率分析")
        return run_all_tests()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Quant_02 测试运行器")
    parser.add_argument(
        '--coverage', 
        action='store_true', 
        help='运行覆盖率分析'
    )
    parser.add_argument(
        '--pattern', 
        nargs='+', 
        help='运行特定的测试模式'
    )
    parser.add_argument(
        '--verbose', 
        action='store_true', 
        help='详细输出'
    )
    
    args = parser.parse_args()
    
    try:
        if args.pattern:
            # 运行特定测试
            result = run_specific_tests(args.pattern)
        elif args.coverage:
            # 运行覆盖率分析
            result = run_coverage_analysis()
        else:
            # 运行所有测试
            result = run_all_tests()
        
        # 打印摘要
        result.print_summary()
        
        # 返回适当的退出码
        if result.failed_tests > 0 or result.error_tests > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试运行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
