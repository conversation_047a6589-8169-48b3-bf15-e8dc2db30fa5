"""
简单测试脚本

测试Quant_01系统的基础功能。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_data_structures():
    """测试数据结构"""
    print("测试数据结构...")
    
    from core.data_structures.market import OHLCV
    from core.data_structures.trading import Order, OrderSide, OrderType
    from datetime import datetime
    
    # 测试OHLCV
    ohlcv = OHLCV(
        open=100.0,
        high=105.0,
        low=98.0,
        close=103.0,
        volume=1000000,
        timestamp=datetime.now()
    )
    
    print(f"✓ OHLCV创建成功: {ohlcv.change_ratio:.2%}")
    
    # 测试Order
    order = Order(
        order_id="test_001",
        symbol="000001",
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=1000,
        timestamp=datetime.now()
    )
    
    print(f"✓ Order创建成功: {order.is_buy}")
    print("✓ 数据结构测试通过\n")


def test_config():
    """测试配置系统"""
    print("测试配置系统...")
    
    from core.config.global_config import GlobalConfig
    
    # 创建默认配置
    config = GlobalConfig.create_default()
    
    print(f"✓ 配置创建成功: {config.app_name}")
    print(f"✓ 环境: {config.environment}")
    print("✓ 配置系统测试通过\n")


def test_mock_data_source():
    """测试Mock数据源"""
    print("测试Mock数据源...")
    
    from dataseed.mock_source import MockDataSource
    
    # 创建Mock数据源
    mock_source = MockDataSource(
        name="test_mock",
        base_price=100.0,
        volatility=0.02
    )
    
    # 获取数据
    data = mock_source.get_data(
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-01-31"
    )
    
    print(f"✓ Mock数据获取成功: {len(data)}条记录")
    print(f"✓ 数据列: {list(data.columns)}")
    print("✓ Mock数据源测试通过\n")


def test_indicators():
    """测试技术指标"""
    print("测试技术指标...")
    
    from indicators.trend.macd import MACD
    from dataseed.mock_source import MockDataSource
    
    # 获取测试数据
    mock_source = MockDataSource()
    data = mock_source.get_data("000001", "2023-01-01", "2023-12-31")
    
    # 测试MACD指标
    macd = MACD(fast_period=12, slow_period=26, signal_period=9)
    result = macd.calculate(data['close'])
    
    print(f"✓ MACD计算成功: {len(result['macd'])}个数据点")
    print(f"✓ MACD组件: {list(result.keys())}")
    print("✓ 技术指标测试通过\n")


def test_strategy():
    """测试策略"""
    print("测试策略...")
    
    from strategies.single.macd import MACDStrategy, MACDConfig
    from dataseed.mock_source import MockDataSource
    
    # 创建策略
    config = MACDConfig(strategy_name="test_macd")
    strategy = MACDStrategy(config)
    
    # 获取测试数据
    mock_source = MockDataSource()
    data = mock_source.get_data("000001", "2023-01-01", "2023-12-31")
    
    # 运行策略
    result = strategy.run(data)
    
    print(f"✓ 策略运行成功: {result.strategy_id}")
    print(f"✓ 总收益率: {result.total_return:.2%}")
    print(f"✓ 交易次数: {result.total_trades}")
    print("✓ 策略测试通过\n")


def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("Quant_01 简单测试")
    print("=" * 50)
    
    try:
        test_data_structures()
        test_config()
        test_mock_data_source()
        test_indicators()
        test_strategy()
        
        print("=" * 50)
        print("✅ 所有测试通过！")
        print("Quant_01 系统基础功能正常")
        print("=" * 50)
        
    except Exception as e:
        print("=" * 50)
        print(f"❌ 测试失败: {e}")
        print("=" * 50)
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
