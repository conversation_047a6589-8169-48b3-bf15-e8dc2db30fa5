"""
缓存装饰器

提供基于装饰器的缓存功能。
"""

import functools
import time
from typing import Callable, Optional
import inspect

from .manager import cache_manager
from ..logger import get_logger

logger = get_logger(__name__)


def cached(
    ttl: int = None,
    key_func: Optional[Callable] = None,
    version: int = 1,
    memory_only: bool = False,
    enabled: bool = True
):
    """
    缓存装饰器

    Args:
        ttl: 缓存时间（秒）
        key_func: 自定义键生成函数
        version: 缓存版本
        memory_only: 仅使用内存缓存
        enabled: 是否启用缓存
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 如果缓存未启用，直接执行函数
            if not enabled:
                return func(*args, **kwargs)

            # 生成缓存键
            if key_func:
                try:
                    cache_key = key_func(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"自定义键生成函数失败: {e}")
                    cache_key = None
            else:
                cache_key = None

            # 使用缓存管理器
            return cache_manager.get_or_set(
                func=func,
                args=args,
                kwargs=kwargs,
                key=cache_key,
                ttl=ttl,
                version=version
            )

        # 添加缓存控制方法
        wrapper.cache_clear = lambda: cache_manager.clear(f"v{version}")
        wrapper.cache_info = lambda: cache_manager.get_stats()

        return wrapper

    return decorator


def cache_result(
    ttl: int = 3600,
    key_prefix: str = "",
    ignore_args: Optional[list] = None
):
    """
    结果缓存装饰器

    Args:
        ttl: 缓存时间
        key_prefix: 键前缀
        ignore_args: 忽略的参数名列表
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数签名
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            # 过滤忽略的参数
            if ignore_args:
                filtered_kwargs = {
                    k: v for k, v in bound_args.arguments.items()
                    if k not in ignore_args
                }
            else:
                filtered_kwargs = bound_args.arguments

            # 生成缓存键
            key_parts = [key_prefix, func.__name__]
            for k, v in filtered_kwargs.items():
                key_parts.append(f"{k}={v}")

            cache_key = ":".join(str(p) for p in key_parts if p)

            # 尝试获取缓存
            cached_value = cache_manager.get(cache_key)
            if cached_value is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_value

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            logger.debug(f"缓存设置: {cache_key}")

            return result

        return wrapper

    return decorator


def memoize(maxsize: int = 128):
    """
    内存缓存装饰器（类似functools.lru_cache）

    Args:
        maxsize: 最大缓存条目数
    """
    def decorator(func: Callable) -> Callable:
        cache = {}
        cache_order = []

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            key = str(args) + str(sorted(kwargs.items()))

            # 检查缓存
            if key in cache:
                # 更新访问顺序
                cache_order.remove(key)
                cache_order.append(key)
                return cache[key]

            # 执行函数
            result = func(*args, **kwargs)

            # 缓存结果
            cache[key] = result
            cache_order.append(key)

            # 检查缓存大小限制
            if len(cache) > maxsize:
                # 移除最旧的条目
                oldest_key = cache_order.pop(0)
                del cache[oldest_key]

            return result

        # 添加缓存控制方法
        wrapper.cache_clear = lambda: (cache.clear(), cache_order.clear())
        wrapper.cache_info = lambda: {
            'hits': 0,  # 简化实现，不统计命中率
            'misses': 0,
            'maxsize': maxsize,
            'currsize': len(cache)
        }

        return wrapper

    return decorator


def conditional_cache(
    condition_func: Callable,
    ttl: int = 3600
):
    """
    条件缓存装饰器

    Args:
        condition_func: 条件判断函数
        ttl: 缓存时间
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 检查缓存条件
            if not condition_func(*args, **kwargs):
                return func(*args, **kwargs)

            # 使用标准缓存
            return cache_manager.get_or_set(
                func=func,
                args=args,
                kwargs=kwargs,
                ttl=ttl
            )

        return wrapper

    return decorator


def cache_property(ttl: int = 3600):
    """
    属性缓存装饰器

    Args:
        ttl: 缓存时间
    """
    def decorator(func: Callable) -> property:
        attr_name = f"_cached_{func.__name__}"

        @functools.wraps(func)
        def wrapper(self):
            # 检查是否有缓存
            if hasattr(self, attr_name):
                cached_data = getattr(self, attr_name)
                if cached_data['expire_time'] > time.time():
                    return cached_data['value']

            # 计算值并缓存
            value = func(self)
            cached_data = {
                'value': value,
                'expire_time': time.time() + ttl
            }
            setattr(self, attr_name, cached_data)

            return value

        return property(wrapper)

    return decorator


# 便捷装饰器
def cache_for_minutes(minutes: int):
    """缓存指定分钟数"""
    return cached(ttl=minutes * 60)


def cache_for_hours(hours: int):
    """缓存指定小时数"""
    return cached(ttl=hours * 3600)


def cache_for_days(days: int):
    """缓存指定天数"""
    return cached(ttl=days * 86400)


__all__ = [
    "cached",
    "cache_result",
    "memoize",
    "conditional_cache",
    "cache_property",
    "cache_for_minutes",
    "cache_for_hours",
    "cache_for_days"
]
