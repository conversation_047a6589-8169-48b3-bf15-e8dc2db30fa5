"""
风险管理器模块

提供全面的风险管理功能，包括持仓控制、回撤管理、杠杆控制等。
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from dataclasses import dataclass, field

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import IRiskManager
from core.structures import Order, Position, Portfolio, OrderSide
from core.config.risk import RiskConfig
from core.exceptions import RiskManagementException, PositionLimitException, DrawdownLimitException
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class RiskMetrics:
    """风险指标数据结构"""
    
    # 基本风险指标
    portfolio_value: float = 0.0
    total_exposure: float = 0.0
    leverage: float = 1.0
    cash_ratio: float = 1.0
    
    # 回撤指标
    current_drawdown: float = 0.0
    max_drawdown: float = 0.0
    drawdown_duration: int = 0
    
    # 持仓风险
    position_count: int = 0
    max_position_size: float = 0.0
    concentration_risk: float = 0.0
    
    # 收益风险
    daily_var: float = 0.0
    portfolio_volatility: float = 0.0
    sharpe_ratio: float = 0.0
    
    # 流动性风险
    liquidity_score: float = 1.0
    turnover_rate: float = 0.0
    
    # 时间戳
    timestamp: datetime = field(default_factory=datetime.now)


class RiskManager(IRiskManager):
    """风险管理器
    
    提供全面的风险管理功能：
    - 实时风险监控
    - 持仓限制控制
    - 回撤保护
    - 杠杆控制
    - 集中度风险管理
    - 流动性风险评估
    """
    
    def __init__(self, config: RiskConfig):
        """
        初始化风险管理器
        
        Args:
            config: 风险配置
        """
        self.config = config
        
        # 风险状态
        self._risk_metrics = RiskMetrics()
        self._risk_history: List[RiskMetrics] = []
        self._alerts: List[Dict[str, Any]] = []
        
        # 监控状态
        self._monitoring_enabled = config.real_time_monitoring
        self._last_check_time = datetime.now()
        
        # 统计信息
        self._stats = {
            'risk_checks': 0,
            'violations': 0,
            'alerts_generated': 0,
            'orders_blocked': 0
        }
        
        logger.info("风险管理器初始化完成")
    
    def check_order(self, order: Order, portfolio: Portfolio) -> bool:
        """
        检查订单是否符合风险要求
        
        Args:
            order: 待检查的订单
            portfolio: 当前投资组合
            
        Returns:
            是否通过风险检查
        """
        self._stats['risk_checks'] += 1
        
        try:
            # 基本订单验证
            if not self._validate_order_basic(order):
                return False
            
            # 资金充足性检查
            if not self._check_sufficient_funds(order, portfolio):
                self._generate_alert("insufficient_funds", f"资金不足，无法执行订单: {order.order_id}")
                return False
            
            # 持仓限制检查
            if not self._check_position_limits(order, portfolio):
                self._generate_alert("position_limit", f"持仓限制，无法执行订单: {order.order_id}")
                return False
            
            # 集中度风险检查
            if not self._check_concentration_risk(order, portfolio):
                self._generate_alert("concentration_risk", f"集中度风险，无法执行订单: {order.order_id}")
                return False
            
            # 杠杆检查
            if not self._check_leverage_limit(order, portfolio):
                self._generate_alert("leverage_limit", f"杠杆限制，无法执行订单: {order.order_id}")
                return False
            
            logger.debug(f"订单风险检查通过: {order.order_id}")
            return True
            
        except Exception as e:
            logger.error(f"订单风险检查失败: {e}")
            self._stats['violations'] += 1
            return False
    
    def check_position(self, position: Position, current_price: float) -> bool:
        """
        检查持仓是否符合风险要求
        
        Args:
            position: 持仓
            current_price: 当前价格
            
        Returns:
            是否符合风险要求
        """
        try:
            # 更新持仓的未实现盈亏
            position.update_unrealized_pnl(current_price)
            
            # 止损检查
            if self.config.enable_stop_loss and position.stop_loss:
                if self._check_stop_loss_triggered(position, current_price):
                    self._generate_alert("stop_loss", f"触发止损: {position.symbol}")
                    return False
            
            # 止盈检查
            if self.config.enable_take_profit and position.take_profit:
                if self._check_take_profit_triggered(position, current_price):
                    self._generate_alert("take_profit", f"触发止盈: {position.symbol}")
                    return False
            
            # 持仓时间检查
            if not self._check_holding_period(position):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"持仓风险检查失败: {e}")
            return False
    
    def get_position_size(self, symbol: str, signal_strength: float, portfolio: Portfolio) -> float:
        """
        计算建议仓位大小
        
        Args:
            symbol: 标的代码
            signal_strength: 信号强度
            portfolio: 投资组合
            
        Returns:
            建议仓位大小
        """
        try:
            # 基础仓位大小
            base_size = portfolio.total_value * self.config.max_position_size
            
            # 根据信号强度调整
            adjusted_size = base_size * signal_strength
            
            # 风险预算调整
            if self.config.risk_budget_enabled:
                risk_budget = self._calculate_risk_budget(symbol, portfolio)
                adjusted_size = min(adjusted_size, risk_budget)
            
            # 流动性调整
            liquidity_factor = self._calculate_liquidity_factor(symbol)
            adjusted_size *= liquidity_factor
            
            # 确保不超过最大持仓限制
            max_allowed = self._get_max_position_size(symbol, portfolio)
            final_size = min(adjusted_size, max_allowed)
            
            logger.debug(f"计算仓位大小: {symbol}, 信号强度: {signal_strength}, 建议仓位: {final_size}")
            
            return final_size
            
        except Exception as e:
            logger.error(f"仓位大小计算失败: {e}")
            return 0.0
    
    def update_risk_metrics(self, portfolio: Portfolio, market_data: Optional[Dict[str, float]] = None):
        """
        更新风险指标
        
        Args:
            portfolio: 投资组合
            market_data: 市场数据
        """
        try:
            # 计算基本指标
            metrics = RiskMetrics()
            metrics.portfolio_value = portfolio.total_value
            metrics.total_exposure = portfolio.positions_value
            metrics.leverage = portfolio.leverage
            metrics.cash_ratio = portfolio.cash / portfolio.total_value if portfolio.total_value > 0 else 1.0
            
            # 计算回撤指标
            self._calculate_drawdown_metrics(metrics, portfolio)
            
            # 计算持仓风险
            self._calculate_position_metrics(metrics, portfolio)
            
            # 计算收益风险指标
            if len(portfolio.value_history) > 1:
                self._calculate_return_metrics(metrics, portfolio)
            
            # 计算流动性指标
            self._calculate_liquidity_metrics(metrics, portfolio, market_data)
            
            # 更新风险指标
            self._risk_metrics = metrics
            self._risk_history.append(metrics)
            
            # 保持历史记录在合理范围内
            if len(self._risk_history) > 1000:
                self._risk_history = self._risk_history[-1000:]
            
            # 检查风险阈值
            self._check_risk_thresholds(metrics)
            
            logger.debug("风险指标更新完成")
            
        except Exception as e:
            logger.error(f"风险指标更新失败: {e}")
    
    def _validate_order_basic(self, order: Order) -> bool:
        """基本订单验证"""
        if order.quantity <= 0:
            logger.warning(f"订单数量无效: {order.quantity}")
            return False
        
        if order.order_type.value not in ['market', 'limit']:
            logger.warning(f"不支持的订单类型: {order.order_type}")
            return False
        
        return True
    
    def _check_sufficient_funds(self, order: Order, portfolio: Portfolio) -> bool:
        """检查资金充足性"""
        if order.side == OrderSide.BUY:
            required_cash = order.quantity * (order.price or 0) * (1 + self.config.cash_reserve_ratio)
            return portfolio.cash >= required_cash
        return True
    
    def _check_position_limits(self, order: Order, portfolio: Portfolio) -> bool:
        """检查持仓限制"""
        if order.side == OrderSide.BUY:
            # 检查单个持仓限制
            order_value = order.quantity * (order.price or 0)
            position_ratio = order_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            if position_ratio > self.config.max_position_size:
                return False
            
            # 检查持仓数量限制
            if len(portfolio.positions) >= 10:  # 假设最大持仓数为10
                return False
        
        return True
    
    def _check_concentration_risk(self, order: Order, portfolio: Portfolio) -> bool:
        """检查集中度风险"""
        # 简化实现：检查单个标的占比
        if order.side == OrderSide.BUY:
            current_position = portfolio.get_position(order.symbol)
            current_value = current_position.market_value if current_position else 0
            
            order_value = order.quantity * (order.price or 0)
            total_value = current_value + order_value
            
            concentration = total_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            return concentration <= self.config.max_sector_exposure
        
        return True
    
    def _check_leverage_limit(self, order: Order, portfolio: Portfolio) -> bool:
        """检查杠杆限制"""
        if order.side == OrderSide.BUY:
            order_value = order.quantity * (order.price or 0)
            new_exposure = portfolio.positions_value + order_value
            new_leverage = new_exposure / portfolio.total_value if portfolio.total_value > 0 else 1
            
            return new_leverage <= self.config.max_leverage
        
        return True
    
    def _check_stop_loss_triggered(self, position: Position, current_price: float) -> bool:
        """检查是否触发止损"""
        if not position.stop_loss:
            return False
        
        if position.is_long:
            return current_price <= position.stop_loss
        else:
            return current_price >= position.stop_loss
    
    def _check_take_profit_triggered(self, position: Position, current_price: float) -> bool:
        """检查是否触发止盈"""
        if not position.take_profit:
            return False
        
        if position.is_long:
            return current_price >= position.take_profit
        else:
            return current_price <= position.take_profit
    
    def _check_holding_period(self, position: Position) -> bool:
        """检查持仓时间"""
        # 简化实现
        return True
    
    def _calculate_risk_budget(self, symbol: str, portfolio: Portfolio) -> float:
        """计算风险预算"""
        # 简化实现：返回固定比例
        return portfolio.total_value * 0.05
    
    def _calculate_liquidity_factor(self, symbol: str) -> float:
        """计算流动性因子"""
        # 简化实现：返回固定值
        return 1.0
    
    def _get_max_position_size(self, symbol: str, portfolio: Portfolio) -> float:
        """获取最大持仓大小"""
        return portfolio.total_value * self.config.max_position_size
    
    def _calculate_drawdown_metrics(self, metrics: RiskMetrics, portfolio: Portfolio):
        """计算回撤指标"""
        if len(portfolio.value_history) > 1:
            peak = portfolio.value_history.cummax()
            drawdown = (portfolio.value_history - peak) / peak
            
            metrics.current_drawdown = abs(drawdown.iloc[-1])
            metrics.max_drawdown = abs(drawdown.min())
            
            # 计算回撤持续时间
            current_dd = drawdown.iloc[-1]
            if current_dd < 0:
                dd_start = (drawdown == 0).iloc[::-1].idxmax()
                metrics.drawdown_duration = len(portfolio.value_history) - portfolio.value_history.index.get_loc(dd_start)
    
    def _calculate_position_metrics(self, metrics: RiskMetrics, portfolio: Portfolio):
        """计算持仓指标"""
        metrics.position_count = len(portfolio.positions)
        
        if portfolio.positions:
            position_values = [pos.market_value for pos in portfolio.positions.values()]
            metrics.max_position_size = max(position_values) / portfolio.total_value if portfolio.total_value > 0 else 0
            
            # 计算集中度风险（赫芬达尔指数）
            weights = [pv / sum(position_values) for pv in position_values]
            metrics.concentration_risk = sum(w**2 for w in weights)
    
    def _calculate_return_metrics(self, metrics: RiskMetrics, portfolio: Portfolio):
        """计算收益风险指标"""
        returns = portfolio.value_history.pct_change().dropna()
        
        if len(returns) > 0:
            metrics.portfolio_volatility = returns.std() * np.sqrt(252)
            
            if metrics.portfolio_volatility > 0:
                metrics.sharpe_ratio = (returns.mean() * 252) / metrics.portfolio_volatility
            
            # 计算VaR（简化版本）
            if len(returns) >= 20:
                metrics.daily_var = abs(returns.quantile(0.05))
    
    def _calculate_liquidity_metrics(self, metrics: RiskMetrics, portfolio: Portfolio, market_data: Optional[Dict[str, float]]):
        """计算流动性指标"""
        # 简化实现
        metrics.liquidity_score = 1.0
        metrics.turnover_rate = 0.1
    
    def _check_risk_thresholds(self, metrics: RiskMetrics):
        """检查风险阈值"""
        # 检查回撤限制
        if metrics.current_drawdown > self.config.max_drawdown:
            self._generate_alert("max_drawdown", f"当前回撤({metrics.current_drawdown:.2%})超过限制({self.config.max_drawdown:.2%})")
        
        # 检查杠杆限制
        if metrics.leverage > self.config.max_leverage:
            self._generate_alert("max_leverage", f"当前杠杆({metrics.leverage:.2f})超过限制({self.config.max_leverage:.2f})")
    
    def _generate_alert(self, alert_type: str, message: str):
        """生成风险警报"""
        alert = {
            'type': alert_type,
            'message': message,
            'timestamp': datetime.now(),
            'severity': 'high' if alert_type in ['max_drawdown', 'stop_loss'] else 'medium'
        }
        
        self._alerts.append(alert)
        self._stats['alerts_generated'] += 1
        
        logger.warning(f"风险警报: {alert_type} - {message}")
    
    def get_risk_metrics(self) -> RiskMetrics:
        """获取当前风险指标"""
        return self._risk_metrics
    
    def get_risk_history(self) -> List[RiskMetrics]:
        """获取风险历史"""
        return self._risk_history.copy()
    
    def get_alerts(self, severity: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取风险警报"""
        if severity:
            return [alert for alert in self._alerts if alert['severity'] == severity]
        return self._alerts.copy()
    
    def clear_alerts(self):
        """清空警报"""
        self._alerts.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()
