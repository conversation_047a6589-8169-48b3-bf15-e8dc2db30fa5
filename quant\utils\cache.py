"""
数据缓存模块

提供数据缓存功能，提高数据获取效率。
"""

import pickle
import json
import hashlib
from utils.logger import get_logger
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Optional, Dict


class DataCache:
    """数据缓存系统"""

    def __init__(self, cache_dir: str = None):
        """
        初始化缓存系统

        参数:
            cache_dir (str): 缓存目录路径，默认为项目根目录下的.cache目录
        """
        if cache_dir is None:
            cache_dir = Path.cwd() / '.cache'
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.logger = get_logger('cache')

        # 清理过期缓存
        self._cleanup_expired_cache()

    def _get_cache_key(self, key_components: dict) -> str:
        """
        生成缓存键

        参数:
            key_components (dict): 用于生成缓存键的组件

        返回:
            str: 缓存键
        """
        # 将组件转换为排序后的JSON字符串，确保相同的组件生成相同的键
        key_str = json.dumps(key_components, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()

    def _get_cache_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.pkl"

    def get(self, key_components: dict, max_age: Optional[timedelta] = None) -> Optional[Any]:
        """
        获取缓存数据

        参数:
            key_components (dict): 缓存键组件
            max_age (timedelta): 最大缓存年龄，None表示永不过期

        返回:
            Optional[Any]: 缓存的数据，如果没有找到或已过期则返回None
        """
        cache_key = self._get_cache_key(key_components)
        cache_path = self._get_cache_path(cache_key)

        if not cache_path.exists():
            return None

        # 检查缓存是否过期
        if max_age is not None:
            cache_time = datetime.fromtimestamp(cache_path.stat().st_mtime)
            if datetime.now() - cache_time > max_age:
                self.logger.debug(f"缓存已过期: {cache_key}")
                return None

        try:
            with open(cache_path, 'rb') as f:
                data = pickle.load(f)
                self.logger.debug(f"从缓存加载数据: {cache_key}")
                return data
        except Exception as e:
            self.logger.warning(f"读取缓存失败: {str(e)}")
            return None

    def set(self, key_components: dict, data: Any):
        """
        设置缓存数据

        参数:
            key_components (dict): 缓存键组件
            data (Any): 要缓存的数据
        """
        cache_key = self._get_cache_key(key_components)
        cache_path = self._get_cache_path(cache_key)

        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            self.logger.debug(f"数据已缓存: {cache_key}")
        except Exception as e:
            self.logger.warning(f"缓存数据失败: {str(e)}")

    def delete(self, key_components: dict) -> bool:
        """
        删除缓存数据

        参数:
            key_components (dict): 缓存键组件

        返回:
            bool: 是否成功删除
        """
        cache_key = self._get_cache_key(key_components)
        cache_path = self._get_cache_path(cache_key)

        try:
            if cache_path.exists():
                cache_path.unlink()
                self.logger.debug(f"缓存已删除: {cache_key}")
                return True
            return False
        except Exception as e:
            self.logger.warning(f"删除缓存失败: {str(e)}")
            return False

    def clear_all(self):
        """清空所有缓存"""
        try:
            for cache_file in self.cache_dir.glob("*.pkl"):
                cache_file.unlink()
            self.logger.info("所有缓存已清空")
        except Exception as e:
            self.logger.error(f"清空缓存失败: {str(e)}")

    def _cleanup_expired_cache(self, max_age_days: int = 7):
        """
        清理过期缓存

        参数:
            max_age_days (int): 最大缓存天数，默认7天
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=max_age_days)
            
            for cache_file in self.cache_dir.glob("*.pkl"):
                cache_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
                if cache_time < cutoff_time:
                    cache_file.unlink()
                    self.logger.debug(f"清理过期缓存: {cache_file.name}")
                    
        except Exception as e:
            self.logger.warning(f"清理过期缓存失败: {str(e)}")

    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息

        返回:
            dict: 缓存统计信息
        """
        try:
            cache_files = list(self.cache_dir.glob("*.pkl"))
            total_size = sum(f.stat().st_size for f in cache_files)
            
            return {
                'cache_dir': str(self.cache_dir),
                'file_count': len(cache_files),
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'oldest_file': min((f.stat().st_mtime for f in cache_files), default=None),
                'newest_file': max((f.stat().st_mtime for f in cache_files), default=None)
            }
        except Exception as e:
            self.logger.error(f"获取缓存信息失败: {str(e)}")
            return {}
