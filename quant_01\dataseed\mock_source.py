"""
Mock数据源

生成高质量的模拟数据，用于测试和演示。
"""

from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date, timedelta
import pandas as pd
import numpy as np
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from dataseed.base import DataSource
from utils.logger import get_logger

logger = get_logger(__name__)


class MockDataSource(DataSource):
    """Mock数据源

    生成符合真实市场特征的模拟数据：
    - 价格遵循几何布朗运动
    - 成交量具有合理的波动性
    - 支持多种市场状态模拟
    - 可配置的数据质量和特征
    """

    def __init__(
        self,
        name: str = "mock",
        base_price: float = 100.0,
        volatility: float = 0.02,
        trend: float = 0.0001,
        volume_base: int = 1000000,
        volume_volatility: float = 0.3,
        market_state: str = "normal",
        seed: Optional[int] = None,
        **kwargs
    ):
        """
        初始化Mock数据源

        Args:
            name: 数据源名称
            base_price: 基础价格
            volatility: 价格波动率
            trend: 价格趋势（日收益率）
            volume_base: 基础成交量
            volume_volatility: 成交量波动率
            market_state: 市场状态 (normal, bull, bear, volatile)
            seed: 随机种子
            **kwargs: 其他参数
        """
        super().__init__(name, **kwargs)

        self.base_price = base_price
        self.volatility = volatility
        self.trend = trend
        self.volume_base = volume_base
        self.volume_volatility = volume_volatility
        self.market_state = market_state

        # 设置随机种子
        if seed is not None:
            np.random.seed(seed)

        # 市场状态参数
        self._market_params = self._get_market_params(market_state)

        # 预定义的股票列表
        self._symbols = self._generate_symbol_list()

        logger.info(f"Mock数据源初始化完成: {name}, 市场状态: {market_state}")

    def _get_market_params(self, market_state: str) -> Dict[str, float]:
        """获取市场状态参数"""
        params = {
            "normal": {
                "volatility_multiplier": 1.0,
                "trend_multiplier": 1.0,
                "volume_multiplier": 1.0,
                "gap_probability": 0.02,
                "trend_change_probability": 0.05
            },
            "bull": {
                "volatility_multiplier": 0.8,
                "trend_multiplier": 2.0,
                "volume_multiplier": 1.2,
                "gap_probability": 0.01,
                "trend_change_probability": 0.02
            },
            "bear": {
                "volatility_multiplier": 1.5,
                "trend_multiplier": -1.5,
                "volume_multiplier": 1.3,
                "gap_probability": 0.03,
                "trend_change_probability": 0.08
            },
            "volatile": {
                "volatility_multiplier": 2.0,
                "trend_multiplier": 0.5,
                "volume_multiplier": 1.5,
                "gap_probability": 0.05,
                "trend_change_probability": 0.1
            }
        }

        return params.get(market_state, params["normal"])

    def _generate_symbol_list(self) -> List[str]:
        """生成股票代码列表"""
        symbols = []

        # A股代码
        for i in range(1, 1000):
            symbols.append(f"{i:06d}")

        # 指数代码
        indices = ["000001", "000300", "399001", "399006"]
        symbols.extend(indices)

        return symbols

    def _fetch_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """生成模拟数据"""
        # 转换日期
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date).date()
        elif isinstance(start_date, datetime):
            start_date = start_date.date()

        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date).date()
        elif isinstance(end_date, datetime):
            end_date = end_date.date()

        # 生成日期序列
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        # 过滤工作日（简化处理）
        date_range = date_range[date_range.weekday < 5]

        if len(date_range) == 0:
            return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])

        # 生成价格数据
        prices = self._generate_price_series(symbol, len(date_range))
        volumes = self._generate_volume_series(len(date_range))

        # 构建DataFrame
        data = pd.DataFrame({
            'open': prices['open'],
            'high': prices['high'],
            'low': prices['low'],
            'close': prices['close'],
            'volume': volumes,
        }, index=date_range)

        # 添加扩展字段
        data['amount'] = data['volume'] * (data['high'] + data['low'] + data['close']) / 3
        data['vwap'] = data['amount'] / data['volume']
        data['adj_close'] = data['close']  # 简化处理，不考虑复权

        return data

    def _generate_price_series(self, symbol: str, length: int) -> Dict[str, np.ndarray]:
        """生成价格序列"""
        # 基于股票代码生成特定的随机种子
        symbol_seed = hash(symbol) % (2**32)
        np.random.seed(symbol_seed)

        # 获取市场参数
        params = self._market_params

        # 调整参数
        volatility = self.volatility * params["volatility_multiplier"]
        trend = self.trend * params["trend_multiplier"]

        # 生成收益率序列
        returns = np.random.normal(trend, volatility, length)

        # 添加趋势变化
        if np.random.random() < params["trend_change_probability"]:
            change_point = np.random.randint(length // 4, 3 * length // 4)
            trend_change = np.random.normal(0, volatility * 2)
            returns[change_point:] += trend_change

        # 添加跳空
        gap_mask = np.random.random(length) < params["gap_probability"]
        gap_returns = np.random.normal(0, volatility * 3, length)
        returns[gap_mask] += gap_returns[gap_mask]

        # 计算价格序列
        price_series = self.base_price * np.exp(np.cumsum(returns))

        # 生成OHLC
        close_prices = price_series

        # 生成日内波动
        intraday_volatility = volatility * 0.5
        high_factors = 1 + np.abs(np.random.normal(0, intraday_volatility, length))
        low_factors = 1 - np.abs(np.random.normal(0, intraday_volatility, length))

        # 确保价格逻辑正确
        open_prices = np.zeros(length)
        high_prices = np.zeros(length)
        low_prices = np.zeros(length)

        open_prices[0] = self.base_price
        for i in range(1, length):
            open_prices[i] = close_prices[i-1] * (1 + np.random.normal(0, volatility * 0.1))

        for i in range(length):
            # 确定当日最高最低价
            day_high = max(open_prices[i], close_prices[i]) * high_factors[i]
            day_low = min(open_prices[i], close_prices[i]) * low_factors[i]

            high_prices[i] = max(day_high, open_prices[i], close_prices[i])
            low_prices[i] = min(day_low, open_prices[i], close_prices[i])

        return {
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices
        }

    def _generate_volume_series(self, length: int) -> np.ndarray:
        """生成成交量序列"""
        params = self._market_params

        # 基础成交量
        base_volume = self.volume_base * params["volume_multiplier"]

        # 成交量波动
        volume_volatility = self.volume_volatility * params["volume_multiplier"]

        # 生成成交量
        volume_factors = np.random.lognormal(0, volume_volatility, length)
        volumes = base_volume * volume_factors

        # 添加周期性模式（模拟交易活跃度）
        time_factor = np.sin(np.arange(length) * 2 * np.pi / 5) * 0.2 + 1  # 5日周期
        volumes *= time_factor

        # 确保成交量为正整数
        volumes = np.maximum(volumes, 1).astype(int)

        return volumes

    def get_available_symbols(self) -> List[str]:
        """获取可用的股票代码列表"""
        return self._symbols.copy()

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        # 模拟股票信息
        symbol_hash = hash(symbol) % 1000

        # 生成模拟的股票信息
        info = {
            'symbol': symbol,
            'name': f'模拟股票{symbol}',
            'exchange': 'MOCK',
            'sector': ['科技', '金融', '消费', '医药', '工业'][symbol_hash % 5],
            'market_cap': (symbol_hash + 1) * 1000000000,  # 市值
            'pe_ratio': 10 + (symbol_hash % 50),  # 市盈率
            'pb_ratio': 1 + (symbol_hash % 10) * 0.5,  # 市净率
            'dividend_yield': (symbol_hash % 10) * 0.01,  # 股息率
            'beta': 0.5 + (symbol_hash % 20) * 0.1,  # Beta值
            'listing_date': '2010-01-01',  # 上市日期
            'description': f'这是一个模拟的股票代码 {symbol}，用于测试和演示。'
        }

        return info

    def set_market_state(self, market_state: str):
        """设置市场状态"""
        if market_state not in ['normal', 'bull', 'bear', 'volatile']:
            raise ValueError(f"不支持的市场状态: {market_state}")

        self.market_state = market_state
        self._market_params = self._get_market_params(market_state)

        # 清空缓存，因为市场状态改变了
        self.clear_cache()

        logger.info(f"市场状态已更新: {market_state}")

    def set_volatility(self, volatility: float):
        """设置波动率"""
        if volatility <= 0:
            raise ValueError("波动率必须大于0")

        self.volatility = volatility
        self.clear_cache()

        logger.info(f"波动率已更新: {volatility}")

    def set_trend(self, trend: float):
        """设置趋势"""
        self.trend = trend
        self.clear_cache()

        logger.info(f"趋势已更新: {trend}")

    def generate_custom_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        custom_params: Dict[str, Any]
    ) -> pd.DataFrame:
        """生成自定义参数的数据

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            custom_params: 自定义参数

        Returns:
            自定义数据DataFrame
        """
        # 保存原始参数
        original_params = {
            'base_price': self.base_price,
            'volatility': self.volatility,
            'trend': self.trend,
            'volume_base': self.volume_base,
            'volume_volatility': self.volume_volatility,
            'market_state': self.market_state
        }

        try:
            # 应用自定义参数
            for key, value in custom_params.items():
                if hasattr(self, key):
                    setattr(self, key, value)

            # 更新市场参数
            if 'market_state' in custom_params:
                self._market_params = self._get_market_params(custom_params['market_state'])

            # 生成数据
            data = self._fetch_data(symbol, start_date, end_date)

            return data

        finally:
            # 恢复原始参数
            for key, value in original_params.items():
                setattr(self, key, value)
            self._market_params = self._get_market_params(self.market_state)
