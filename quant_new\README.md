# 量化回测引擎

基于VectorBT的高性能量化回测系统，支持A股、基金、可转债等多种资产类别。

## 主要特性

- **模块化设计**: 易于扩展和维护
- **高性能计算**: 基于VectorBT的向量化回测
- **多种策略**: 支持单标、多标、动态策略
- **完整风控**: 止盈止损、仓位管理、风险限制
- **丰富数据源**: AkShare、数据库、Mock数据
- **智能缓存**: 多级缓存提升性能
- **可视化报告**: 基于Plotly的交互式图表

## 项目结构

```
quant_new/
├── dataseed/             # 数据源模块
│   ├── base.py           # 抽象数据源接口
│   ├── akshare.py        # AkShare实现
│   ├── database.py       # 数据库实现
│   ├── mock.py           # 模拟数据实现
│   └── adapter.py        # 数据适配器
├── strategies/           # 策略模块
│   ├── base.py           # 策略抽象基类
│   ├── single/           # 单标策略
│   │   ├── macd.py       # MACD策略
│   │   └── rsi.py        # RSI策略
│   ├── multi/            # 多标策略
│   └── dynamic/          # 动态标的策略
├── core/                 # 核心引擎
│   ├── config/           # 配置管理
│   ├── engine/           # 回测引擎
│   ├── optimizer/        # 参数优化
│   ├── report/           # 报告生成
│   ├── risk/             # 风险管理
│   └── data_structures.py # 核心数据结构
├── utils/                # 工具模块
│   ├── cache/            # 缓存系统
│   ├── indicators/       # 技术指标
│   └── logger.py         # 日志系统
├── tests/                # 测试模块
├── templates/            # HTML报告模板
├── main.py               # 主入口
├── requirements.txt      # 依赖
└── README.md             # 文档
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 基本使用

```python
from main import QuantEngine

# 创建引擎
engine = QuantEngine(data_source="mock")

# 运行MACD策略
result = engine.run_strategy(
    strategy_name="macd",
    symbol="000001",
    start_date="2023-01-01",
    end_date="2023-12-31",
    strategy_params={
        'fast_period': 12,
        'slow_period': 26,
        'signal_period': 9
    }
)

print(f"总收益: {result['total_return']:.2%}")
print(f"夏普比率: {result['sharpe_ratio']:.2f}")
```

### 3. 运行演示

```bash
cd quant_new
python main.py
```

## 支持的策略

### 单标策略

1. **MACD策略** (`macd`)
   - 基于MACD指标的趋势跟踪
   - 支持成交量过滤和趋势过滤
   - 可配置参数：快慢线周期、信号线周期等

2. **RSI策略** (`rsi`)
   - 基于RSI指标的均值回归
   - 支持背离检测和多时间框架
   - 可配置参数：RSI周期、超买超卖阈值等

### 策略参数

```python
# 查看策略参数说明
engine = QuantEngine()
params_info = engine.get_strategy_params("macd")
print(params_info['description'])
print(params_info['constraints'])
```

## 数据源

### Mock数据源 (默认)
- 用于测试和演示
- 生成符合真实市场特征的模拟数据
- 支持日线和分钟线数据

### AkShare数据源
```python
engine = QuantEngine(data_source="akshare")
```

### 数据库数据源
```python
from dataseed.database import DatabaseDataSeed

data_source = DatabaseDataSeed({
    'url': 'sqlite:///quant_data.db',
    'table_prefix': 'stock_'
})
```

## 配置系统

### 全局配置
```python
from core.config import config

# 修改缓存设置
config.cache.enabled = True
config.cache.default_ttl = 3600

# 修改日志设置
config.log.level = "DEBUG"
```

### 回测配置
```python
backtest_params = {
    'initial_cash': 100000,
    'commission': 0.0005,
    'slippage': 0.001,
    'max_position_size': 0.2
}
```

## 批量回测

```python
# 获取股票列表
symbols = engine.get_available_symbols()[:10]

# 批量回测
results = engine.batch_test(
    strategy_name="rsi",
    symbols=symbols,
    start_date="2023-01-01",
    end_date="2023-12-31"
)

# 分析结果
for symbol, result in results.items():
    if 'error' not in result:
        print(f"{symbol}: {result['total_return']:.2%}")
```

## 技术指标

系统内置丰富的技术指标：

### 趋势指标
- SMA/EMA (简单/指数移动平均)
- MACD (移动平均收敛发散)
- Bollinger Bands (布林带)
- Parabolic SAR
- Ichimoku Cloud (一目均衡表)

### 动量指标
- RSI (相对强弱指标)
- Stochastic (随机指标)
- Williams %R
- CCI (商品通道指标)
- MFI (资金流量指标)

### 波动率指标
- ATR (平均真实范围)
- Historical Volatility (历史波动率)
- Keltner Channels (肯特纳通道)
- Donchian Channels (唐奇安通道)

## 自定义策略

```python
from strategies.base import BaseStrategy, StrategyConfig
from pydantic import Field

class MyStrategyConfig(StrategyConfig):
    strategy_name: str = Field(default="我的策略")
    my_param: int = Field(default=20, description="自定义参数")

class MyStrategy(BaseStrategy):
    def __init__(self, config: MyStrategyConfig):
        super().__init__(config)
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        # 实现信号生成逻辑
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0  # 0: 无信号, 1: 买入, -1: 卖出
        signals['strength'] = 0.0  # 信号强度 0-1
        signals['price'] = data['close']
        
        # 添加你的策略逻辑
        # ...
        
        return signals
```

## 性能优化

### 缓存系统
```python
from utils.cache.decorators import cached

@cached(ttl=3600)  # 缓存1小时
def expensive_calculation(data):
    # 耗时计算
    return result
```

### 并行计算
```python
# 批量回测自动使用并行处理
results = engine.batch_test(
    strategy_name="macd",
    symbols=large_symbol_list,
    start_date="2023-01-01",
    end_date="2023-12-31"
)
```

## 日志系统

```python
from utils.logger import get_logger

logger = get_logger(__name__)
logger.info("策略运行开始")
logger.warning("数据质量问题")
logger.error("策略执行失败")
```

## 开发指南

### 添加新数据源
1. 继承 `DataSeed` 基类
2. 实现必需的抽象方法
3. 在 `dataseed/__init__.py` 中注册

### 添加新策略
1. 继承 `BaseStrategy` 基类
2. 定义策略配置类
3. 实现 `generate_signals` 方法
4. 在相应模块中注册

### 添加新指标
1. 在 `utils/indicators/` 对应模块中添加函数
2. 遵循统一的函数签名
3. 添加适当的文档和测试

## 测试

```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_strategies.py

# 生成覆盖率报告
pytest --cov=quant_new tests/
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

- 项目地址: https://github.com/your-repo/quant_new
- 文档地址: https://your-docs.com
- 邮箱: <EMAIL>
