"""
风险配置模块

定义风险管理相关的配置。
"""

from typing import Dict, Any, Optional, List
from pydantic import Field, validator

from .base import BaseConfig


class RiskConfig(BaseConfig):
    """风险管理配置"""
    
    config_name: str = Field(default="risk", description="配置名称")
    
    # 基本风险参数
    enabled: bool = Field(default=True, description="是否启用风险管理")
    
    # 资金管理
    max_portfolio_risk: float = Field(default=0.02, description="组合最大风险", ge=0, le=1)
    max_position_size: float = Field(default=0.1, description="单个持仓最大比例", ge=0, le=1)
    max_sector_exposure: float = Field(default=0.3, description="单个行业最大敞口", ge=0, le=1)
    cash_reserve_ratio: float = Field(default=0.05, description="现金保留比例", ge=0, le=1)
    
    # 回撤控制
    max_drawdown: float = Field(default=0.2, description="最大回撤限制", ge=0, le=1)
    daily_loss_limit: float = Field(default=0.05, description="单日最大亏损", ge=0, le=1)
    monthly_loss_limit: float = Field(default=0.15, description="单月最大亏损", ge=0, le=1)
    
    # 止损止盈
    enable_stop_loss: bool = Field(default=True, description="是否启用止损")
    default_stop_loss: float = Field(default=0.05, description="默认止损比例", ge=0, le=1)
    enable_take_profit: bool = Field(default=True, description="是否启用止盈")
    default_take_profit: float = Field(default=0.15, description="默认止盈比例", ge=0)
    
    # 动态止损
    enable_trailing_stop: bool = Field(default=False, description="是否启用移动止损")
    trailing_stop_ratio: float = Field(default=0.03, description="移动止损比例", ge=0, le=1)
    
    # 波动率控制
    max_volatility: Optional[float] = Field(default=None, description="最大波动率限制")
    volatility_window: int = Field(default=20, description="波动率计算窗口", gt=0)
    
    # 相关性控制
    max_correlation: float = Field(default=0.8, description="最大相关性", ge=0, le=1)
    correlation_window: int = Field(default=60, description="相关性计算窗口", gt=0)
    
    # 流动性控制
    min_liquidity_ratio: float = Field(default=0.01, description="最小流动性比例", ge=0, le=1)
    max_volume_ratio: float = Field(default=0.1, description="最大成交量比例", ge=0, le=1)
    
    # 杠杆控制
    max_leverage: float = Field(default=1.0, description="最大杠杆倍数", ge=1.0)
    leverage_buffer: float = Field(default=0.1, description="杠杆缓冲", ge=0, le=1)
    
    # 交易频率控制
    max_trades_per_day: Optional[int] = Field(default=None, description="每日最大交易次数")
    max_turnover_rate: Optional[float] = Field(default=None, description="最大换手率")
    
    # 持仓时间控制
    min_holding_period: Optional[int] = Field(default=None, description="最小持仓天数")
    max_holding_period: Optional[int] = Field(default=None, description="最大持仓天数")
    
    # 风险预算
    risk_budget_enabled: bool = Field(default=False, description="是否启用风险预算")
    risk_budget_allocation: Dict[str, float] = Field(default_factory=dict, description="风险预算分配")
    
    # 压力测试
    stress_test_enabled: bool = Field(default=False, description="是否启用压力测试")
    stress_scenarios: List[Dict[str, Any]] = Field(default_factory=list, description="压力测试场景")
    
    # 风险监控
    real_time_monitoring: bool = Field(default=True, description="是否启用实时监控")
    alert_thresholds: Dict[str, float] = Field(default_factory=dict, description="告警阈值")
    
    # 风险报告
    daily_risk_report: bool = Field(default=True, description="是否生成日度风险报告")
    weekly_risk_report: bool = Field(default=True, description="是否生成周度风险报告")
    monthly_risk_report: bool = Field(default=True, description="是否生成月度风险报告")
    
    def __post_init__(self):
        """初始化后处理"""
        super().__post_init__()
        
        # 设置默认告警阈值
        if not self.alert_thresholds:
            self.alert_thresholds = {
                'drawdown': self.max_drawdown * 0.8,
                'daily_loss': self.daily_loss_limit * 0.8,
                'position_size': self.max_position_size * 0.9,
                'leverage': self.max_leverage * 0.9,
            }
    
    @validator('max_portfolio_risk', 'max_position_size', 'max_sector_exposure')
    def validate_risk_ratios(cls, v):
        """验证风险比例参数"""
        if v < 0 or v > 1:
            raise ValueError("风险比例必须在0-1之间")
        return v
    
    @validator('max_drawdown', 'daily_loss_limit', 'monthly_loss_limit')
    def validate_loss_limits(cls, v):
        """验证亏损限制参数"""
        if v < 0 or v > 1:
            raise ValueError("亏损限制必须在0-1之间")
        return v
    
    def get_position_limit(self, symbol: str, portfolio_value: float) -> float:
        """获取持仓限制"""
        return portfolio_value * self.max_position_size
    
    def get_stop_loss_price(self, entry_price: float, is_long: bool = True) -> float:
        """计算止损价格"""
        if not self.enable_stop_loss:
            return 0.0
        
        if is_long:
            return entry_price * (1 - self.default_stop_loss)
        else:
            return entry_price * (1 + self.default_stop_loss)
    
    def get_take_profit_price(self, entry_price: float, is_long: bool = True) -> float:
        """计算止盈价格"""
        if not self.enable_take_profit:
            return 0.0
        
        if is_long:
            return entry_price * (1 + self.default_take_profit)
        else:
            return entry_price * (1 - self.default_take_profit)
    
    def check_drawdown_limit(self, current_drawdown: float) -> bool:
        """检查回撤限制"""
        return current_drawdown <= self.max_drawdown
    
    def check_daily_loss_limit(self, daily_pnl: float, portfolio_value: float) -> bool:
        """检查单日亏损限制"""
        daily_loss_ratio = abs(daily_pnl) / portfolio_value if portfolio_value > 0 else 0
        return daily_loss_ratio <= self.daily_loss_limit
    
    def check_position_size_limit(self, position_value: float, portfolio_value: float) -> bool:
        """检查持仓大小限制"""
        position_ratio = position_value / portfolio_value if portfolio_value > 0 else 0
        return position_ratio <= self.max_position_size
    
    def check_leverage_limit(self, total_exposure: float, portfolio_value: float) -> bool:
        """检查杠杆限制"""
        leverage = total_exposure / portfolio_value if portfolio_value > 0 else 0
        return leverage <= self.max_leverage
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """获取风险指标配置"""
        return {
            'max_drawdown': self.max_drawdown,
            'max_position_size': self.max_position_size,
            'max_leverage': self.max_leverage,
            'stop_loss_enabled': self.enable_stop_loss,
            'take_profit_enabled': self.enable_take_profit,
            'trailing_stop_enabled': self.enable_trailing_stop,
            'real_time_monitoring': self.real_time_monitoring,
        }
    
    @classmethod
    def create_conservative(cls) -> 'RiskConfig':
        """创建保守型风险配置"""
        return cls(
            config_name="conservative_risk",
            max_portfolio_risk=0.01,
            max_position_size=0.05,
            max_drawdown=0.1,
            daily_loss_limit=0.02,
            default_stop_loss=0.03,
            default_take_profit=0.1,
            max_leverage=1.0,
            enable_trailing_stop=True,
            trailing_stop_ratio=0.02,
        )
    
    @classmethod
    def create_aggressive(cls) -> 'RiskConfig':
        """创建激进型风险配置"""
        return cls(
            config_name="aggressive_risk",
            max_portfolio_risk=0.05,
            max_position_size=0.2,
            max_drawdown=0.3,
            daily_loss_limit=0.1,
            default_stop_loss=0.08,
            default_take_profit=0.25,
            max_leverage=3.0,
            enable_trailing_stop=False,
        )
    
    @classmethod
    def create_default(cls) -> 'RiskConfig':
        """创建默认风险配置"""
        return cls()
