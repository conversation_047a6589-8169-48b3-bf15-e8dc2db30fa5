"""
RSI指标

Relative Strength Index - 相对强弱指标
"""

from typing import Union
import pandas as pd
import numpy as np

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from indicators.base import OscillatorBase


class RSI(OscillatorBase):
    """RSI指标

    相对强弱指标，用于衡量价格变动的速度和幅度。
    """

    def __init__(self, period: int = 14):
        """
        初始化RSI指标

        Args:
            period: 计算周期
        """
        super().__init__(period)

    def _calculate(self, data: Union[pd.Series, pd.DataFrame]) -> pd.Series:
        """
        计算RSI

        Args:
            data: 输入数据

        Returns:
            RSI序列
        """
        if isinstance(data, pd.DataFrame):
            if 'close' not in data.columns:
                raise ValueError("DataFrame必须包含'close'列")
            prices = data['close']
        else:
            prices = data

        # 计算价格变化
        delta = prices.diff()

        # 分离上涨和下跌
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # 计算平均涨幅和跌幅
        avg_gain = gain.rolling(window=self.period, min_periods=1).mean()
        avg_loss = loss.rolling(window=self.period, min_periods=1).mean()

        # 计算RS和RSI
        rs = avg_gain / avg_loss.where(avg_loss != 0, np.inf)
        rsi = 100 - (100 / (1 + rs))

        return rsi
