# 核心框架
vectorbt>=0.25.0
pandas>=1.5.0
numpy>=1.21.0

# 数据源
akshare>=1.9.0
yfinance>=0.2.0

# 配置管理
pydantic>=2.0.0
pydantic-settings>=2.0.0

# 缓存系统
joblib>=1.3.0
diskcache>=5.6.0

# 日志系统
loguru>=0.7.0

# 可视化
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0

# 优化算法
optuna>=3.3.0
scipy>=1.11.0
scikit-learn>=1.3.0

# 数据库支持（可选）
sqlalchemy>=2.0.0
pymongo>=4.5.0

# 测试框架
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# 开发工具
black>=23.7.0
isort>=5.12.0
mypy>=1.5.0
pre-commit>=3.3.0

# 性能优化
numba>=0.57.0
cython>=3.0.0

# 并行计算
dask>=2023.8.0
ray>=2.6.0

# 其他工具
tqdm>=4.66.0
click>=8.1.0
rich>=13.5.0
