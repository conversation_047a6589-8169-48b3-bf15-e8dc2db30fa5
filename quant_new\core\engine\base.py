"""
回测引擎基类

提供统一的回测执行接口和核心功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, date
import pandas as pd
import numpy as np

try:
    import vectorbt as vbt
    VECTORBT_AVAILABLE = True
except ImportError:
    VECTORBT_AVAILABLE = False
    vbt = None

from ..config.backtest import BacktestConfig
from ..config.risk import RiskConfig
from ...dataseed.base import DataSeed
from ...strategies.base import BaseStrategy, StrategyResult
from ...utils.logger import get_logger, log_execution_time

logger = get_logger(__name__)


class BacktestEngine(ABC):
    """回测引擎抽象基类"""
    
    def __init__(
        self,
        data_seed: DataSeed,
        backtest_config: Optional[BacktestConfig] = None,
        risk_config: Optional[RiskConfig] = None
    ):
        """
        初始化回测引擎
        
        Args:
            data_seed: 数据源
            backtest_config: 回测配置
            risk_config: 风险配置
        """
        self.data_seed = data_seed
        self.backtest_config = backtest_config or BacktestConfig(
            start_date="2023-01-01",
            end_date="2023-12-31"
        )
        self.risk_config = risk_config
        
        # 内部状态
        self._results = {}
        self._portfolio_data = {}
        self._performance_stats = {}
        
        logger.info("回测引擎初始化完成")
    
    @abstractmethod
    def run_strategy(
        self,
        strategy: BaseStrategy,
        symbols: Union[str, List[str]],
        **kwargs
    ) -> Union[StrategyResult, Dict[str, StrategyResult]]:
        """
        运行策略回测
        
        Args:
            strategy: 策略实例
            symbols: 股票代码或代码列表
            **kwargs: 其他参数
            
        Returns:
            策略结果
        """
        pass
    
    def get_data(
        self,
        symbols: Union[str, List[str]],
        start_date: Optional[Union[str, date, datetime]] = None,
        end_date: Optional[Union[str, date, datetime]] = None,
        **kwargs
    ) -> Union[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        获取回测数据
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 其他参数
            
        Returns:
            数据DataFrame或字典
        """
        start_date = start_date or self.backtest_config.start_date
        end_date = end_date or self.backtest_config.end_date
        
        if isinstance(symbols, str):
            # 单个股票
            data = self.data_seed.get_daily_data(symbols, start_date, end_date)
            return data
        else:
            # 多个股票
            data_dict = {}
            for symbol in symbols:
                try:
                    data = self.data_seed.get_daily_data(symbol, start_date, end_date)
                    if not data.empty:
                        data_dict[symbol] = data
                    else:
                        logger.warning(f"股票 {symbol} 数据为空")
                except Exception as e:
                    logger.error(f"获取股票 {symbol} 数据失败: {e}")
            
            return data_dict
    
    def validate_data(self, data: Union[pd.DataFrame, Dict[str, pd.DataFrame]]) -> bool:
        """
        验证数据有效性
        
        Args:
            data: 数据
            
        Returns:
            是否有效
        """
        if isinstance(data, pd.DataFrame):
            return self._validate_single_data(data)
        elif isinstance(data, dict):
            return all(self._validate_single_data(df) for df in data.values())
        else:
            return False
    
    def _validate_single_data(self, data: pd.DataFrame) -> bool:
        """验证单个数据DataFrame"""
        if data.empty:
            return False
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            logger.warning(f"缺少必需的列: {missing_columns}")
            return False
        
        # 检查数据质量
        if data.isnull().any().any():
            logger.warning("数据包含空值")
        
        # 检查价格逻辑
        invalid_prices = (
            (data['high'] < data['low']) |
            (data['high'] < data['open']) |
            (data['high'] < data['close']) |
            (data['low'] > data['open']) |
            (data['low'] > data['close'])
        )
        
        if invalid_prices.any():
            logger.warning(f"发现 {invalid_prices.sum()} 条无效价格数据")
        
        return True
    
    @log_execution_time()
    def calculate_performance_metrics(
        self,
        returns: pd.Series,
        benchmark_returns: Optional[pd.Series] = None
    ) -> Dict[str, float]:
        """
        计算绩效指标
        
        Args:
            returns: 策略收益序列
            benchmark_returns: 基准收益序列
            
        Returns:
            绩效指标字典
        """
        if returns.empty:
            return {}
        
        # 基础指标
        total_return = (1 + returns).prod() - 1
        annual_return = (1 + total_return) ** (252 / len(returns)) - 1
        volatility = returns.std() * np.sqrt(252)
        
        # 夏普比率
        risk_free_rate = self.backtest_config.interest_rate
        excess_returns = returns - risk_free_rate / 252
        sharpe_ratio = excess_returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
        
        # 最大回撤
        cumulative_returns = (1 + returns).cumprod()
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / peak
        max_drawdown = drawdown.min()
        
        # Calmar比率
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Sortino比率
        downside_returns = returns[returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = annual_return / downside_deviation if downside_deviation > 0 else 0
        
        # 胜率
        win_rate = (returns > 0).mean()
        
        # VaR和CVaR
        var_95 = returns.quantile(0.05)
        cvar_95 = returns[returns <= var_95].mean()
        
        metrics = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'calmar_ratio': calmar_ratio,
            'sortino_ratio': sortino_ratio,
            'win_rate': win_rate,
            'var_95': var_95,
            'cvar_95': cvar_95
        }
        
        # 基准相关指标
        if benchmark_returns is not None and not benchmark_returns.empty:
            # 对齐数据
            aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
            
            if len(aligned_returns) > 0:
                # 贝塔系数
                covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
                benchmark_variance = np.var(aligned_benchmark)
                beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
                
                # 阿尔法
                benchmark_annual_return = (1 + aligned_benchmark).prod() ** (252 / len(aligned_benchmark)) - 1
                alpha = annual_return - (risk_free_rate + beta * (benchmark_annual_return - risk_free_rate))
                
                # 信息比率
                active_returns = aligned_returns - aligned_benchmark
                tracking_error = active_returns.std() * np.sqrt(252)
                information_ratio = active_returns.mean() / active_returns.std() * np.sqrt(252) if active_returns.std() > 0 else 0
                
                # 相关系数
                correlation = aligned_returns.corr(aligned_benchmark)
                
                metrics.update({
                    'beta': beta,
                    'alpha': alpha,
                    'tracking_error': tracking_error,
                    'information_ratio': information_ratio,
                    'correlation': correlation
                })
        
        return metrics
    
    def calculate_trade_statistics(self, signals: pd.DataFrame) -> Dict[str, Any]:
        """
        计算交易统计
        
        Args:
            signals: 信号DataFrame
            
        Returns:
            交易统计字典
        """
        if signals.empty or 'signal' not in signals.columns:
            return {}
        
        # 提取交易信号
        trades = signals[signals['signal'] != 0].copy()
        
        if trades.empty:
            return {'total_trades': 0}
        
        # 基础统计
        total_trades = len(trades)
        buy_trades = (trades['signal'] > 0).sum()
        sell_trades = (trades['signal'] < 0).sum()
        
        # 持仓时间统计
        if len(trades) > 1:
            trade_intervals = trades.index.to_series().diff().dt.days
            avg_holding_period = trade_intervals.mean()
            max_holding_period = trade_intervals.max()
            min_holding_period = trade_intervals.min()
        else:
            avg_holding_period = max_holding_period = min_holding_period = 0
        
        # 信号强度统计
        if 'strength' in trades.columns:
            avg_signal_strength = trades['strength'].mean()
            max_signal_strength = trades['strength'].max()
            min_signal_strength = trades['strength'].min()
        else:
            avg_signal_strength = max_signal_strength = min_signal_strength = 0
        
        return {
            'total_trades': total_trades,
            'buy_trades': buy_trades,
            'sell_trades': sell_trades,
            'avg_holding_period': avg_holding_period,
            'max_holding_period': max_holding_period,
            'min_holding_period': min_holding_period,
            'avg_signal_strength': avg_signal_strength,
            'max_signal_strength': max_signal_strength,
            'min_signal_strength': min_signal_strength
        }
    
    def get_results(self) -> Dict[str, Any]:
        """获取回测结果"""
        return self._results.copy()
    
    def get_portfolio_data(self) -> Dict[str, Any]:
        """获取投资组合数据"""
        return self._portfolio_data.copy()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取绩效统计"""
        return self._performance_stats.copy()
    
    def clear_results(self):
        """清空结果"""
        self._results.clear()
        self._portfolio_data.clear()
        self._performance_stats.clear()
        logger.info("回测结果已清空")


__all__ = [
    "BacktestEngine"
]
