"""
报告生成模块

生成回测报告的JSON数据，用于与HTML模板结合展示。
"""

import json
import os
import pandas as pd
import numpy as np
import vectorbt as vbt
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from core.config import ConfigManager


class ReportGenerator:
    """报告生成器，生成JSON格式的回测报告数据"""

    def __init__(self, portfolio: vbt.Portfolio, symbol: str, strategy_name: str, 
                 config: Optional[ConfigManager] = None):
        """
        初始化报告生成器

        参数:
            portfolio (vbt.Portfolio): 回测结果
            symbol (str): 证券代码
            strategy_name (str): 策略名称
            config (ConfigManager): 配置管理器
        """
        self.portfolio = portfolio
        self.symbol = symbol
        self.strategy_name = strategy_name
        self.config = config or ConfigManager()
        self.logger = logging.getLogger('quant.report')

    def generate_report_data(self) -> Dict[str, Any]:
        """
        生成报告数据

        返回:
            dict: 包含报告数据的字典
        """
        self.logger.info("开始生成回测报告数据...")

        # 计算绩效指标
        metrics = self._calculate_metrics()

        # 获取交易记录
        trades = self._get_trades()

        # 生成图表数据
        chart_data = self._generate_chart_data()

        # 组装报告数据
        report_data = {
            'symbol': self.symbol,
            'strategy': self.strategy_name,
            'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'metrics': metrics,
            'trades': trades,
            'chart_data': chart_data,
            'summary': self._generate_summary(metrics)
        }

        self.logger.info("报告数据生成完成")
        return report_data

    def save_report_json(self, output_dir: str, filename: Optional[str] = None) -> str:
        """
        保存报告数据为JSON文件

        参数:
            output_dir (str): 输出目录
            filename (str): 文件名，默认自动生成

        返回:
            str: 保存的文件路径
        """
        # 生成报告数据
        report_data = self.generate_report_data()

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件名
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_strategy_name = self.strategy_name.replace(' ', '_').replace('/', '_')
            safe_symbol = self.symbol.replace('.', '_')
            filename = f"{safe_strategy_name}_{safe_symbol}_{timestamp}.json"

        # 保存文件
        file_path = os.path.join(output_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)

        self.logger.info(f"报告数据已保存至: {file_path}")
        return file_path

    def _calculate_metrics(self) -> Dict[str, Any]:
        """计算绩效指标"""
        metrics = {}

        try:
            # 基本收益指标
            metrics['total_return'] = float(self.portfolio.total_return())
            metrics['annual_return'] = float(self.portfolio.annualized_return())
            metrics['max_drawdown'] = float(self.portfolio.max_drawdown())
            
            # 风险调整收益指标
            metrics['sharpe_ratio'] = float(self.portfolio.sharpe_ratio())
            
            # 交易统计
            trades = self.portfolio.trades
            metrics['total_trades'] = int(trades.count())
            
            if trades.count() > 0:
                metrics['win_rate'] = float(trades.win_rate())
                metrics['avg_trade_return'] = float(trades.returns.mean())
                metrics['best_trade'] = float(trades.returns.max())
                metrics['worst_trade'] = float(trades.returns.min())
                metrics['avg_trade_duration'] = float(trades.duration.mean())
            else:
                metrics['win_rate'] = 0.0
                metrics['avg_trade_return'] = 0.0
                metrics['best_trade'] = 0.0
                metrics['worst_trade'] = 0.0
                metrics['avg_trade_duration'] = 0.0

            # 风险指标
            returns = self.portfolio.returns()
            if len(returns) > 0:
                metrics['volatility'] = float(returns.std() * np.sqrt(252))
                metrics['var_95'] = float(np.percentile(returns, 5))
                metrics['var_99'] = float(np.percentile(returns, 1))
                metrics['skewness'] = float(returns.skew())
                metrics['kurtosis'] = float(returns.kurtosis())
            else:
                metrics['volatility'] = 0.0
                metrics['var_95'] = 0.0
                metrics['var_99'] = 0.0
                metrics['skewness'] = 0.0
                metrics['kurtosis'] = 0.0

            # 卡尔玛比率
            if metrics['max_drawdown'] != 0:
                metrics['calmar_ratio'] = metrics['annual_return'] / abs(metrics['max_drawdown'])
            else:
                metrics['calmar_ratio'] = 0.0

            # 索提诺比率
            downside_returns = returns[returns < 0]
            if len(downside_returns) > 0:
                downside_deviation = downside_returns.std() * np.sqrt(252)
                metrics['sortino_ratio'] = metrics['annual_return'] / downside_deviation
            else:
                metrics['sortino_ratio'] = 0.0

        except Exception as e:
            self.logger.error(f"计算指标时出错: {str(e)}")
            # 返回默认值
            metrics = {
                'total_return': 0.0, 'annual_return': 0.0, 'max_drawdown': 0.0,
                'sharpe_ratio': 0.0, 'total_trades': 0, 'win_rate': 0.0,
                'avg_trade_return': 0.0, 'best_trade': 0.0, 'worst_trade': 0.0,
                'avg_trade_duration': 0.0, 'volatility': 0.0, 'var_95': 0.0,
                'var_99': 0.0, 'skewness': 0.0, 'kurtosis': 0.0,
                'calmar_ratio': 0.0, 'sortino_ratio': 0.0
            }

        return metrics

    def _get_trades(self) -> List[Dict[str, Any]]:
        """获取交易记录"""
        trades_list = []

        try:
            trades = self.portfolio.trades
            if trades.count() > 0:
                trades_df = trades.records_readable

                # 检查字段是否存在
                has_duration = 'Duration' in trades_df.columns
                self.logger.debug(f"交易记录字段: {trades_df.columns.tolist()}")

                for _, trade in trades_df.iterrows():
                    try:
                        # 计算持续时间(如果字段不存在则使用退出-进入时间计算)
                        if has_duration and pd.notna(trade['Duration']):
                            duration_days = int(trade['Duration'].total_seconds() / 86400)
                        elif 'Exit Timestamp' in trades_df.columns and 'Entry Timestamp' in trades_df.columns:
                            if pd.notna(trade['Exit Timestamp']) and pd.notna(trade['Entry Timestamp']):
                                duration_days = (trade['Exit Timestamp'] - trade['Entry Timestamp']).days
                            else:
                                duration_days = 0
                        else:
                            duration_days = 0

                        trade_dict = {
                            'entry_date': trade['Entry Timestamp'].strftime('%Y-%m-%d') if 'Entry Timestamp' in trades_df.columns and pd.notna(trade['Entry Timestamp']) else '',
                            'exit_date': trade['Exit Timestamp'].strftime('%Y-%m-%d') if 'Exit Timestamp' in trades_df.columns and pd.notna(trade['Exit Timestamp']) else '',
                            'entry_price': float(trade['Avg Entry Price']) if 'Avg Entry Price' in trades_df.columns and pd.notna(trade['Avg Entry Price']) else 0.0,
                            'exit_price': float(trade['Avg Exit Price']) if 'Avg Exit Price' in trades_df.columns and pd.notna(trade['Avg Exit Price']) else 0.0,
                            'size': float(trade['Size']) if 'Size' in trades_df.columns and pd.notna(trade['Size']) else 0.0,
                            'pnl': float(trade['PnL']) if 'PnL' in trades_df.columns and pd.notna(trade['PnL']) else 0.0,
                            'return_pct': float(trade['Return']) if 'Return' in trades_df.columns and pd.notna(trade['Return']) else 0.0,
                            'duration': duration_days
                        }
                        trades_list.append(trade_dict)
                    except Exception as trade_e:
                        self.logger.error(f"处理单笔交易时出错: {str(trade_e)}", exc_info=True)
                        continue

        except Exception as e:
            self.logger.error(f"获取交易记录时出错: {str(e)}", exc_info=True)

        return trades_list

    def _generate_chart_data(self) -> Dict[str, Any]:
        """生成图表数据"""
        chart_data = {}

        try:
            # 净值曲线数据
            portfolio_value = self.portfolio.value()
            chart_data['portfolio_value'] = {
                'dates': [date.strftime('%Y-%m-%d') for date in portfolio_value.index],
                'values': portfolio_value.tolist()
            }

            # 回撤曲线数据
            drawdown = self.portfolio.drawdown()
            chart_data['drawdown'] = {
                'dates': [date.strftime('%Y-%m-%d') for date in drawdown.index],
                'values': drawdown.tolist()
            }

            # 收益分布数据
            returns = self.portfolio.returns()
            if len(returns) > 0:
                chart_data['returns_distribution'] = {
                    'returns': returns.tolist(),
                    'bins': 50
                }

            # 月度收益数据
            monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
            chart_data['monthly_returns'] = {
                'dates': [date.strftime('%Y-%m') for date in monthly_returns.index],
                'returns': monthly_returns.tolist()
            }

        except Exception as e:
            self.logger.error(f"生成图表数据时出错: {str(e)}")
            chart_data = {}

        return chart_data

    def _generate_summary(self, metrics: Dict[str, Any]) -> Dict[str, str]:
        """生成报告摘要"""
        summary = {}

        try:
            # 总体表现
            total_return = metrics.get('total_return', 0)
            if total_return > 0:
                summary['performance'] = f"策略获得了 {total_return:.2%} 的总收益"
            else:
                summary['performance'] = f"策略产生了 {abs(total_return):.2%} 的总亏损"

            # 风险水平
            max_drawdown = metrics.get('max_drawdown', 0)
            if abs(max_drawdown) < 0.1:
                summary['risk'] = "低风险"
            elif abs(max_drawdown) < 0.2:
                summary['risk'] = "中等风险"
            else:
                summary['risk'] = "高风险"

            # 交易频率
            total_trades = metrics.get('total_trades', 0)
            if total_trades < 10:
                summary['frequency'] = "低频交易"
            elif total_trades < 50:
                summary['frequency'] = "中频交易"
            else:
                summary['frequency'] = "高频交易"

            # 胜率评价
            win_rate = metrics.get('win_rate', 0)
            if win_rate > 0.6:
                summary['win_rate_level'] = "高胜率"
            elif win_rate > 0.4:
                summary['win_rate_level'] = "中等胜率"
            else:
                summary['win_rate_level'] = "低胜率"

        except Exception as e:
            self.logger.error(f"生成摘要时出错: {str(e)}")
            summary = {
                'performance': "无法计算",
                'risk': "无法评估",
                'frequency': "无法确定",
                'win_rate_level': "无法评估"
            }

        return summary
