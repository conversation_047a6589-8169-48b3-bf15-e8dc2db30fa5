"""
动量指标

实现各种动量分析技术指标。
"""

from typing import Tuple
import pandas as pd
import numpy as np

from ..logger import get_logger

logger = get_logger(__name__)


def rsi(data: pd.Series, period: int = 14) -> pd.Series:
    """
    相对强弱指标 (Relative Strength Index)
    
    Args:
        data: 价格序列
        period: 计算周期
        
    Returns:
        RSI序列
    """
    if len(data) < period + 1:
        logger.warning(f"数据长度不足: {len(data)} < {period + 1}")
        return pd.Series(index=data.index, dtype=float)
    
    # 计算价格变化
    delta = data.diff()
    
    # 分离上涨和下跌
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    # 计算平均收益和平均损失
    avg_gain = gain.rolling(window=period, min_periods=period).mean()
    avg_loss = loss.rolling(window=period, min_periods=period).mean()
    
    # 计算RS和RSI
    rs = avg_gain / avg_loss
    rsi_values = 100 - (100 / (1 + rs))
    
    return rsi_values


def stochastic(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    k_period: int = 14, 
    d_period: int = 3
) -> Tuple[pd.Series, pd.Series]:
    """
    随机指标 (Stochastic Oscillator)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        k_period: %K周期
        d_period: %D周期
        
    Returns:
        (%K, %D)
    """
    if len(high) < k_period:
        logger.warning(f"数据长度不足: {len(high)} < {k_period}")
        empty_series = pd.Series(index=high.index, dtype=float)
        return empty_series, empty_series
    
    # 计算最高价和最低价的滚动窗口
    highest_high = high.rolling(window=k_period, min_periods=k_period).max()
    lowest_low = low.rolling(window=k_period, min_periods=k_period).min()
    
    # 计算%K
    k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
    
    # 计算%D（%K的移动平均）
    d_percent = k_percent.rolling(window=d_period, min_periods=d_period).mean()
    
    return k_percent, d_percent


def williams_r(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    period: int = 14
) -> pd.Series:
    """
    威廉指标 (Williams %R)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: 计算周期
        
    Returns:
        Williams %R序列
    """
    if len(high) < period:
        logger.warning(f"数据长度不足: {len(high)} < {period}")
        return pd.Series(index=high.index, dtype=float)
    
    # 计算最高价和最低价的滚动窗口
    highest_high = high.rolling(window=period, min_periods=period).max()
    lowest_low = low.rolling(window=period, min_periods=period).min()
    
    # 计算Williams %R
    williams_r_values = -100 * (highest_high - close) / (highest_high - lowest_low)
    
    return williams_r_values


def momentum(data: pd.Series, period: int = 10) -> pd.Series:
    """
    动量指标 (Momentum)
    
    Args:
        data: 价格序列
        period: 计算周期
        
    Returns:
        动量序列
    """
    if len(data) < period + 1:
        logger.warning(f"数据长度不足: {len(data)} < {period + 1}")
        return pd.Series(index=data.index, dtype=float)
    
    return data - data.shift(period)


def rate_of_change(data: pd.Series, period: int = 10) -> pd.Series:
    """
    变化率指标 (Rate of Change)
    
    Args:
        data: 价格序列
        period: 计算周期
        
    Returns:
        变化率序列
    """
    if len(data) < period + 1:
        logger.warning(f"数据长度不足: {len(data)} < {period + 1}")
        return pd.Series(index=data.index, dtype=float)
    
    return ((data - data.shift(period)) / data.shift(period)) * 100


def commodity_channel_index(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    period: int = 20
) -> pd.Series:
    """
    商品通道指标 (Commodity Channel Index)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: 计算周期
        
    Returns:
        CCI序列
    """
    if len(high) < period:
        logger.warning(f"数据长度不足: {len(high)} < {period}")
        return pd.Series(index=high.index, dtype=float)
    
    # 计算典型价格
    typical_price = (high + low + close) / 3
    
    # 计算典型价格的移动平均
    sma_tp = typical_price.rolling(window=period, min_periods=period).mean()
    
    # 计算平均绝对偏差
    mad = typical_price.rolling(window=period, min_periods=period).apply(
        lambda x: np.mean(np.abs(x - x.mean())), raw=True
    )
    
    # 计算CCI
    cci = (typical_price - sma_tp) / (0.015 * mad)
    
    return cci


def money_flow_index(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    volume: pd.Series, 
    period: int = 14
) -> pd.Series:
    """
    资金流量指标 (Money Flow Index)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        volume: 成交量序列
        period: 计算周期
        
    Returns:
        MFI序列
    """
    if len(high) < period + 1:
        logger.warning(f"数据长度不足: {len(high)} < {period + 1}")
        return pd.Series(index=high.index, dtype=float)
    
    # 计算典型价格
    typical_price = (high + low + close) / 3
    
    # 计算资金流量
    money_flow = typical_price * volume
    
    # 计算价格变化
    price_change = typical_price.diff()
    
    # 分离正负资金流量
    positive_flow = money_flow.where(price_change > 0, 0)
    negative_flow = money_flow.where(price_change < 0, 0)
    
    # 计算资金流量比率
    positive_mf = positive_flow.rolling(window=period, min_periods=period).sum()
    negative_mf = negative_flow.rolling(window=period, min_periods=period).sum()
    
    money_ratio = positive_mf / negative_mf
    
    # 计算MFI
    mfi = 100 - (100 / (1 + money_ratio))
    
    return mfi


def awesome_oscillator(high: pd.Series, low: pd.Series) -> pd.Series:
    """
    动量震荡指标 (Awesome Oscillator)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        
    Returns:
        AO序列
    """
    # 计算中点价格
    midpoint = (high + low) / 2
    
    # 计算5期和34期简单移动平均
    sma5 = midpoint.rolling(window=5, min_periods=5).mean()
    sma34 = midpoint.rolling(window=34, min_periods=34).mean()
    
    # AO = SMA5 - SMA34
    ao = sma5 - sma34
    
    return ao


def ultimate_oscillator(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series,
    period1: int = 7,
    period2: int = 14,
    period3: int = 28
) -> pd.Series:
    """
    终极震荡指标 (Ultimate Oscillator)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period1: 短期周期
        period2: 中期周期
        period3: 长期周期
        
    Returns:
        UO序列
    """
    if len(high) < period3 + 1:
        logger.warning(f"数据长度不足: {len(high)} < {period3 + 1}")
        return pd.Series(index=high.index, dtype=float)
    
    # 计算买压 (Buying Pressure)
    prev_close = close.shift(1)
    bp = close - np.minimum(low, prev_close)
    
    # 计算真实范围 (True Range)
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算三个周期的平均值
    avg1 = bp.rolling(window=period1).sum() / tr.rolling(window=period1).sum()
    avg2 = bp.rolling(window=period2).sum() / tr.rolling(window=period2).sum()
    avg3 = bp.rolling(window=period3).sum() / tr.rolling(window=period3).sum()
    
    # 计算终极震荡指标
    uo = 100 * (4 * avg1 + 2 * avg2 + avg3) / 7
    
    return uo


__all__ = [
    "rsi",
    "stochastic",
    "williams_r",
    "momentum",
    "rate_of_change",
    "commodity_channel_index",
    "money_flow_index",
    "awesome_oscillator",
    "ultimate_oscillator"
]
