"""
配置管理模块

提供全局配置管理功能，支持从文件加载和动态修改配置。
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器

        参数:
            config_file (str): 配置文件路径，默认为None
        """
        self.config_file = config_file
        self._config = {}
        self._init_config()

    def _init_config(self):
        """初始化默认配置"""
        self._config = {
            # 数据源配置
            "dataseed": {
                "akshare": {
                    "retry_times": 3,
                    "timeout": 30
                },
                "database": {
                    "connection_string": "sqlite:///quant_data.db"
                },
                "cache": {
                    "enabled": True,
                    "max_age_hours": 24
                }
            },

            # 回测配置
            "backtest": {
                "default_initial_capital": 100000.0,
                "default_commission": 0.0003,
                "default_slippage": 0.001,
                "default_position_size": 1.0
            },

            # 策略配置
            "strategies": {
                "default_params": {
                    "ma_cross": {
                        "fast_window": 5,
                        "slow_window": 20
                    },
                    "rsi": {
                        "window": 14,
                        "overbought": 70,
                        "oversold": 30
                    }
                }
            },

            # 风险管理配置
            "risk": {
                "max_drawdown": 0.2,  # 最大回撤20%
                "max_position_size": 1.0,  # 最大仓位100%
                "stop_loss": 0.1,  # 止损10%
                "take_profit": 0.2  # 止盈20%
            },

            # 日志配置
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_enabled": True,
                "file_path": "./logs"
            }
        }

        # 尝试加载用户自定义配置
        self._load_user_config()

    def _load_user_config(self):
        """加载用户自定义配置"""
        if self.config_file and os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self._merge_config(user_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")

        # 尝试加载默认配置文件
        default_config_file = Path.cwd() / 'config.json'
        if default_config_file.exists():
            try:
                with open(default_config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self._merge_config(user_config)
            except Exception as e:
                print(f"加载默认配置文件失败: {e}")

    def _merge_config(self, user_config: Dict[str, Any]):
        """
        合并用户配置到默认配置

        参数:
            user_config (dict): 用户配置
        """
        def merge_dict(base_dict: dict, update_dict: dict):
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    merge_dict(base_dict[key], value)
                else:
                    base_dict[key] = value

        merge_dict(self._config, user_config)

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值

        参数:
            key (str): 配置键，支持点号分隔的嵌套键，如 'dataseed.akshare.timeout'
            default (Any): 默认值

        返回:
            Any: 配置值
        """
        keys = key.split('.')
        value = self._config

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key: str, value: Any):
        """
        设置配置值

        参数:
            key (str): 配置键，支持点号分隔的嵌套键
            value (Any): 配置值
        """
        keys = key.split('.')
        config = self._config

        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        # 设置值
        config[keys[-1]] = value

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置段

        参数:
            section (str): 配置段名称

        返回:
            dict: 配置段内容
        """
        return self._config.get(section, {})

    def save(self, file_path: Optional[str] = None):
        """
        保存配置到文件

        参数:
            file_path (str): 保存路径，默认使用初始化时的配置文件路径
        """
        save_path = file_path or self.config_file or 'config.json'

        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def reload(self):
        """重新加载配置"""
        self._init_config()

    def get_all(self) -> Dict[str, Any]:
        """
        获取所有配置

        返回:
            dict: 完整配置字典
        """
        return self._config.copy()

    def update(self, config_dict: Dict[str, Any]):
        """
        批量更新配置

        参数:
            config_dict (dict): 配置字典
        """
        self._merge_config(config_dict)
