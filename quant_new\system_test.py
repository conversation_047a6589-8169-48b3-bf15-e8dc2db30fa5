"""
量化回测引擎系统测试

完整测试系统的各个模块功能。
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

print(f"系统测试开始...")
print(f"当前目录: {current_dir}")

def test_data_structures():
    """测试核心数据结构"""
    print("\n=== 测试核心数据结构 ===")
    
    try:
        from core.data_structures import OHLCV, Order, Position, OrderSide, PositionSide
        
        # 测试OHLCV
        ohlcv = OHLCV(
            open=100.0,
            high=105.0,
            low=98.0,
            close=103.0,
            volume=1000000,
            timestamp=datetime.now()
        )
        
        print(f"OHLCV创建成功: 收盘价={ohlcv.close}, 涨跌幅={ohlcv.change_ratio:.2%}")
        
        # 测试Order
        order = Order(
            order_id="test_001",
            symbol="000001",
            side=OrderSide.BUY,
            price=100.0,
            quantity=1000,
            timestamp=datetime.now()
        )
        
        print(f"订单创建成功: {order.side} {order.quantity}股 @{order.price}")
        
        # 测试Position
        position = Position(
            symbol="000001",
            side=PositionSide.LONG,
            quantity=1000,
            avg_price=100.0,
            timestamp=datetime.now()
        )
        
        position.update_market_price(105.0)
        print(f"持仓创建成功: 未实现盈亏={position.unrealized_pnl}")
        
        print("✓ 核心数据结构测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 核心数据结构测试失败: {e}")
        return False


def test_indicators():
    """测试技术指标"""
    print("\n=== 测试技术指标 ===")
    
    try:
        from utils.indicators.trend import sma, ema, macd
        from utils.indicators.momentum import rsi
        from utils.indicators.volatility import atr
        
        # 生成测试数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(100) * 0.02)
        
        price_series = pd.Series(prices, index=dates)
        
        # 测试SMA
        sma_20 = sma(price_series, 20)
        print(f"SMA(20)计算成功: 最新值={sma_20.iloc[-1]:.2f}")
        
        # 测试EMA
        ema_12 = ema(price_series, 12)
        print(f"EMA(12)计算成功: 最新值={ema_12.iloc[-1]:.2f}")
        
        # 测试MACD
        macd_line, signal_line, histogram = macd(price_series)
        print(f"MACD计算成功: MACD={macd_line.iloc[-1]:.3f}, Signal={signal_line.iloc[-1]:.3f}")
        
        # 测试RSI
        rsi_values = rsi(price_series)
        print(f"RSI计算成功: 最新值={rsi_values.iloc[-1]:.2f}")
        
        # 测试ATR（需要高低价数据）
        high_prices = prices * (1 + np.random.uniform(0, 0.02, 100))
        low_prices = prices * (1 - np.random.uniform(0, 0.02, 100))
        
        high_series = pd.Series(high_prices, index=dates)
        low_series = pd.Series(low_prices, index=dates)
        
        atr_values = atr(high_series, low_series, price_series)
        print(f"ATR计算成功: 最新值={atr_values.iloc[-1]:.3f}")
        
        print("✓ 技术指标测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 技术指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_mock_data_source():
    """测试Mock数据源"""
    print("\n=== 测试Mock数据源 ===")
    
    try:
        from dataseed.mock import MockDataSeed
        
        # 创建数据源
        data_source = MockDataSeed()
        
        # 测试日线数据
        data = data_source.get_daily_data("000001", "2023-01-01", "2023-12-31")
        print(f"日线数据获取成功: {len(data)} 条记录")
        print(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
        
        # 测试股票列表
        symbols = data_source.get_universe()
        print(f"股票列表获取成功: {len(symbols)} 只股票")
        
        # 测试基本信息
        info = data_source.get_basic_info("000001")
        print(f"基本信息获取成功: {info.get('name', 'N/A')}")
        
        print("✓ Mock数据源测试通过")
        return True
        
    except Exception as e:
        print(f"✗ Mock数据源测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_strategies():
    """测试策略"""
    print("\n=== 测试策略 ===")
    
    try:
        from strategies.single.macd import MACDStrategy, MACDConfig
        from strategies.single.rsi import RSIStrategy, RSIConfig
        from dataseed.mock import MockDataSeed
        from core.config.backtest import BacktestConfig
        
        # 获取测试数据
        data_source = MockDataSeed()
        data = data_source.get_daily_data("000001", "2023-01-01", "2023-12-31")
        
        # 测试MACD策略
        macd_config = MACDConfig(
            fast_period=12,
            slow_period=26,
            signal_period=9
        )
        macd_strategy = MACDStrategy(macd_config)
        
        backtest_config = BacktestConfig(
            start_date="2023-01-01",
            end_date="2023-12-31",
            initial_cash=100000
        )
        
        macd_result = macd_strategy.run(data, backtest_config)
        print(f"MACD策略测试成功: 收益={macd_result.total_return:.2%}, 夏普={macd_result.sharpe_ratio:.2f}")
        
        # 测试RSI策略
        rsi_config = RSIConfig(
            rsi_period=14,
            oversold_threshold=30,
            overbought_threshold=70
        )
        rsi_strategy = RSIStrategy(rsi_config)
        
        rsi_result = rsi_strategy.run(data, backtest_config)
        print(f"RSI策略测试成功: 收益={rsi_result.total_return:.2%}, 夏普={rsi_result.sharpe_ratio:.2f}")
        
        print("✓ 策略测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backtest_engine():
    """测试回测引擎"""
    print("\n=== 测试回测引擎 ===")
    
    try:
        from core.engine.single import SingleAssetEngine
        from core.engine.multi import MultiAssetEngine
        from dataseed.mock import MockDataSeed
        from strategies.single.macd import MACDStrategy, MACDConfig
        from core.config.backtest import BacktestConfig
        
        # 创建数据源和配置
        data_source = MockDataSeed()
        backtest_config = BacktestConfig(
            start_date="2023-01-01",
            end_date="2023-12-31",
            initial_cash=100000
        )
        
        # 测试单资产引擎
        single_engine = SingleAssetEngine(data_source, backtest_config)
        
        macd_config = MACDConfig()
        macd_strategy = MACDStrategy(macd_config)
        
        single_result = single_engine.run_strategy(macd_strategy, "000001")
        print(f"单资产引擎测试成功: 收益={single_result.total_return:.2%}")
        
        # 测试多资产引擎
        multi_engine = MultiAssetEngine(data_source, backtest_config, max_workers=2)
        
        symbols = ["000001", "000002", "600000"]
        multi_results = multi_engine.run_strategy(macd_strategy, symbols, parallel=False)
        
        print(f"多资产引擎测试成功: {len(multi_results)} 个结果")
        for symbol, result in multi_results.items():
            print(f"  {symbol}: 收益={result.total_return:.2%}")
        
        print("✓ 回测引擎测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 回测引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cache_system():
    """测试缓存系统"""
    print("\n=== 测试缓存系统 ===")
    
    try:
        from utils.cache.manager import CacheManager
        from utils.cache.decorators import cached
        
        # 测试缓存管理器
        cache_manager = CacheManager()
        
        # 测试基本缓存操作
        cache_manager.set("test_key", "test_value", ttl=60)
        value = cache_manager.get("test_key")
        print(f"缓存基本操作成功: {value}")
        
        # 测试缓存装饰器
        @cached(ttl=60)
        def expensive_function(x):
            return x * x
        
        result1 = expensive_function(10)
        result2 = expensive_function(10)  # 应该从缓存获取
        print(f"缓存装饰器测试成功: {result1} == {result2}")
        
        # 测试缓存统计
        stats = cache_manager.get_stats()
        print(f"缓存统计: 命中率={stats.get('hit_rate', 0):.2%}")
        
        print("✓ 缓存系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 缓存系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_risk_management():
    """测试风险管理"""
    print("\n=== 测试风险管理 ===")
    
    try:
        from core.risk.manager import RiskManager
        from core.config.risk import RiskConfig
        
        # 创建风险配置
        risk_config = RiskConfig()
        risk_config.limits.max_drawdown_limit = 0.1
        risk_config.limits.daily_loss_limit = 0.05
        
        # 创建风险管理器
        risk_manager = RiskManager(risk_config)
        
        # 测试风险报告
        report = risk_manager.get_risk_report()
        print(f"风险管理器创建成功: {report['active_rules']} 个规则")
        
        # 测试紧急停止检查
        emergency = risk_manager.check_emergency_conditions(80000, 100000)  # 20%损失
        print(f"紧急停止检查: {emergency}")
        
        print("✓ 风险管理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 风险管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parameter_optimization():
    """测试参数优化"""
    print("\n=== 测试参数优化 ===")
    
    try:
        from core.optimizer.grid import GridSearchOptimizer
        from strategies.single.macd import MACDStrategy, MACDConfig
        from dataseed.mock import MockDataSeed
        from core.config.backtest import BacktestConfig
        
        # 获取测试数据
        data_source = MockDataSeed()
        data = data_source.get_daily_data("000001", "2023-01-01", "2023-12-31")
        
        # 定义参数空间
        param_space = {
            'fast_period': [8, 12, 16],
            'slow_period': [20, 26, 32],
            'signal_period': [6, 9, 12]
        }
        
        # 创建优化器
        optimizer = GridSearchOptimizer(
            strategy_class=MACDStrategy,
            param_space=param_space,
            objective='sharpe_ratio',
            direction='maximize'
        )
        
        # 运行优化（小规模测试）
        backtest_config = BacktestConfig(
            start_date="2023-01-01",
            end_date="2023-12-31"
        )
        
        # 只测试前几个组合
        optimizer.param_combinations = optimizer.param_combinations[:3]
        
        result = optimizer.optimize(data, backtest_config)
        
        print(f"参数优化测试成功: 最佳夏普比率={result['best_score']:.3f}")
        print(f"最佳参数: {result['best_params']}")
        
        print("✓ 参数优化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 参数优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_comprehensive_test():
    """运行综合测试"""
    print("\n" + "="*60)
    print("量化回测引擎 - 综合系统测试")
    print("="*60)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("核心数据结构", test_data_structures),
        ("技术指标", test_indicators),
        ("Mock数据源", test_mock_data_source),
        ("策略模块", test_strategies),
        ("回测引擎", test_backtest_engine),
        ("缓存系统", test_cache_system),
        ("风险管理", test_risk_management),
        ("参数优化", test_parameter_optimization),
    ]
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            test_results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total:.1%})")
    
    if passed == total:
        print("\n🎉 所有测试通过！量化回测引擎系统运行正常。")
    else:
        print(f"\n⚠️  有 {total-passed} 个测试失败，请检查相关模块。")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = run_comprehensive_test()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 系统测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
