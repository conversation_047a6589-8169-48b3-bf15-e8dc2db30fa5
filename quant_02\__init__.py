"""
Quant_02 - 下一代高性能量化回测引擎

基于quant_01优化重构的量化回测系统，采用更清晰的架构设计，
专注于高性能、可扩展性、可维护性和易用性。

主要特性：
- 模块化设计，高度解耦
- 插件化系统，支持动态加载
- 高性能向量化计算
- 智能缓存和内存优化
- 完整的风险管理
- 专业的报告生成
- 丰富的可视化功能
- 全面的测试覆盖

架构优化：
- 更清晰的模块分离
- 统一的接口设计
- 更好的依赖注入
- 增强的错误处理
- 完善的类型提示
"""

__version__ = "2.0.0"
__author__ = "Quant Team"
__email__ = "<EMAIL>"
__description__ = "下一代高性能量化回测引擎"

# 导入核心模块
from .core.engine.base import QuantEngine
from .core.config.global import GlobalConfig
from .core.structures.market import OHLCV
from .core.structures.trade import Order, Position, Trade
from .core.structures.portfolio import Portfolio

# 导入数据源
from .dataseed.base import DataSource
from .dataseed.mock import MockDataSource
from .dataseed.factory import DataSourceFactory

# 导入策略基类
from .strategies.base.strategy import BaseStrategy
from .strategies.factory import StrategyFactory
from .strategies.registry import StrategyRegistry

# 导入技术指标
from .indicators.base import Indicator
from .indicators.factory import IndicatorFactory

# 导入风险管理
from .risk.manager import RiskManager

# 导入优化器
from .optimizer.manager import OptimizationManager

# 导入报告生成
from .reports.generators.base import ReportGenerator

# 导入工具
from .utils.logger import get_logger
from .utils.cache import CacheManager

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__description__",
    
    # 核心组件
    "QuantEngine",
    "GlobalConfig",
    
    # 数据结构
    "OHLCV",
    "Order",
    "Position", 
    "Trade",
    "Portfolio",
    
    # 数据源
    "DataSource",
    "MockDataSource",
    "DataSourceFactory",
    
    # 策略
    "BaseStrategy",
    "StrategyFactory",
    "StrategyRegistry",
    
    # 技术指标
    "Indicator",
    "IndicatorFactory",
    
    # 风险管理
    "RiskManager",
    
    # 优化器
    "OptimizationManager",
    
    # 报告
    "ReportGenerator",
    
    # 工具
    "get_logger",
    "CacheManager",
]

# 快速开始接口
def create_engine(data_source="mock", config=None):
    """
    快速创建量化引擎
    
    Args:
        data_source: 数据源类型
        config: 配置对象
        
    Returns:
        QuantEngine: 配置好的量化引擎
    """
    if config is None:
        config = GlobalConfig.create_default()
    
    # 创建数据源
    ds = DataSourceFactory.create(data_source)
    
    # 创建引擎
    engine = QuantEngine(data_source=ds, config=config)
    
    return engine

def quick_backtest(strategy, symbol, start_date, end_date, **kwargs):
    """
    快速回测接口
    
    Args:
        strategy: 策略名称或策略对象
        symbol: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        **kwargs: 其他参数
        
    Returns:
        回测结果
    """
    engine = create_engine(**kwargs)
    
    # 如果是字符串，从注册器获取策略
    if isinstance(strategy, str):
        strategy = StrategyRegistry.get(strategy)
    
    return engine.run_backtest(
        strategy=strategy,
        symbol=symbol,
        start_date=start_date,
        end_date=end_date
    )

# 设置默认日志
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
