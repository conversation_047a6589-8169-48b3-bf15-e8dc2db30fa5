"""
数据源工厂

提供数据源的动态创建和管理功能。
"""

from typing import Dict, Type, Any, Optional, List
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from dataseed.base import DataSource
from dataseed.mock_source import MockDataSource
from utils.logger import get_logger

logger = get_logger(__name__)


class DataSourceFactory:
    """数据源工厂

    负责数据源的注册、创建和管理。
    """

    def __init__(self):
        self._sources: Dict[str, Type[DataSource]] = {}

        # 注册内置数据源
        self._register_builtin_sources()

        logger.info("数据源工厂初始化完成")

    def _register_builtin_sources(self):
        """注册内置数据源"""
        # 注册Mock数据源
        self.register("mock", MockDataSource)

        # 尝试注册AkShare数据源
        try:
            from dataseed.akshare_source import AkShareDataSource
            self.register("akshare", AkShareDataSource)
        except ImportError:
            logger.warning("AkShare数据源不可用")

        logger.info("内置数据源注册完成")

    def register(self, name: str, source_class: Type[DataSource]):
        """
        注册数据源

        Args:
            name: 数据源名称
            source_class: 数据源类
        """
        if not issubclass(source_class, DataSource):
            raise ValueError(f"数据源类必须继承DataSource: {source_class}")

        self._sources[name] = source_class
        logger.info(f"数据源已注册: {name}")

    def create(self, name: str, **kwargs) -> DataSource:
        """
        创建数据源实例

        Args:
            name: 数据源名称
            **kwargs: 数据源参数

        Returns:
            数据源实例
        """
        if name not in self._sources:
            raise ValueError(f"未注册的数据源: {name}")

        source_class = self._sources[name]

        # 设置默认名称
        if 'name' not in kwargs:
            kwargs['name'] = name

        # 创建数据源实例
        source = source_class(**kwargs)

        logger.info(f"数据源实例已创建: {name}")
        return source

    def get_available_sources(self) -> List[str]:
        """获取可用数据源列表"""
        return list(self._sources.keys())

    def get_source_info(self, name: str) -> Dict[str, Any]:
        """
        获取数据源信息

        Args:
            name: 数据源名称

        Returns:
            数据源信息
        """
        if name not in self._sources:
            raise ValueError(f"未注册的数据源: {name}")

        source_class = self._sources[name]

        return {
            'name': name,
            'class': source_class.__name__,
            'module': source_class.__module__,
            'doc': source_class.__doc__ or '',
        }

    def is_registered(self, name: str) -> bool:
        """检查数据源是否已注册"""
        return name in self._sources

    def unregister(self, name: str):
        """注销数据源"""
        if name in self._sources:
            del self._sources[name]
            logger.info(f"数据源已注销: {name}")

    def clear(self):
        """清空所有注册的数据源"""
        self._sources.clear()
        logger.info("所有数据源已清空")

    def __len__(self) -> int:
        """返回注册数据源数量"""
        return len(self._sources)

    def __contains__(self, name: str) -> bool:
        """检查数据源是否存在"""
        return name in self._sources

    def __iter__(self):
        """迭代数据源名称"""
        return iter(self._sources.keys())


# 全局数据源工厂实例
datasource_factory = DataSourceFactory()
