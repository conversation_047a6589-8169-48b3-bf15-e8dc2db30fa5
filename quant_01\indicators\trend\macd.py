"""
MACD指标

Moving Average Convergence Divergence - 移动平均收敛发散指标
"""

from typing import Dict, Union
import pandas as pd
import numpy as np

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from indicators.base import BaseIndicator
from utils.logger import get_logger

logger = get_logger(__name__)


class MACD(BaseIndicator):
    """MACD指标

    MACD是一个趋势跟踪动量指标，由三个部分组成：
    1. MACD线：快速EMA - 慢速EMA
    2. 信号线：MACD线的EMA
    3. 柱状图：MACD线 - 信号线

    优化特性：
    - 向量化计算，高性能
    - 支持自定义参数
    - 完整的数据验证
    - 结果缓存
    """

    def __init__(
        self,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9
    ):
        """
        初始化MACD指标

        Args:
            fast_period: 快速EMA周期
            slow_period: 慢速EMA周期
            signal_period: 信号线EMA周期
        """
        parameters = {
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': signal_period
        }

        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period

        super().__init__("MACD", parameters)

    def _validate_parameters(self):
        """验证参数"""
        if self.fast_period <= 0 or self.slow_period <= 0 or self.signal_period <= 0:
            raise ValueError("所有周期参数必须大于0")

        if self.fast_period >= self.slow_period:
            raise ValueError("快速周期必须小于慢速周期")

        if not all(isinstance(p, int) for p in [self.fast_period, self.slow_period, self.signal_period]):
            raise ValueError("所有周期参数必须是整数")

    def _calculate(self, data: Union[pd.Series, pd.DataFrame]) -> Dict[str, pd.Series]:
        """
        计算MACD指标

        Args:
            data: 价格数据（Series）或包含close列的DataFrame

        Returns:
            包含macd, signal, histogram的字典
        """
        # 提取收盘价
        if isinstance(data, pd.DataFrame):
            if 'close' not in data.columns:
                raise ValueError("DataFrame必须包含'close'列")
            close_prices = data['close']
        else:
            close_prices = data

        # 计算快速和慢速EMA
        fast_ema = self._calculate_ema(close_prices, self.fast_period)
        slow_ema = self._calculate_ema(close_prices, self.slow_period)

        # 计算MACD线
        macd_line = fast_ema - slow_ema

        # 计算信号线
        signal_line = self._calculate_ema(macd_line, self.signal_period)

        # 计算柱状图
        histogram = macd_line - signal_line

        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }

    def _calculate_ema(self, data: pd.Series, period: int) -> pd.Series:
        """
        计算指数移动平均

        Args:
            data: 输入数据
            period: 周期

        Returns:
            EMA序列
        """
        # 使用pandas的ewm方法计算EMA
        return data.ewm(span=period, adjust=False).mean()

    def get_signals(
        self,
        data: Union[pd.Series, pd.DataFrame],
        signal_threshold: float = 0.0
    ) -> pd.DataFrame:
        """
        生成MACD交易信号

        Args:
            data: 价格数据
            signal_threshold: 信号阈值

        Returns:
            包含信号的DataFrame
        """
        # 计算MACD
        macd_result = self.calculate(data)
        macd_line = macd_result['macd']
        signal_line = macd_result['signal']
        histogram = macd_result['histogram']

        # 创建信号DataFrame
        signals = pd.DataFrame(index=data.index if isinstance(data, pd.DataFrame) else data.index)

        # 计算穿越信号
        # 金叉：MACD线上穿信号线
        golden_cross = (macd_line > signal_line) & (macd_line.shift(1) <= signal_line.shift(1))
        # 死叉：MACD线下穿信号线
        death_cross = (macd_line < signal_line) & (macd_line.shift(1) >= signal_line.shift(1))

        # 生成信号
        signals['signal'] = 0
        signals.loc[golden_cross, 'signal'] = 1  # 买入信号
        signals.loc[death_cross, 'signal'] = -1  # 卖出信号

        # 应用信号阈值
        if signal_threshold != 0:
            # 只有当MACD线绝对值大于阈值时才产生信号
            weak_signals = macd_line.abs() < signal_threshold
            signals.loc[weak_signals, 'signal'] = 0

        # 添加MACD数据
        signals['macd'] = macd_line
        signals['signal_line'] = signal_line
        signals['histogram'] = histogram

        # 计算信号强度（基于柱状图）
        histogram_abs = histogram.abs()
        histogram_max = histogram_abs.rolling(window=50, min_periods=10).max()
        signals['strength'] = (histogram_abs / histogram_max.where(histogram_max > 0, 1.0)).clip(0, 1)

        return signals

    def get_divergence_signals(
        self,
        data: Union[pd.Series, pd.DataFrame],
        lookback_period: int = 20
    ) -> pd.DataFrame:
        """
        检测MACD背离信号

        Args:
            data: 价格数据
            lookback_period: 回看周期

        Returns:
            包含背离信号的DataFrame
        """
        # 提取收盘价
        if isinstance(data, pd.DataFrame):
            close_prices = data['close']
        else:
            close_prices = data

        # 计算MACD
        macd_result = self.calculate(data)
        macd_line = macd_result['macd']

        # 创建背离信号DataFrame
        divergence = pd.DataFrame(index=close_prices.index)
        divergence['bullish_divergence'] = False
        divergence['bearish_divergence'] = False

        # 寻找价格和MACD的高点和低点
        price_highs = close_prices.rolling(window=lookback_period, center=True).max() == close_prices
        price_lows = close_prices.rolling(window=lookback_period, center=True).min() == close_prices

        macd_highs = macd_line.rolling(window=lookback_period, center=True).max() == macd_line
        macd_lows = macd_line.rolling(window=lookback_period, center=True).min() == macd_line

        # 检测背离
        for i in range(lookback_period, len(close_prices) - lookback_period):
            current_idx = close_prices.index[i]

            # 寻找前一个高点/低点
            prev_high_indices = close_prices.index[price_highs.iloc[:i]]
            prev_low_indices = close_prices.index[price_lows.iloc[:i]]

            if len(prev_high_indices) > 0 and price_highs.iloc[i]:
                # 检测顶背离
                prev_high_idx = prev_high_indices[-1]
                if (close_prices.loc[current_idx] > close_prices.loc[prev_high_idx] and
                    macd_line.loc[current_idx] < macd_line.loc[prev_high_idx]):
                    divergence.loc[current_idx, 'bearish_divergence'] = True

            if len(prev_low_indices) > 0 and price_lows.iloc[i]:
                # 检测底背离
                prev_low_idx = prev_low_indices[-1]
                if (close_prices.loc[current_idx] < close_prices.loc[prev_low_idx] and
                    macd_line.loc[current_idx] > macd_line.loc[prev_low_idx]):
                    divergence.loc[current_idx, 'bullish_divergence'] = True

        return divergence

    def get_trend_strength(self, data: Union[pd.Series, pd.DataFrame]) -> pd.Series:
        """
        计算趋势强度

        Args:
            data: 价格数据

        Returns:
            趋势强度序列（-1到1，正值表示上升趋势）
        """
        # 计算MACD
        macd_result = self.calculate(data)
        macd_line = macd_result['macd']
        signal_line = macd_result['signal']
        histogram = macd_result['histogram']

        # 基于MACD组件计算趋势强度
        # 1. MACD线相对于信号线的位置
        macd_signal_ratio = (macd_line - signal_line) / (macd_line.abs() + signal_line.abs() + 1e-8)

        # 2. 柱状图的方向和强度
        histogram_normalized = histogram / (histogram.abs().rolling(window=50).max() + 1e-8)

        # 3. MACD线的斜率
        macd_slope = macd_line.diff() / (macd_line.abs() + 1e-8)

        # 综合趋势强度
        trend_strength = (macd_signal_ratio + histogram_normalized + macd_slope) / 3

        # 限制范围到[-1, 1]
        trend_strength = trend_strength.clip(-1, 1)

        return trend_strength

    @classmethod
    def create_fast(cls) -> 'MACD':
        """创建快速MACD（参数较小）"""
        return cls(fast_period=8, slow_period=21, signal_period=5)

    @classmethod
    def create_slow(cls) -> 'MACD':
        """创建慢速MACD（参数较大）"""
        return cls(fast_period=16, slow_period=35, signal_period=12)

    @classmethod
    def create_custom(cls, fast: int, slow: int, signal: int) -> 'MACD':
        """创建自定义参数的MACD"""
        return cls(fast_period=fast, slow_period=slow, signal_period=signal)
