"""
策略基类模块

定义所有量化策略的基础接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from pydantic import BaseModel, Field, validator
import uuid

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from core.interfaces import IStrategy
from core.structures import Order, Position, Trade
from utils.logger import get_logger

logger = get_logger(__name__)


class StrategyConfig(BaseModel):
    """策略配置基类"""
    
    # 基本信息
    strategy_name: str = Field(description="策略名称")
    strategy_id: Optional[str] = Field(default=None, description="策略ID")
    description: str = Field(default="", description="策略描述")
    
    # 资金管理
    initial_capital: float = Field(default=1000000.0, description="初始资金", gt=0)
    position_size: float = Field(default=0.1, description="仓位大小", ge=0, le=1)
    max_positions: int = Field(default=1, description="最大持仓数", ge=1)
    
    # 风险控制
    stop_loss: Optional[float] = Field(default=None, description="止损比例", ge=0, le=1)
    take_profit: Optional[float] = Field(default=None, description="止盈比例", ge=0)
    max_drawdown: Optional[float] = Field(default=None, description="最大回撤限制", ge=0, le=1)
    
    # 信号过滤
    min_signal_strength: float = Field(default=0.5, description="最小信号强度", ge=0, le=1)
    signal_threshold: float = Field(default=0.0, description="信号阈值", ge=0)
    
    # 交易控制
    enable_short: bool = Field(default=False, description="是否允许做空")
    max_trades_per_day: Optional[int] = Field(default=None, description="每日最大交易次数")
    min_holding_period: Optional[int] = Field(default=None, description="最小持仓天数")
    
    # 扩展参数
    custom_params: Dict[str, Any] = Field(default_factory=dict, description="自定义参数")
    
    def __post_init__(self):
        """初始化后处理"""
        if self.strategy_id is None:
            self.strategy_id = f"{self.strategy_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    @validator('position_size')
    def validate_position_size(cls, v):
        if v < 0 or v > 1:
            raise ValueError("仓位大小必须在0-1之间")
        return v
    
    @validator('stop_loss', 'take_profit')
    def validate_risk_params(cls, v):
        if v is not None and v < 0:
            raise ValueError("风险参数不能为负数")
        return v


@dataclass
class StrategyResult:
    """策略执行结果"""
    
    strategy_id: str
    strategy_name: str
    symbol: str
    start_date: datetime
    end_date: datetime
    
    # 基本指标
    total_return: float = 0.0
    annual_return: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    
    # 交易指标
    total_trades: int = 0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_trade_return: float = 0.0
    
    # 详细数据
    signals: pd.DataFrame = field(default_factory=pd.DataFrame)
    trades: List[Trade] = field(default_factory=list)
    equity_curve: pd.Series = field(default_factory=pd.Series)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)


class BaseStrategy(IStrategy, ABC):
    """策略抽象基类
    
    所有量化策略都应该继承此类。
    
    优化特性：
    - 标准化的策略接口
    - 完整的生命周期管理
    - 内置性能监控
    - 灵活的参数配置
    - 详细的执行日志
    """
    
    def __init__(self, config: StrategyConfig):
        """
        初始化策略
        
        Args:
            config: 策略配置
        """
        self.config = config
        self.name = config.strategy_name
        self.strategy_id = config.strategy_id or str(uuid.uuid4())
        
        # 内部状态
        self._initialized = False
        self._data = None
        self._current_position = 0.0
        self._cash = config.initial_capital
        self._portfolio_value = config.initial_capital
        
        # 交易记录
        self._trades: List[Trade] = []
        self._orders: List[Order] = []
        self._signals = pd.DataFrame()
        
        # 性能统计
        self._stats = {
            'signal_count': 0,
            'trade_count': 0,
            'execution_time': 0.0,
            'last_update': None
        }
        
        # 指标缓存
        self._indicators_cache: Dict[str, Any] = {}
        
        logger.info(f"策略初始化: {self.name} ({self.strategy_id})")
    
    def initialize(self, **kwargs):
        """初始化策略"""
        self._initialized = True
        self._indicators_cache.clear()
        logger.info(f"策略初始化完成: {self.name}")
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            data: 价格数据DataFrame，包含OHLCV列
            
        Returns:
            信号DataFrame，包含以下列：
            - signal: 信号方向 (1: 买入, -1: 卖出, 0: 无信号)
            - strength: 信号强度 (0-1)
            - price: 信号价格
            - timestamp: 信号时间
        """
        pass
    
    def get_name(self) -> str:
        """获取策略名称"""
        return self.name
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        return self.config.dict()
    
    def precompute_indicators(self, data: pd.DataFrame):
        """预计算技术指标"""
        # 子类可以重写此方法来预计算指标
        pass
    
    def validate_signal(self, signal: pd.Series, data: pd.DataFrame) -> bool:
        """验证信号有效性"""
        # 检查信号强度
        if signal.get('strength', 0) < self.config.min_signal_strength:
            return False
        
        # 检查信号阈值
        if abs(signal.get('signal', 0)) < self.config.signal_threshold:
            return False
        
        # 检查做空限制
        if signal.get('signal', 0) < 0 and not self.config.enable_short:
            return False
        
        return True
    
    def calculate_position_size(self, signal_strength: float, current_price: float) -> float:
        """计算仓位大小"""
        base_size = self.config.position_size * self.config.initial_capital
        
        # 根据信号强度调整仓位
        adjusted_size = base_size * signal_strength
        
        # 转换为股数
        shares = int(adjusted_size / current_price)
        
        return shares
    
    def apply_risk_management(self, signals: pd.DataFrame, data: pd.DataFrame) -> pd.DataFrame:
        """应用风险管理规则"""
        filtered_signals = signals.copy()
        
        # 应用止损止盈
        if self.config.stop_loss is not None or self.config.take_profit is not None:
            filtered_signals = self._apply_stop_loss_take_profit(filtered_signals, data)
        
        # 应用最大回撤限制
        if self.config.max_drawdown is not None:
            filtered_signals = self._apply_drawdown_limit(filtered_signals, data)
        
        # 应用交易频率限制
        if self.config.max_trades_per_day is not None:
            filtered_signals = self._apply_trade_frequency_limit(filtered_signals)
        
        return filtered_signals
    
    def _apply_stop_loss_take_profit(self, signals: pd.DataFrame, data: pd.DataFrame) -> pd.DataFrame:
        """应用止损止盈"""
        # 简化实现，实际应该根据持仓情况动态调整
        return signals
    
    def _apply_drawdown_limit(self, signals: pd.DataFrame, data: pd.DataFrame) -> pd.DataFrame:
        """应用回撤限制"""
        # 简化实现
        return signals
    
    def _apply_trade_frequency_limit(self, signals: pd.DataFrame) -> pd.DataFrame:
        """应用交易频率限制"""
        # 简化实现
        return signals
    
    def get_indicator(self, name: str) -> Optional[Any]:
        """获取缓存的指标"""
        return self._indicators_cache.get(name)
    
    def set_indicator(self, name: str, value: Any):
        """设置指标缓存"""
        self._indicators_cache[name] = value
    
    def get_stats(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        return self._stats.copy()
    
    def reset(self):
        """重置策略状态"""
        self._initialized = False
        self._data = None
        self._current_position = 0.0
        self._cash = self.config.initial_capital
        self._portfolio_value = self.config.initial_capital
        self._trades.clear()
        self._orders.clear()
        self._signals = pd.DataFrame()
        self._indicators_cache.clear()
        
        logger.info(f"策略状态已重置: {self.name}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'strategy_id': self.strategy_id,
            'name': self.name,
            'config': self.config.dict(),
            'stats': self.get_stats(),
            'initialized': self._initialized,
        }
    
    def __str__(self) -> str:
        return f"{self.name}({self.strategy_id})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}: {self.name}>"
