<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化回测报告查看器</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    </script>
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3a0ca3;
            --success-color: #4cc9f0;
            --danger-color: #f72585;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }
        
        body {
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1.5rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        .file-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            border: none;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline:hover {
            background-color: rgba(67, 97, 238, 0.1);
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .chart-container {
            height: 400px;
            position: relative;
        }

        .table-container {
            overflow-x: auto;
        }

        .trades-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .trades-table th,
        .trades-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .trades-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: var(--dark-color);
        }

        .trades-table tr:hover {
            background-color: #f5f7fa;
        }
        
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary-color);
        }
        
        .metric-value {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }
        
        .spinner {
            width: 2rem;
            height: 2rem;
            border: 3px solid rgba(67, 97, 238, 0.2);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .file-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1 id="reportTitle">量化回测报告查看器</h1>
                <div class="file-actions">
                    <button id="selectFileBtn" class="btn btn-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                        </svg>
                        选择报告文件
                    </button>
                </div>
            </div>
        </div>

        <div class="main-content">

            <div id="reportContent" style="display: none;">
                <div class="metrics-grid" id="metricsGrid">
                    <!-- 指标卡片将通过JS动态生成 -->
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">交易记录</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table id="tradesTable" class="trades-table">
                                <thead>
                                    <tr>
                                        <th>入场日期</th>
                                        <th>出场日期</th>
                                        <th>入场价格</th>
                                        <th>出场价格</th>
                                        <th>数量</th>
                                        <th>盈亏</th>
                                        <th>收益率</th>
                                        <th>持仓天数</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 交易记录将通过JS动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">投资组合净值曲线</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="portfolio-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">回撤曲线</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="drawdown-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">月度收益分布</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthly-returns-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="loadingIndicator" class="loading" style="display: none;">
                <div class="spinner"></div>
                <span style="margin-left: 1rem;">加载中...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentReportData = null;

        // DOM元素
        const dom = {
            reportTitle: document.getElementById('reportTitle'),
            selectFileBtn: document.getElementById('selectFileBtn'),
            reportContent: document.getElementById('reportContent'),
            loadingIndicator: document.getElementById('loadingIndicator'),
            metricsGrid: document.getElementById('metricsGrid')
        };

        // 加载并显示选中的报告
        async function loadReport(file) {
            showLoading();
            dom.reportContent.style.display = 'none';
            
            try {
                const reportData = await readFile(file);
                currentReportData = reportData;
                window.reportData = reportData; // 用于调试
                
                // 更新UI
                updateReportTitle(file.name || '回测报告');
                renderMetrics(reportData);
                renderTradesTable(reportData);
                renderCharts(reportData);
                
                dom.reportContent.style.display = 'block';
            } catch (error) {
                console.error('加载报告失败:', error);
                alert(`加载报告失败: ${error.message}`);
            } finally {
                hideLoading();
            }
        }

        // 读取文件内容
        function readFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                
                reader.onload = event => {
                    try {
                        const data = JSON.parse(event.target.result);
                        resolve(data);
                    } catch (error) {
                        reject(new Error('文件解析失败，请确保是有效的JSON文件'));
                    }
                };
                
                reader.onerror = () => {
                    reject(new Error('文件读取失败'));
                };
                
                reader.readAsText(file);
            });
        }

        // 显示加载状态
        function showLoading() {
            dom.loadingIndicator.style.display = 'flex';
        }

        // 隐藏加载状态
        function hideLoading() {
            dom.loadingIndicator.style.display = 'none';
        }

        // 更新报告标题
        function updateReportTitle(title) {
            dom.reportTitle.textContent = title;
        }

        // 渲染关键指标
        function renderMetrics(data) {
            if (!data.metrics) {
                dom.metricsGrid.innerHTML = '<div class="metric-card"><div class="metric-value">无指标数据</div></div>';
                return;
            }

            const metrics = data.metrics;
            let html = '';

            // 总收益率
            if (metrics.total_return !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value" style="color: ${metrics.total_return >= 0 ? 'var(--success-color)' : 'var(--danger-color)'}">
                            ${(metrics.total_return * 100).toFixed(2)}%
                        </div>
                        <div class="metric-label">总收益率</div>
                    </div>
                `;
            }

            // 年化收益率
            if (metrics.annual_return !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value" style="color: ${metrics.annual_return >= 0 ? 'var(--success-color)' : 'var(--danger-color)'}">
                            ${(metrics.annual_return * 100).toFixed(2)}%
                        </div>
                        <div class="metric-label">年化收益率</div>
                    </div>
                `;
            }

            // 最大回撤
            if (metrics.max_drawdown !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value" style="color: var(--danger-color);">${(metrics.max_drawdown * 100).toFixed(2)}%</div>
                        <div class="metric-label">最大回撤</div>
                    </div>
                `;
            }

            // 夏普比率
            if (metrics.sharpe_ratio !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value">${metrics.sharpe_ratio.toFixed(2)}</div>
                        <div class="metric-label">夏普比率</div>
                    </div>
                `;
            }

            // 胜率
            if (metrics.win_rate !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value">${(metrics.win_rate * 100).toFixed(2)}%</div>
                        <div class="metric-label">胜率</div>
                    </div>
                `;
            }

            // 交易次数
            if (metrics.total_trades !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value">${metrics.total_trades}</div>
                        <div class="metric-label">总交易次数</div>
                    </div>
                `;
            }

            // 平均交易收益率
            if (metrics.avg_trade_return !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value" style="color: ${metrics.avg_trade_return >= 0 ? 'var(--success-color)' : 'var(--danger-color)'}">
                            ${(metrics.avg_trade_return * 100).toFixed(2)}%
                        </div>
                        <div class="metric-label">平均交易收益率</div>
                    </div>
                `;
            }

            // 最佳交易
            if (metrics.best_trade !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value" style="color: var(--success-color);">
                            ${(metrics.best_trade * 100).toFixed(2)}%
                        </div>
                        <div class="metric-label">最佳交易</div>
                    </div>
                `;
            }

            // 最差交易
            if (metrics.worst_trade !== undefined) {
                html += `
                    <div class="metric-card">
                        <div class="metric-value" style="color: var(--danger-color);">
                            ${(metrics.worst_trade * 100).toFixed(2)}%
                        </div>
                        <div class="metric-label">最差交易</div>
                    </div>
                `;
            }

            dom.metricsGrid.innerHTML = html || '<div class="metric-card"><div class="metric-value">无指标数据</div></div>';
        }

        // 渲染交易记录表格
        function renderTradesTable(data) {
            if (!data.trades || !data.trades.length) {
                document.getElementById('tradesTable').querySelector('tbody').innerHTML =
                    '<tr><td colspan="8" style="text-align: center;">无交易记录</td></tr>';
                return;
            }

            const tbody = document.getElementById('tradesTable').querySelector('tbody');
            let html = '';

            data.trades.forEach(trade => {
                html += `
                    <tr>
                        <td>${trade.entry_date}</td>
                        <td>${trade.exit_date}</td>
                        <td>${trade.entry_price.toFixed(4)}</td>
                        <td>${trade.exit_price.toFixed(4)}</td>
                        <td>${trade.size.toFixed(2)}</td>
                        <td style="color: ${trade.pnl >= 0 ? 'var(--success-color)' : 'var(--danger-color)'}">
                            ${trade.pnl.toFixed(2)}
                        </td>
                        <td style="color: ${trade.return_pct >= 0 ? 'var(--success-color)' : 'var(--danger-color)'}">
                            ${(trade.return_pct * 100).toFixed(2)}%
                        </td>
                        <td>${trade.duration}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // 渲染图表
        function renderCharts(data) {
            // 销毁现有图表
            ['portfolio-chart', 'drawdown-chart', 'monthly-returns-chart'].forEach(id => {
                const chart = Chart.getChart(id);
                if (chart) chart.destroy();
            });

            // 净值曲线
            if (data.portfolio_value || (data.chart_data && data.chart_data.portfolio_value)) {
                const portfolioData = data.portfolio_value || data.chart_data.portfolio_value;
                new Chart(document.getElementById('portfolio-chart'), {
                    type: 'line',
                    data: {
                        labels: portfolioData.dates,
                        datasets: [{
                            label: '投资组合净值',
                            data: portfolioData.values,
                            borderColor: 'var(--primary-color)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: context => `${context.dataset.label}: ${context.raw.toFixed(2)}`
                                }
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: '日期' } },
                            y: {
                                title: { display: true, text: '净值' },
                                ticks: { callback: value => value.toFixed(2) }
                            }
                        }
                    }
                });
            }

            // 回撤曲线
            if (data.drawdown || (data.chart_data && data.chart_data.drawdown)) {
                const drawdownData = data.drawdown || data.chart_data.drawdown;
                new Chart(document.getElementById('drawdown-chart'), {
                    type: 'line',
                    data: {
                        labels: drawdownData.dates,
                        datasets: [{
                            label: '回撤',
                            data: drawdownData.values.map(v => v * 100),
                            borderColor: 'var(--danger-color)',
                            borderWidth: 2,
                            backgroundColor: 'rgba(247, 37, 133, 0.1)',
                            fill: true,
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: context => `${context.dataset.label}: ${context.raw.toFixed(2)}%`
                                }
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: '日期' } },
                            y: {
                                title: { display: true, text: '回撤 (%)' },
                                ticks: { callback: value => `${value.toFixed(2)}%` }
                            }
                        }
                    }
                });
            }

            // 月度收益
            if (data.monthly_returns || (data.chart_data && data.chart_data.monthly_returns)) {
                const monthlyReturnsData = data.monthly_returns || data.chart_data.monthly_returns;
                new Chart(document.getElementById('monthly-returns-chart'), {
                    type: 'bar',
                    data: {
                        labels: monthlyReturnsData.dates,
                        datasets: [{
                            label: '月度收益',
                            data: monthlyReturnsData.returns.map(v => v * 100),
                            backgroundColor: monthlyReturnsData.returns.map(
                                v => v > 0 ? 'var(--success-color)' : 'var(--danger-color)'
                            ),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: context => `${context.dataset.label}: ${context.raw.toFixed(2)}%`
                                }
                            }
                        },
                        scales: {
                            x: { title: { display: true, text: '月份' } },
                            y: {
                                title: { display: true, text: '收益率 (%)' },
                                ticks: { callback: value => `${value.toFixed(2)}%` }
                            }
                        }
                    }
                });
            }
        }


        // 初始化文件选择按钮
        function initFileSelector() {
            dom.selectFileBtn.addEventListener('click', () => {
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = '.json';
                fileInput.style.display = 'none';
                
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length) {
                        loadReport(e.target.files[0]);
                    }
                });
                
                fileInput.click();
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initFileSelector();
        });
    </script>
</body>
</html>