"""
工具模块

提供各种实用工具和辅助功能。
"""

from .logger import (
    get_logger, init_logger, set_log_level,
    enable_file_logging, enable_console_logging, set_log_file,
    DEBUG, INFO, WARNING, ERROR, CRITICAL
)
from .cache import CacheManager, cached, get_global_cache

# 尝试导入装饰器和验证器（可能不完整）
try:
    from .decorators import timing, timeout
    _DECORATORS_AVAILABLE = True
except ImportError:
    timing = timeout = None
    _DECORATORS_AVAILABLE = False

try:
    from .validators import DataValidator, validate_ohlcv, validate_price_data
    _VALIDATORS_AVAILABLE = True
except ImportError:
    DataValidator = validate_ohlcv = validate_price_data = None
    _VALIDATORS_AVAILABLE = False

__all__ = [
    # 日志工具
    "get_logger",
    "init_logger",
    "set_log_level",
    "enable_file_logging",
    "enable_console_logging",
    "set_log_file",
    "DEBUG",
    "INFO",
    "WARNING",
    "ERROR",
    "CRITICAL",

    # 缓存工具
    "CacheManager",
    "cached",
    "get_global_cache",
]

# 动态添加可用的工具到__all__
if _DECORATORS_AVAILABLE:
    __all__.extend(["timing", "timeout"])

if _VALIDATORS_AVAILABLE:
    __all__.extend(["DataValidator", "validate_ohlcv", "validate_price_data"])
