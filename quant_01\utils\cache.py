"""
缓存管理系统

提供多级缓存功能。
"""

import time
import pickle
import hashlib
from typing import Any, Optional, Dict, Union
from pathlib import Path
import threading
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.config.global_config import GlobalConfig
from utils.logger import get_logger

logger = get_logger(__name__)


class CacheManager:
    """缓存管理器

    提供内存缓存和磁盘缓存功能。
    """

    def __init__(self, config: Optional[GlobalConfig] = None):
        """
        初始化缓存管理器

        Args:
            config: 全局配置
        """
        self.config = config or GlobalConfig.create_default()
        self.cache_config = self.config.cache

        # 内存缓存
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._access_times: Dict[str, float] = {}
        self._lock = threading.RLock()

        # 磁盘缓存路径
        self.disk_cache_path = Path(self.cache_config.disk_path)
        self.disk_cache_path.mkdir(parents=True, exist_ok=True)

        logger.info("缓存管理器初始化完成")

    def _generate_key(self, key: str) -> str:
        """生成缓存键的哈希值"""
        return hashlib.md5(key.encode()).hexdigest()

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值

        Args:
            key: 缓存键
            default: 默认值

        Returns:
            缓存值
        """
        if not self.cache_config.enabled:
            return default

        # 先尝试内存缓存
        if self.cache_config.memory_enabled:
            memory_value = self._get_from_memory(key)
            if memory_value is not None:
                return memory_value

        # 再尝试磁盘缓存
        if self.cache_config.disk_enabled:
            disk_value = self._get_from_disk(key)
            if disk_value is not None:
                # 将磁盘缓存加载到内存
                if self.cache_config.memory_enabled:
                    self._set_to_memory(key, disk_value)
                return disk_value

        return default

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存值

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）

        Returns:
            是否设置成功
        """
        if not self.cache_config.enabled:
            return False

        if ttl is None:
            ttl = self.cache_config.default_ttl

        success = True

        # 设置内存缓存
        if self.cache_config.memory_enabled:
            success &= self._set_to_memory(key, value, ttl)

        # 设置磁盘缓存
        if self.cache_config.disk_enabled:
            success &= self._set_to_disk(key, value, ttl)

        return success

    def delete(self, key: str) -> bool:
        """
        删除缓存

        Args:
            key: 缓存键

        Returns:
            是否删除成功
        """
        success = True

        # 删除内存缓存
        if self.cache_config.memory_enabled:
            success &= self._delete_from_memory(key)

        # 删除磁盘缓存
        if self.cache_config.disk_enabled:
            success &= self._delete_from_disk(key)

        return success

    def clear(self):
        """清空所有缓存"""
        # 清空内存缓存
        with self._lock:
            self._memory_cache.clear()
            self._access_times.clear()

        # 清空磁盘缓存
        if self.disk_cache_path.exists():
            for file_path in self.disk_cache_path.glob("*.cache"):
                try:
                    file_path.unlink()
                except Exception as e:
                    logger.warning(f"删除缓存文件失败: {file_path}, 错误: {e}")

        logger.info("缓存已清空")

    def _get_from_memory(self, key: str) -> Any:
        """从内存获取缓存"""
        with self._lock:
            hashed_key = self._generate_key(key)

            if hashed_key not in self._memory_cache:
                return None

            cache_item = self._memory_cache[hashed_key]

            # 检查是否过期
            if cache_item['expires_at'] < time.time():
                del self._memory_cache[hashed_key]
                if hashed_key in self._access_times:
                    del self._access_times[hashed_key]
                return None

            # 更新访问时间
            self._access_times[hashed_key] = time.time()

            return cache_item['value']

    def _set_to_memory(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置内存缓存"""
        if ttl is None:
            ttl = self.cache_config.default_ttl

        with self._lock:
            # 检查缓存大小限制
            if len(self._memory_cache) >= self.cache_config.memory_max_size:
                self._evict_memory_cache()

            hashed_key = self._generate_key(key)
            expires_at = time.time() + ttl

            self._memory_cache[hashed_key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': time.time()
            }

            self._access_times[hashed_key] = time.time()

            return True

    def _delete_from_memory(self, key: str) -> bool:
        """从内存删除缓存"""
        with self._lock:
            hashed_key = self._generate_key(key)

            if hashed_key in self._memory_cache:
                del self._memory_cache[hashed_key]

            if hashed_key in self._access_times:
                del self._access_times[hashed_key]

            return True

    def _evict_memory_cache(self):
        """内存缓存淘汰"""
        if not self._access_times:
            return

        # LRU淘汰策略
        oldest_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])

        if oldest_key in self._memory_cache:
            del self._memory_cache[oldest_key]

        if oldest_key in self._access_times:
            del self._access_times[oldest_key]

    def _get_from_disk(self, key: str) -> Any:
        """从磁盘获取缓存"""
        try:
            hashed_key = self._generate_key(key)
            cache_file = self.disk_cache_path / f"{hashed_key}.cache"

            if not cache_file.exists():
                return None

            with open(cache_file, 'rb') as f:
                cache_item = pickle.load(f)

            # 检查是否过期
            if cache_item['expires_at'] < time.time():
                cache_file.unlink(missing_ok=True)
                return None

            return cache_item['value']

        except Exception as e:
            logger.warning(f"磁盘缓存读取失败: {key}, 错误: {e}")
            return None

    def _set_to_disk(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置磁盘缓存"""
        if ttl is None:
            ttl = self.cache_config.default_ttl

        try:
            hashed_key = self._generate_key(key)
            cache_file = self.disk_cache_path / f"{hashed_key}.cache"

            expires_at = time.time() + ttl

            cache_item = {
                'value': value,
                'expires_at': expires_at,
                'created_at': time.time()
            }

            with open(cache_file, 'wb') as f:
                pickle.dump(cache_item, f)

            return True

        except Exception as e:
            logger.warning(f"磁盘缓存写入失败: {key}, 错误: {e}")
            return False

    def _delete_from_disk(self, key: str) -> bool:
        """从磁盘删除缓存"""
        try:
            hashed_key = self._generate_key(key)
            cache_file = self.disk_cache_path / f"{hashed_key}.cache"

            cache_file.unlink(missing_ok=True)
            return True

        except Exception as e:
            logger.warning(f"磁盘缓存删除失败: {key}, 错误: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            memory_size = len(self._memory_cache)

        disk_files = list(self.disk_cache_path.glob("*.cache"))
        disk_size = len(disk_files)

        return {
            'memory_cache_size': memory_size,
            'disk_cache_size': disk_size,
            'memory_enabled': self.cache_config.memory_enabled,
            'disk_enabled': self.cache_config.disk_enabled,
            'cache_enabled': self.cache_config.enabled,
        }


# 全局缓存管理器实例
cache_manager = CacheManager()
