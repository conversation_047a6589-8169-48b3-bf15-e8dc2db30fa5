"""
波动率指标

实现各种波动率分析技术指标。
"""

from typing import Tuple
import pandas as pd
import numpy as np

from ..logger import get_logger

logger = get_logger(__name__)


def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """
    平均真实范围 (Average True Range)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: 计算周期
        
    Returns:
        ATR序列
    """
    if len(high) < period + 1:
        logger.warning(f"数据长度不足: {len(high)} < {period + 1}")
        return pd.Series(index=high.index, dtype=float)
    
    # 计算真实范围
    prev_close = close.shift(1)
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算ATR（真实范围的移动平均）
    atr_values = true_range.rolling(window=period, min_periods=period).mean()
    
    return atr_values


def standard_deviation(data: pd.Series, period: int = 20) -> pd.Series:
    """
    标准差 (Standard Deviation)
    
    Args:
        data: 价格序列
        period: 计算周期
        
    Returns:
        标准差序列
    """
    if len(data) < period:
        logger.warning(f"数据长度不足: {len(data)} < {period}")
        return pd.Series(index=data.index, dtype=float)
    
    return data.rolling(window=period, min_periods=period).std()


def historical_volatility(data: pd.Series, period: int = 20, annualize: bool = True) -> pd.Series:
    """
    历史波动率 (Historical Volatility)
    
    Args:
        data: 价格序列
        period: 计算周期
        annualize: 是否年化
        
    Returns:
        历史波动率序列
    """
    if len(data) < period + 1:
        logger.warning(f"数据长度不足: {len(data)} < {period + 1}")
        return pd.Series(index=data.index, dtype=float)
    
    # 计算对数收益率
    log_returns = np.log(data / data.shift(1))
    
    # 计算滚动标准差
    volatility = log_returns.rolling(window=period, min_periods=period).std()
    
    # 年化处理
    if annualize:
        volatility = volatility * np.sqrt(252)  # 假设252个交易日
    
    return volatility


def chaikin_volatility(
    high: pd.Series, 
    low: pd.Series, 
    period: int = 10, 
    roc_period: int = 10
) -> pd.Series:
    """
    蔡金波动率 (Chaikin Volatility)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        period: EMA周期
        roc_period: 变化率周期
        
    Returns:
        蔡金波动率序列
    """
    if len(high) < period + roc_period:
        logger.warning(f"数据长度不足: {len(high)} < {period + roc_period}")
        return pd.Series(index=high.index, dtype=float)
    
    # 计算高低价差的EMA
    hl_diff = high - low
    ema_hl = hl_diff.ewm(span=period).mean()
    
    # 计算变化率
    chaikin_vol = ((ema_hl - ema_hl.shift(roc_period)) / ema_hl.shift(roc_period)) * 100
    
    return chaikin_vol


def keltner_channels(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    period: int = 20, 
    multiplier: float = 2.0
) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    肯特纳通道 (Keltner Channels)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: 计算周期
        multiplier: ATR倍数
        
    Returns:
        (上轨, 中轨, 下轨)
    """
    if len(high) < period + 1:
        logger.warning(f"数据长度不足: {len(high)} < {period + 1}")
        empty_series = pd.Series(index=high.index, dtype=float)
        return empty_series, empty_series, empty_series
    
    # 中轨：收盘价的EMA
    middle_line = close.ewm(span=period).mean()
    
    # 计算ATR
    atr_values = atr(high, low, close, period)
    
    # 上轨和下轨
    upper_line = middle_line + (multiplier * atr_values)
    lower_line = middle_line - (multiplier * atr_values)
    
    return upper_line, middle_line, lower_line


def donchian_channels(
    high: pd.Series, 
    low: pd.Series, 
    period: int = 20
) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    唐奇安通道 (Donchian Channels)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        period: 计算周期
        
    Returns:
        (上轨, 中轨, 下轨)
    """
    if len(high) < period:
        logger.warning(f"数据长度不足: {len(high)} < {period}")
        empty_series = pd.Series(index=high.index, dtype=float)
        return empty_series, empty_series, empty_series
    
    # 上轨：最高价的最大值
    upper_line = high.rolling(window=period, min_periods=period).max()
    
    # 下轨：最低价的最小值
    lower_line = low.rolling(window=period, min_periods=period).min()
    
    # 中轨：上轨和下轨的平均值
    middle_line = (upper_line + lower_line) / 2
    
    return upper_line, middle_line, lower_line


def price_channels(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    period: int = 20
) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    价格通道 (Price Channels)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: 计算周期
        
    Returns:
        (上轨, 中轨, 下轨)
    """
    if len(high) < period:
        logger.warning(f"数据长度不足: {len(high)} < {period}")
        empty_series = pd.Series(index=high.index, dtype=float)
        return empty_series, empty_series, empty_series
    
    # 上轨：最高价的最大值
    upper_line = high.rolling(window=period, min_periods=period).max()
    
    # 下轨：最低价的最小值
    lower_line = low.rolling(window=period, min_periods=period).min()
    
    # 中轨：收盘价的移动平均
    middle_line = close.rolling(window=period, min_periods=period).mean()
    
    return upper_line, middle_line, lower_line


def mass_index(high: pd.Series, low: pd.Series, period: int = 25) -> pd.Series:
    """
    质量指标 (Mass Index)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        period: 计算周期
        
    Returns:
        质量指标序列
    """
    if len(high) < period + 18:  # 需要额外的数据来计算EMA
        logger.warning(f"数据长度不足: {len(high)} < {period + 18}")
        return pd.Series(index=high.index, dtype=float)
    
    # 计算高低价差
    hl_diff = high - low
    
    # 计算9期EMA
    ema9 = hl_diff.ewm(span=9).mean()
    
    # 计算9期EMA的9期EMA
    ema9_ema9 = ema9.ewm(span=9).mean()
    
    # 计算比率
    ratio = ema9 / ema9_ema9
    
    # 计算质量指标（25期总和）
    mass_index_values = ratio.rolling(window=period, min_periods=period).sum()
    
    return mass_index_values


def vortex_indicator(
    high: pd.Series, 
    low: pd.Series, 
    close: pd.Series, 
    period: int = 14
) -> Tuple[pd.Series, pd.Series]:
    """
    涡流指标 (Vortex Indicator)
    
    Args:
        high: 最高价序列
        low: 最低价序列
        close: 收盘价序列
        period: 计算周期
        
    Returns:
        (VI+, VI-)
    """
    if len(high) < period + 1:
        logger.warning(f"数据长度不足: {len(high)} < {period + 1}")
        empty_series = pd.Series(index=high.index, dtype=float)
        return empty_series, empty_series
    
    # 计算涡流运动
    vm_plus = abs(high - low.shift(1))
    vm_minus = abs(low - high.shift(1))
    
    # 计算真实范围
    prev_close = close.shift(1)
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算涡流指标
    vi_plus = vm_plus.rolling(window=period).sum() / true_range.rolling(window=period).sum()
    vi_minus = vm_minus.rolling(window=period).sum() / true_range.rolling(window=period).sum()
    
    return vi_plus, vi_minus


__all__ = [
    "atr",
    "standard_deviation",
    "historical_volatility",
    "chaikin_volatility",
    "keltner_channels",
    "donchian_channels",
    "price_channels",
    "mass_index",
    "vortex_indicator"
]
