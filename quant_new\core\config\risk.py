"""
风险管理配置模块

定义风险控制相关的配置参数，包括止盈止损、仓位管理、风险限制等。
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator
from enum import Enum


class StopType(str, Enum):
    """止损类型"""
    FIXED_RATIO = "fixed_ratio"  # 固定比例
    TRAILING = "trailing"        # 移动止损
    ATR = "atr"                 # ATR止损
    SUPPORT_RESISTANCE = "sr"    # 支撑阻力位
    TIME_BASED = "time"         # 时间止损


class PositionSizeMethod(str, Enum):
    """仓位计算方法"""
    FIXED_AMOUNT = "fixed_amount"    # 固定金额
    FIXED_RATIO = "fixed_ratio"      # 固定比例
    KELLY = "kelly"                  # 凯利公式
    RISK_PARITY = "risk_parity"      # 风险平价
    VOLATILITY_TARGET = "vol_target" # 波动率目标


class RiskMetric(str, Enum):
    """风险指标"""
    MAX_DRAWDOWN = "max_drawdown"
    VAR = "var"                      # 风险价值
    CVAR = "cvar"                    # 条件风险价值
    VOLATILITY = "volatility"        # 波动率
    BETA = "beta"                    # 贝塔系数
    TRACKING_ERROR = "tracking_error" # 跟踪误差


class StopLossConfig(BaseModel):
    """止损配置"""
    
    enabled: bool = Field(default=False, description="是否启用止损")
    stop_type: StopType = Field(default=StopType.FIXED_RATIO, description="止损类型")
    
    # 固定比例止损
    stop_loss_ratio: float = Field(default=0.1, description="止损比例")
    
    # 移动止损
    trailing_ratio: float = Field(default=0.05, description="移动止损比例")
    trailing_start: float = Field(default=0.1, description="移动止损启动阈值")
    
    # ATR止损
    atr_period: int = Field(default=14, description="ATR周期")
    atr_multiplier: float = Field(default=2.0, description="ATR倍数")
    
    # 时间止损
    max_hold_days: Optional[int] = Field(default=None, description="最大持有天数")
    
    @validator('stop_loss_ratio', 'trailing_ratio', 'trailing_start')
    def validate_ratios(cls, v):
        """验证比例参数"""
        if v <= 0 or v >= 1:
            raise ValueError("比例参数必须在0-1之间")
        return v


class TakeProfitConfig(BaseModel):
    """止盈配置"""
    
    enabled: bool = Field(default=False, description="是否启用止盈")
    
    # 固定比例止盈
    take_profit_ratio: float = Field(default=0.2, description="止盈比例")
    
    # 分批止盈
    partial_profit: bool = Field(default=False, description="是否分批止盈")
    profit_levels: List[float] = Field(default_factory=lambda: [0.1, 0.2, 0.3], description="止盈水平")
    profit_ratios: List[float] = Field(default_factory=lambda: [0.3, 0.3, 0.4], description="止盈比例")
    
    @validator('take_profit_ratio')
    def validate_take_profit_ratio(cls, v):
        """验证止盈比例"""
        if v <= 0:
            raise ValueError("止盈比例必须大于0")
        return v
    
    @validator('profit_ratios')
    def validate_profit_ratios(cls, v, values):
        """验证分批止盈比例"""
        if 'profit_levels' in values and len(v) != len(values['profit_levels']):
            raise ValueError("止盈比例数量必须与止盈水平数量一致")
        if abs(sum(v) - 1.0) > 1e-6:
            raise ValueError("止盈比例总和必须等于1")
        return v


class PositionConfig(BaseModel):
    """仓位管理配置"""
    
    # 仓位计算方法
    size_method: PositionSizeMethod = Field(default=PositionSizeMethod.FIXED_RATIO, description="仓位计算方法")
    
    # 固定金额/比例
    fixed_amount: float = Field(default=10000.0, description="固定交易金额")
    fixed_ratio: float = Field(default=0.1, description="固定仓位比例")
    
    # 最大仓位限制
    max_position_ratio: float = Field(default=0.95, description="最大总仓位比例")
    max_single_position: float = Field(default=0.2, description="单个标的最大仓位")
    
    # 凯利公式参数
    kelly_lookback: int = Field(default=252, description="凯利公式回看期")
    kelly_multiplier: float = Field(default=0.25, description="凯利公式倍数")
    
    # 波动率目标
    target_volatility: float = Field(default=0.15, description="目标波动率")
    vol_lookback: int = Field(default=60, description="波动率计算回看期")
    
    # 风险平价
    risk_budget: Dict[str, float] = Field(default_factory=dict, description="风险预算分配")
    
    @validator('fixed_ratio', 'max_position_ratio', 'max_single_position')
    def validate_position_ratios(cls, v):
        """验证仓位比例"""
        if v <= 0 or v > 1:
            raise ValueError("仓位比例必须在0-1之间")
        return v
    
    @validator('target_volatility')
    def validate_target_volatility(cls, v):
        """验证目标波动率"""
        if v <= 0 or v > 1:
            raise ValueError("目标波动率必须在0-1之间")
        return v


class RiskLimitConfig(BaseModel):
    """风险限制配置"""
    
    # 回撤限制
    max_drawdown_limit: float = Field(default=0.2, description="最大回撤限制")
    daily_loss_limit: float = Field(default=0.05, description="单日最大亏损限制")
    
    # 波动率限制
    max_volatility: float = Field(default=0.3, description="最大波动率限制")
    
    # VaR限制
    var_confidence: float = Field(default=0.95, description="VaR置信度")
    var_limit: float = Field(default=0.05, description="VaR限制")
    
    # 集中度限制
    max_sector_exposure: float = Field(default=0.3, description="单个行业最大敞口")
    max_correlation: float = Field(default=0.8, description="最大相关性限制")
    
    # 流动性限制
    min_avg_volume: float = Field(default=1000000.0, description="最小平均成交量")
    max_volume_ratio: float = Field(default=0.1, description="最大成交量占比")
    
    @validator('max_drawdown_limit', 'daily_loss_limit', 'max_volatility', 'var_limit')
    def validate_risk_limits(cls, v):
        """验证风险限制"""
        if v <= 0 or v >= 1:
            raise ValueError("风险限制必须在0-1之间")
        return v
    
    @validator('var_confidence')
    def validate_var_confidence(cls, v):
        """验证VaR置信度"""
        if v <= 0 or v >= 1:
            raise ValueError("VaR置信度必须在0-1之间")
        return v


class RiskConfig(BaseModel):
    """风险管理总配置"""
    
    # 是否启用风险管理
    enabled: bool = Field(default=True, description="是否启用风险管理")
    
    # 子配置
    stop_loss: StopLossConfig = Field(default_factory=StopLossConfig)
    take_profit: TakeProfitConfig = Field(default_factory=TakeProfitConfig)
    position: PositionConfig = Field(default_factory=PositionConfig)
    limits: RiskLimitConfig = Field(default_factory=RiskLimitConfig)
    
    # 风险监控
    monitor_frequency: str = Field(default="daily", description="风险监控频率")
    alert_threshold: float = Field(default=0.8, description="风险预警阈值")
    
    # 应急措施
    emergency_stop: bool = Field(default=True, description="是否启用紧急停止")
    liquidation_threshold: float = Field(default=0.9, description="强制平仓阈值")
    
    def get_risk_metrics(self) -> List[RiskMetric]:
        """获取需要监控的风险指标"""
        metrics = [RiskMetric.MAX_DRAWDOWN, RiskMetric.VOLATILITY]
        
        if self.limits.var_limit > 0:
            metrics.extend([RiskMetric.VAR, RiskMetric.CVAR])
            
        return metrics


__all__ = [
    "RiskConfig",
    "StopLossConfig",
    "TakeProfitConfig", 
    "PositionConfig",
    "RiskLimitConfig",
    "StopType",
    "PositionSizeMethod",
    "RiskMetric"
]
