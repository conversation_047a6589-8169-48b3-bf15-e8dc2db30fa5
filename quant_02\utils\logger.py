"""
日志工具模块

提供统一的日志管理功能。
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self._loggers: Dict[str, logging.Logger] = {}
        self._initialized = False
        self._config = {
            'level': logging.INFO,
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S',
            'file_enabled': True,
            'console_enabled': True,
            'file_path': 'logs/quant.log',
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5,
            'colorize': True,
        }
    
    def init_logger(self, config: Optional[Dict[str, Any]] = None):
        """初始化日志系统"""
        if self._initialized:
            return
        
        # 更新配置
        if config:
            self._config.update(config)
        
        # 创建根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(self._config['level'])
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 添加控制台处理器
        if self._config['console_enabled']:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self._config['level'])
            
            if self._config['colorize']:
                console_formatter = ColoredFormatter(
                    self._config['format'],
                    datefmt=self._config['date_format']
                )
            else:
                console_formatter = logging.Formatter(
                    self._config['format'],
                    datefmt=self._config['date_format']
                )
            
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self._config['file_enabled']:
            # 确保日志目录存在
            log_file = Path(self._config['file_path'])
            log_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 使用RotatingFileHandler
            from logging.handlers import RotatingFileHandler
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=self._config['max_file_size'],
                backupCount=self._config['backup_count'],
                encoding='utf-8'
            )
            file_handler.setLevel(self._config['level'])
            
            file_formatter = logging.Formatter(
                self._config['format'],
                datefmt=self._config['date_format']
            )
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
        
        self._initialized = True
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志器"""
        if not self._initialized:
            self.init_logger()
        
        if name not in self._loggers:
            logger = logging.getLogger(name)
            self._loggers[name] = logger
        
        return self._loggers[name]
    
    def set_level(self, level: int):
        """设置日志级别"""
        self._config['level'] = level
        
        # 更新所有处理器的级别
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        for handler in root_logger.handlers:
            handler.setLevel(level)
    
    def enable_file_logging(self, enabled: bool = True):
        """启用/禁用文件日志"""
        self._config['file_enabled'] = enabled
        
        if self._initialized:
            # 重新初始化
            self._initialized = False
            self.init_logger()
    
    def enable_console_logging(self, enabled: bool = True):
        """启用/禁用控制台日志"""
        self._config['console_enabled'] = enabled
        
        if self._initialized:
            # 重新初始化
            self._initialized = False
            self.init_logger()
    
    def set_log_file(self, file_path: str):
        """设置日志文件路径"""
        self._config['file_path'] = file_path
        
        if self._initialized:
            # 重新初始化
            self._initialized = False
            self.init_logger()
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self._config.copy()


# 全局日志管理器实例
_logger_manager = LoggerManager()


def init_logger(config: Optional[Dict[str, Any]] = None):
    """初始化日志系统
    
    Args:
        config: 日志配置字典
    """
    _logger_manager.init_logger(config)


def get_logger(name: str) -> logging.Logger:
    """获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    return _logger_manager.get_logger(name)


def set_log_level(level: int):
    """设置日志级别
    
    Args:
        level: 日志级别
    """
    _logger_manager.set_level(level)


def enable_file_logging(enabled: bool = True):
    """启用/禁用文件日志
    
    Args:
        enabled: 是否启用
    """
    _logger_manager.enable_file_logging(enabled)


def enable_console_logging(enabled: bool = True):
    """启用/禁用控制台日志
    
    Args:
        enabled: 是否启用
    """
    _logger_manager.enable_console_logging(enabled)


def set_log_file(file_path: str):
    """设置日志文件路径
    
    Args:
        file_path: 文件路径
    """
    _logger_manager.set_log_file(file_path)


# 便捷的日志级别常量
DEBUG = logging.DEBUG
INFO = logging.INFO
WARNING = logging.WARNING
ERROR = logging.ERROR
CRITICAL = logging.CRITICAL


# 默认初始化
init_logger()
