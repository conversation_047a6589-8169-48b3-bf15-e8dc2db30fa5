"""
贝叶斯优化器

使用贝叶斯优化算法进行参数搜索，适合昂贵的目标函数优化。
"""

from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import pandas as pd
import numpy as np

from .base import BaseOptimizer, OptimizationResult
from ..utils.logger import get_logger

logger = get_logger(__name__)

try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False
    logger.warning("scikit-optimize 未安装，贝叶斯优化将使用简化实现")


class BayesianOptimizer(BaseOptimizer):
    """贝叶斯优化器"""
    
    def __init__(
        self,
        strategy_class: type,
        param_space: Dict[str, Union[List[Any], Dict[str, Any]]],
        objective: str = "sharpe_ratio",
        direction: str = "maximize",
        n_jobs: int = 1,
        random_state: Optional[int] = None,
        acquisition_function: str = "EI"
    ):
        """
        初始化贝叶斯优化器
        
        Args:
            strategy_class: 策略类
            param_space: 参数空间
            objective: 优化目标
            direction: 优化方向
            n_jobs: 并行任务数
            random_state: 随机种子
            acquisition_function: 采集函数 ('EI', 'PI', 'LCB')
        """
        super().__init__(strategy_class, param_space, objective, direction, n_jobs, random_state)
        
        self.acquisition_function = acquisition_function
        
        if SKOPT_AVAILABLE:
            self.search_space = self._build_search_space()
            logger.info("贝叶斯优化器初始化完成 (使用 scikit-optimize)")
        else:
            logger.info("贝叶斯优化器初始化完成 (使用简化实现)")
    
    def _build_search_space(self) -> List:
        """构建搜索空间"""
        if not SKOPT_AVAILABLE:
            return []
        
        space = []
        
        for param_name, param_config in self.param_space.items():
            if isinstance(param_config, list):
                # 离散值
                if all(isinstance(x, (int, float)) for x in param_config):
                    # 数值型离散值
                    if all(isinstance(x, int) for x in param_config):
                        space.append(Integer(min(param_config), max(param_config), name=param_name))
                    else:
                        space.append(Real(min(param_config), max(param_config), name=param_name))
                else:
                    # 分类型离散值
                    space.append(Categorical(param_config, name=param_name))
            
            elif isinstance(param_config, dict):
                # 连续分布
                dist_type = param_config.get('type', 'uniform')
                
                if dist_type == 'uniform':
                    low = param_config['low']
                    high = param_config['high']
                    if param_config.get('dtype') == 'int':
                        space.append(Integer(int(low), int(high), name=param_name))
                    else:
                        space.append(Real(low, high, name=param_name))
                
                elif dist_type == 'choice':
                    space.append(Categorical(param_config['choices'], name=param_name))
                
                else:
                    logger.warning(f"贝叶斯优化不支持分布类型 {dist_type}，使用默认范围")
                    space.append(Real(0, 1, name=param_name))
        
        return space
    
    def optimize(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame] = None,
        n_trials: int = 100,
        n_initial_points: int = 10,
        **kwargs
    ) -> OptimizationResult:
        """
        执行贝叶斯优化
        
        Args:
            data: 训练数据
            validation_data: 验证数据
            n_trials: 试验次数
            n_initial_points: 初始随机点数量
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        start_time = datetime.now()
        logger.info(f"开始贝叶斯优化: {n_trials} 次试验")
        
        if SKOPT_AVAILABLE and self.search_space:
            result = self._optimize_with_skopt(data, validation_data, n_trials, n_initial_points)
        else:
            result = self._optimize_simplified(data, validation_data, n_trials)
        
        # 计算优化时间
        optimization_time = (datetime.now() - start_time).total_seconds()
        result.optimization_time = optimization_time
        
        logger.info(f"贝叶斯优化完成: 最佳{self.objective} = {result.best_score:.4f}, 耗时: {optimization_time:.2f}秒")
        
        return result
    
    def _optimize_with_skopt(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame],
        n_trials: int,
        n_initial_points: int
    ) -> OptimizationResult:
        """使用 scikit-optimize 进行优化"""
        results = []
        
        @use_named_args(self.search_space)
        def objective_function(**params):
            try:
                # 评估训练集
                train_score = self._evaluate_strategy(params, data)
                
                # 评估验证集
                val_score = None
                if validation_data is not None:
                    val_score = self._evaluate_strategy(params, validation_data)
                
                score = val_score if val_score is not None else train_score
                
                # 记录结果
                results.append({
                    'params': params.copy(),
                    'train_score': train_score,
                    'val_score': val_score,
                    'score': score
                })
                
                # scikit-optimize 默认最小化，需要转换
                return -score if self.direction == 'maximize' else score
                
            except Exception as e:
                logger.warning(f"参数组合 {params} 评估失败: {e}")
                return float('inf')
        
        # 执行优化
        res = gp_minimize(
            func=objective_function,
            dimensions=self.search_space,
            n_calls=n_trials,
            n_initial_points=n_initial_points,
            acq_func=self.acquisition_function,
            random_state=self.random_state
        )
        
        return self._analyze_results(results, data, validation_data)
    
    def _optimize_simplified(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame],
        n_trials: int
    ) -> OptimizationResult:
        """简化的贝叶斯优化实现（随机搜索替代）"""
        logger.warning("使用随机搜索替代贝叶斯优化")
        
        from .random_search import RandomSearchOptimizer
        
        # 创建随机搜索优化器
        random_optimizer = RandomSearchOptimizer(
            strategy_class=self.strategy_class,
            param_space=self.param_space,
            objective=self.objective,
            direction=self.direction,
            n_jobs=self.n_jobs,
            random_state=self.random_state
        )
        
        return random_optimizer.optimize(data, validation_data, n_trials)
    
    def _analyze_results(
        self,
        results: List[Dict[str, Any]],
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> OptimizationResult:
        """分析优化结果"""
        if not results:
            raise ValueError("没有有效的优化结果")
        
        # 找到最佳结果
        best_result = max(results, key=lambda x: x['score']) if self.direction == 'maximize' else min(results, key=lambda x: x['score'])
        
        # 计算过拟合分数
        overfitting_score = None
        if validation_data is not None and best_result['val_score'] is not None:
            overfitting_score = self._calculate_overfitting_score(
                best_result['train_score'],
                best_result['val_score']
            )
        
        return OptimizationResult(
            best_params=best_result['params'],
            best_score=best_result['score'],
            objective=self.objective,
            direction=self.direction,
            n_trials=len(results),
            optimization_time=0,  # 将在外部设置
            all_results=results,
            validation_score=best_result.get('val_score'),
            overfitting_score=overfitting_score
        )
    
    def get_optimization_summary(self, result: OptimizationResult) -> Dict[str, Any]:
        """获取优化摘要"""
        summary = {
            'method': 'Bayesian Optimization',
            'acquisition_function': self.acquisition_function,
            'n_trials': result.n_trials,
            'best_params': result.best_params,
            'best_score': result.best_score,
            'objective': result.objective,
            'optimization_time': result.optimization_time,
            'skopt_available': SKOPT_AVAILABLE
        }
        
        if result.validation_score is not None:
            summary['validation_score'] = result.validation_score
            summary['overfitting_score'] = result.overfitting_score
        
        return summary
