"""
策略基类

定义量化交易策略的统一接口，所有具体策略都应该继承此基类。
"""

from abc import ABC, abstractmethod
from typing import Dict, Tuple, Any, Optional
import pandas as pd
import numpy as np


class BaseStrategy(ABC):
    """策略基类，定义策略接口"""
    
    def __init__(self, params: Optional[Dict[str, Any]] = None):
        """
        初始化策略
        
        参数:
            params (dict): 策略参数
        """
        self.params = params or {}
        self.name = self.__class__.__name__
        
    @staticmethod
    def get_params_schema() -> Dict[str, Any]:
        """
        获取策略参数的验证模式
        
        返回:
            dict: 参数验证模式，用于前端验证和参数说明
        """
        return {}
    
    @abstractmethod
    def run(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        运行策略，生成交易信号
        
        参数:
            data (pd.DataFrame): 输入数据，包含OHLCV列
            
        返回:
            tuple: (entries, exits) 买入和卖出信号的布尔数组
        """
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否符合要求
        
        参数:
            data (pd.DataFrame): 输入数据
            
        返回:
            bool: 数据是否有效
        """
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        # 检查必需的列是否存在
        if not all(col in data.columns for col in required_columns):
            return False
            
        # 检查数据是否为空
        if data.empty:
            return False
            
        # 检查数据长度是否足够
        if len(data) < 2:
            return False
            
        return True
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        返回:
            dict: 策略信息，包括名称、参数等
        """
        return {
            'name': self.name,
            'params': self.params,
            'params_schema': self.get_params_schema(),
            'description': self.__doc__ or "无描述"
        }
    
    def set_params(self, params: Dict[str, Any]):
        """
        设置策略参数
        
        参数:
            params (dict): 新的策略参数
        """
        self.params.update(params)
    
    def get_param(self, key: str, default: Any = None) -> Any:
        """
        获取策略参数
        
        参数:
            key (str): 参数名
            default (Any): 默认值
            
        返回:
            Any: 参数值
        """
        return self.params.get(key, default)
