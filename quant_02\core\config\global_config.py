"""
全局配置模块

定义系统的全局配置。
"""

from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import Field, validator

from .base import BaseConfig


class LoggingConfig(BaseConfig):
    """日志配置"""
    
    config_name: str = Field(default="logging", description="配置名称")
    
    # 基本设置
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        description="日志格式"
    )
    
    # 文件设置
    file_enabled: bool = Field(default=True, description="是否启用文件日志")
    file_path: str = Field(default="logs/quant.log", description="日志文件路径")
    file_rotation: str = Field(default="1 day", description="日志轮转")
    file_retention: str = Field(default="30 days", description="日志保留时间")
    
    # 控制台设置
    console_enabled: bool = Field(default=True, description="是否启用控制台日志")
    console_colorize: bool = Field(default=True, description="是否启用控制台颜色")
    
    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['TRACE', 'DEBUG', 'INFO', 'SUCCESS', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是: {valid_levels}")
        return v.upper()


class CacheConfig(BaseConfig):
    """缓存配置"""
    
    config_name: str = Field(default="cache", description="配置名称")
    
    # 基本设置
    enabled: bool = Field(default=True, description="是否启用缓存")
    default_ttl: int = Field(default=3600, description="默认TTL（秒）")
    
    # 内存缓存
    memory_enabled: bool = Field(default=True, description="是否启用内存缓存")
    memory_max_size: int = Field(default=1000, description="内存缓存最大条目数")
    
    # 磁盘缓存
    disk_enabled: bool = Field(default=True, description="是否启用磁盘缓存")
    disk_path: str = Field(default="cache", description="磁盘缓存路径")
    disk_max_size: str = Field(default="1GB", description="磁盘缓存最大大小")
    
    # Redis缓存
    redis_enabled: bool = Field(default=False, description="是否启用Redis缓存")
    redis_host: str = Field(default="localhost", description="Redis主机")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_db: int = Field(default=0, description="Redis数据库")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")


class DatabaseConfig(BaseConfig):
    """数据库配置"""
    
    config_name: str = Field(default="database", description="配置名称")
    
    # 基本设置
    enabled: bool = Field(default=False, description="是否启用数据库")
    url: str = Field(default="sqlite:///quant.db", description="数据库URL")
    
    # 连接池设置
    pool_size: int = Field(default=5, description="连接池大小")
    max_overflow: int = Field(default=10, description="最大溢出连接数")
    pool_timeout: int = Field(default=30, description="连接池超时时间")
    
    # 其他设置
    echo: bool = Field(default=False, description="是否打印SQL")
    autocommit: bool = Field(default=False, description="是否自动提交")
    autoflush: bool = Field(default=True, description="是否自动刷新")


class PerformanceConfig(BaseConfig):
    """性能配置"""
    
    config_name: str = Field(default="performance", description="配置名称")
    
    # 并行设置
    max_workers: int = Field(default=4, description="最大工作进程数")
    chunk_size: int = Field(default=1000, description="数据块大小")
    
    # 内存设置
    memory_limit: str = Field(default="2GB", description="内存限制")
    gc_threshold: int = Field(default=1000, description="垃圾回收阈值")
    
    # 计算设置
    use_numba: bool = Field(default=True, description="是否使用Numba加速")
    use_multiprocessing: bool = Field(default=True, description="是否使用多进程")
    use_threading: bool = Field(default=False, description="是否使用多线程")
    
    # 优化设置
    optimize_memory: bool = Field(default=True, description="是否优化内存使用")
    lazy_loading: bool = Field(default=True, description="是否使用懒加载")


class SecurityConfig(BaseConfig):
    """安全配置"""
    
    config_name: str = Field(default="security", description="配置名称")
    
    # API安全
    api_key_required: bool = Field(default=False, description="是否需要API密钥")
    api_key: Optional[str] = Field(default=None, description="API密钥")
    
    # 数据加密
    encrypt_data: bool = Field(default=False, description="是否加密数据")
    encryption_key: Optional[str] = Field(default=None, description="加密密钥")
    
    # 访问控制
    enable_rate_limit: bool = Field(default=False, description="是否启用访问限制")
    rate_limit_requests: int = Field(default=100, description="每分钟请求限制")


class GlobalConfig(BaseConfig):
    """全局配置"""
    
    # 基本信息
    config_name: str = Field(default="global", description="配置名称")
    app_name: str = Field(default="Quant_02", description="应用名称")
    app_version: str = Field(default="2.0.0", description="应用版本")
    
    # 环境设置
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=False, description="是否调试模式")
    
    # 路径设置
    data_dir: str = Field(default="data", description="数据目录")
    cache_dir: str = Field(default="cache", description="缓存目录")
    log_dir: str = Field(default="logs", description="日志目录")
    config_dir: str = Field(default="config", description="配置目录")
    output_dir: str = Field(default="output", description="输出目录")
    
    # 子配置
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    # 扩展配置
    extensions: Dict[str, Any] = Field(default_factory=dict, description="扩展配置")
    
    @validator('environment')
    def validate_environment(cls, v):
        valid_envs = ['development', 'testing', 'staging', 'production']
        if v.lower() not in valid_envs:
            raise ValueError(f"环境必须是: {valid_envs}")
        return v.lower()
    
    def is_development(self) -> bool:
        """是否开发环境"""
        return self.environment == 'development'
    
    def is_testing(self) -> bool:
        """是否测试环境"""
        return self.environment == 'testing'
    
    def is_staging(self) -> bool:
        """是否预发布环境"""
        return self.environment == 'staging'
    
    def is_production(self) -> bool:
        """是否生产环境"""
        return self.environment == 'production'
    
    def get_data_dir(self) -> Path:
        """获取数据目录路径"""
        return Path(self.data_dir)
    
    def get_cache_dir(self) -> Path:
        """获取缓存目录路径"""
        return Path(self.cache_dir)
    
    def get_log_dir(self) -> Path:
        """获取日志目录路径"""
        return Path(self.log_dir)
    
    def get_config_dir(self) -> Path:
        """获取配置目录路径"""
        return Path(self.config_dir)
    
    def get_output_dir(self) -> Path:
        """获取输出目录路径"""
        return Path(self.output_dir)
    
    def ensure_directories(self):
        """确保所有目录存在"""
        directories = [
            self.data_dir,
            self.cache_dir,
            self.log_dir,
            self.config_dir,
            self.output_dir,
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def create_default(cls) -> 'GlobalConfig':
        """创建默认配置"""
        config = cls()
        config.ensure_directories()
        return config
    
    @classmethod
    def load_from_env(cls) -> 'GlobalConfig':
        """从环境变量加载配置"""
        config = cls.from_env(prefix="QUANT_")
        config.ensure_directories()
        return config
    
    def get_runtime_info(self) -> Dict[str, Any]:
        """获取运行时信息"""
        return {
            'app_name': self.app_name,
            'app_version': self.app_version,
            'environment': self.environment,
            'debug': self.debug,
            'directories': {
                'data': str(self.get_data_dir()),
                'cache': str(self.get_cache_dir()),
                'log': str(self.get_log_dir()),
                'config': str(self.get_config_dir()),
                'output': str(self.get_output_dir()),
            },
            'features': {
                'cache_enabled': self.cache.enabled,
                'database_enabled': self.database.enabled,
                'redis_enabled': self.cache.redis_enabled,
                'numba_enabled': self.performance.use_numba,
                'multiprocessing_enabled': self.performance.use_multiprocessing,
            }
        }


# 全局配置实例
global_config = GlobalConfig.create_default()
