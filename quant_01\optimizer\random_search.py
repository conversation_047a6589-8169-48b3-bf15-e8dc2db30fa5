"""
随机搜索优化器

通过随机采样参数组合来寻找最优参数，适合大参数空间的优化。
"""

import random
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import pandas as pd
import numpy as np

from .base import BaseOptimizer, OptimizationResult
from ..utils.logger import get_logger

logger = get_logger(__name__)


class RandomSearchOptimizer(BaseOptimizer):
    """随机搜索优化器"""
    
    def __init__(
        self,
        strategy_class: type,
        param_space: Dict[str, Union[List[Any], Dict[str, Any]]],
        objective: str = "sharpe_ratio",
        direction: str = "maximize",
        n_jobs: int = 1,
        random_state: Optional[int] = None
    ):
        """
        初始化随机搜索优化器
        
        Args:
            strategy_class: 策略类
            param_space: 参数空间，支持离散值列表或连续分布字典
            objective: 优化目标
            direction: 优化方向
            n_jobs: 并行任务数
            random_state: 随机种子
        """
        super().__init__(strategy_class, param_space, objective, direction, n_jobs, random_state)
        
        # 设置随机种子
        if random_state is not None:
            random.seed(random_state)
            np.random.seed(random_state)
        
        logger.info("随机搜索优化器初始化完成")
    
    def optimize(
        self,
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame] = None,
        n_trials: int = 100,
        early_stopping: bool = False,
        patience: int = 20,
        **kwargs
    ) -> OptimizationResult:
        """
        执行随机搜索优化
        
        Args:
            data: 训练数据
            validation_data: 验证数据
            n_trials: 试验次数
            early_stopping: 是否启用早停
            patience: 早停耐心值
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        start_time = datetime.now()
        logger.info(f"开始随机搜索优化: {n_trials} 次试验")
        
        results = []
        best_score = self._get_initial_best_score()
        no_improvement_count = 0
        
        for i in range(n_trials):
            try:
                # 随机采样参数
                params = self._sample_params()
                
                # 评估训练集
                train_score = self._evaluate_strategy(params, data)
                
                # 评估验证集
                val_score = None
                if validation_data is not None:
                    val_score = self._evaluate_strategy(params, validation_data)
                
                result = {
                    'params': params,
                    'train_score': train_score,
                    'val_score': val_score,
                    'score': val_score if val_score is not None else train_score
                }
                
                results.append(result)
                
                # 早停检查
                if early_stopping:
                    current_score = val_score if val_score is not None else train_score
                    if self._is_better_score(current_score, best_score):
                        best_score = current_score
                        no_improvement_count = 0
                    else:
                        no_improvement_count += 1
                    
                    if no_improvement_count >= patience:
                        logger.info(f"早停触发: {patience} 次无改善")
                        break
                
                # 进度报告
                if (i + 1) % 20 == 0:
                    logger.info(f"随机搜索进度: {i + 1}/{n_trials}")
                
            except Exception as e:
                logger.warning(f"第 {i+1} 次试验失败: {e}")
                continue
        
        # 分析结果
        optimization_result = self._analyze_results(results, data, validation_data)
        
        # 计算优化时间
        optimization_time = (datetime.now() - start_time).total_seconds()
        optimization_result.optimization_time = optimization_time
        
        logger.info(f"随机搜索完成: 最佳{self.objective} = {optimization_result.best_score:.4f}, 耗时: {optimization_time:.2f}秒")
        
        return optimization_result
    
    def _sample_params(self) -> Dict[str, Any]:
        """随机采样参数"""
        params = {}
        
        for param_name, param_config in self.param_space.items():
            if isinstance(param_config, list):
                # 离散值列表
                params[param_name] = random.choice(param_config)
            elif isinstance(param_config, dict):
                # 连续分布
                params[param_name] = self._sample_continuous_param(param_config)
            else:
                raise ValueError(f"不支持的参数配置类型: {type(param_config)}")
        
        return params
    
    def _sample_continuous_param(self, config: Dict[str, Any]) -> Any:
        """采样连续参数"""
        dist_type = config.get('type', 'uniform')
        
        if dist_type == 'uniform':
            low = config['low']
            high = config['high']
            if config.get('dtype') == 'int':
                return random.randint(int(low), int(high))
            else:
                return random.uniform(low, high)
        
        elif dist_type == 'normal':
            mean = config['mean']
            std = config['std']
            value = np.random.normal(mean, std)
            if config.get('dtype') == 'int':
                return int(round(value))
            return value
        
        elif dist_type == 'lognormal':
            mean = config['mean']
            sigma = config['sigma']
            value = np.random.lognormal(mean, sigma)
            if config.get('dtype') == 'int':
                return int(round(value))
            return value
        
        elif dist_type == 'choice':
            return random.choice(config['choices'])
        
        else:
            raise ValueError(f"不支持的分布类型: {dist_type}")
    
    def _analyze_results(
        self,
        results: List[Dict[str, Any]],
        data: pd.DataFrame,
        validation_data: Optional[pd.DataFrame]
    ) -> OptimizationResult:
        """分析优化结果"""
        if not results:
            raise ValueError("没有有效的优化结果")
        
        # 找到最佳结果
        best_result = max(results, key=lambda x: x['score']) if self.direction == 'maximize' else min(results, key=lambda x: x['score'])
        
        # 计算过拟合分数
        overfitting_score = None
        if validation_data is not None and best_result['val_score'] is not None:
            overfitting_score = self._calculate_overfitting_score(
                best_result['train_score'],
                best_result['val_score']
            )
        
        return OptimizationResult(
            best_params=best_result['params'],
            best_score=best_result['score'],
            objective=self.objective,
            direction=self.direction,
            n_trials=len(results),
            optimization_time=0,  # 将在外部设置
            all_results=results,
            validation_score=best_result.get('val_score'),
            overfitting_score=overfitting_score
        )
    
    def get_optimization_summary(self, result: OptimizationResult) -> Dict[str, Any]:
        """获取优化摘要"""
        summary = {
            'method': 'Random Search',
            'n_trials': result.n_trials,
            'best_params': result.best_params,
            'best_score': result.best_score,
            'objective': result.objective,
            'optimization_time': result.optimization_time
        }
        
        if result.validation_score is not None:
            summary['validation_score'] = result.validation_score
            summary['overfitting_score'] = result.overfitting_score
        
        # 参数重要性
        param_importance = self.get_param_importance(result.all_results)
        summary['param_importance'] = param_importance
        
        return summary
