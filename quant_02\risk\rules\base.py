"""
风险规则基类模块

定义风险规则的基础接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from core.structures import Order, Position, Portfolio
from utils.logger import get_logger

logger = get_logger(__name__)


class RiskLevel(Enum):
    """风险级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskRuleResult:
    """风险规则检查结果"""
    
    passed: bool
    risk_level: RiskLevel
    message: str
    details: Dict[str, Any]
    rule_name: str
    timestamp: datetime
    
    @classmethod
    def pass_result(cls, rule_name: str, message: str = "检查通过") -> 'RiskRuleResult':
        """创建通过结果"""
        return cls(
            passed=True,
            risk_level=RiskLevel.LOW,
            message=message,
            details={},
            rule_name=rule_name,
            timestamp=datetime.now()
        )
    
    @classmethod
    def fail_result(
        cls, 
        rule_name: str, 
        message: str, 
        risk_level: RiskLevel = RiskLevel.HIGH,
        details: Optional[Dict[str, Any]] = None
    ) -> 'RiskRuleResult':
        """创建失败结果"""
        return cls(
            passed=False,
            risk_level=risk_level,
            message=message,
            details=details or {},
            rule_name=rule_name,
            timestamp=datetime.now()
        )


class BaseRiskRule(ABC):
    """风险规则基类
    
    所有风险规则都应该继承此类。
    """
    
    def __init__(self, name: str, enabled: bool = True, **kwargs):
        """
        初始化风险规则
        
        Args:
            name: 规则名称
            enabled: 是否启用
            **kwargs: 其他参数
        """
        self.name = name
        self.enabled = enabled
        self.parameters = kwargs
        
        # 统计信息
        self._stats = {
            'checks': 0,
            'passes': 0,
            'failures': 0,
            'last_check': None
        }
        
        logger.debug(f"风险规则初始化: {self.name}")
    
    @abstractmethod
    def check_order(self, order: Order, portfolio: Portfolio) -> RiskRuleResult:
        """
        检查订单
        
        Args:
            order: 订单
            portfolio: 投资组合
            
        Returns:
            检查结果
        """
        pass
    
    @abstractmethod
    def check_position(self, position: Position, portfolio: Portfolio) -> RiskRuleResult:
        """
        检查持仓
        
        Args:
            position: 持仓
            portfolio: 投资组合
            
        Returns:
            检查结果
        """
        pass
    
    @abstractmethod
    def check_portfolio(self, portfolio: Portfolio) -> RiskRuleResult:
        """
        检查投资组合
        
        Args:
            portfolio: 投资组合
            
        Returns:
            检查结果
        """
        pass
    
    def is_enabled(self) -> bool:
        """是否启用"""
        return self.enabled
    
    def enable(self):
        """启用规则"""
        self.enabled = True
        logger.info(f"风险规则已启用: {self.name}")
    
    def disable(self):
        """禁用规则"""
        self.enabled = False
        logger.info(f"风险规则已禁用: {self.name}")
    
    def get_parameter(self, name: str, default: Any = None) -> Any:
        """获取参数"""
        return self.parameters.get(name, default)
    
    def set_parameter(self, name: str, value: Any):
        """设置参数"""
        self.parameters[name] = value
        logger.debug(f"风险规则参数更新: {self.name}.{name} = {value}")
    
    def update_parameters(self, parameters: Dict[str, Any]):
        """更新参数"""
        self.parameters.update(parameters)
        logger.debug(f"风险规则参数批量更新: {self.name}")
    
    def _update_stats(self, result: RiskRuleResult):
        """更新统计信息"""
        self._stats['checks'] += 1
        self._stats['last_check'] = result.timestamp
        
        if result.passed:
            self._stats['passes'] += 1
        else:
            self._stats['failures'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self._stats.copy()
        
        if stats['checks'] > 0:
            stats['pass_rate'] = stats['passes'] / stats['checks']
            stats['failure_rate'] = stats['failures'] / stats['checks']
        else:
            stats['pass_rate'] = 0.0
            stats['failure_rate'] = 0.0
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self._stats = {
            'checks': 0,
            'passes': 0,
            'failures': 0,
            'last_check': None
        }
        logger.debug(f"风险规则统计信息已重置: {self.name}")
    
    def __str__(self) -> str:
        return f"{self.name}(enabled={self.enabled})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}: {self.name}>"


class RiskRuleEngine:
    """风险规则引擎
    
    管理和执行多个风险规则。
    """
    
    def __init__(self):
        self.rules: Dict[str, BaseRiskRule] = {}
        
        logger.info("风险规则引擎初始化完成")
    
    def add_rule(self, rule: BaseRiskRule):
        """添加风险规则"""
        self.rules[rule.name] = rule
        logger.info(f"风险规则已添加: {rule.name}")
    
    def remove_rule(self, name: str):
        """移除风险规则"""
        if name in self.rules:
            del self.rules[name]
            logger.info(f"风险规则已移除: {name}")
    
    def get_rule(self, name: str) -> Optional[BaseRiskRule]:
        """获取风险规则"""
        return self.rules.get(name)
    
    def list_rules(self) -> Dict[str, BaseRiskRule]:
        """列出所有规则"""
        return self.rules.copy()
    
    def check_order(self, order: Order, portfolio: Portfolio) -> Dict[str, RiskRuleResult]:
        """检查订单的所有规则"""
        results = {}
        
        for name, rule in self.rules.items():
            if rule.is_enabled():
                try:
                    result = rule.check_order(order, portfolio)
                    rule._update_stats(result)
                    results[name] = result
                    
                    if not result.passed:
                        logger.warning(f"订单风险规则失败: {name} - {result.message}")
                        
                except Exception as e:
                    logger.error(f"风险规则检查失败: {name} - {e}")
                    results[name] = RiskRuleResult.fail_result(
                        name, f"规则执行失败: {e}", RiskLevel.CRITICAL
                    )
        
        return results
    
    def check_position(self, position: Position, portfolio: Portfolio) -> Dict[str, RiskRuleResult]:
        """检查持仓的所有规则"""
        results = {}
        
        for name, rule in self.rules.items():
            if rule.is_enabled():
                try:
                    result = rule.check_position(position, portfolio)
                    rule._update_stats(result)
                    results[name] = result
                    
                    if not result.passed:
                        logger.warning(f"持仓风险规则失败: {name} - {result.message}")
                        
                except Exception as e:
                    logger.error(f"风险规则检查失败: {name} - {e}")
                    results[name] = RiskRuleResult.fail_result(
                        name, f"规则执行失败: {e}", RiskLevel.CRITICAL
                    )
        
        return results
    
    def check_portfolio(self, portfolio: Portfolio) -> Dict[str, RiskRuleResult]:
        """检查投资组合的所有规则"""
        results = {}
        
        for name, rule in self.rules.items():
            if rule.is_enabled():
                try:
                    result = rule.check_portfolio(portfolio)
                    rule._update_stats(result)
                    results[name] = result
                    
                    if not result.passed:
                        logger.warning(f"投资组合风险规则失败: {name} - {result.message}")
                        
                except Exception as e:
                    logger.error(f"风险规则检查失败: {name} - {e}")
                    results[name] = RiskRuleResult.fail_result(
                        name, f"规则执行失败: {e}", RiskLevel.CRITICAL
                    )
        
        return results
    
    def has_critical_failures(self, results: Dict[str, RiskRuleResult]) -> bool:
        """是否有严重失败"""
        return any(
            not result.passed and result.risk_level == RiskLevel.CRITICAL 
            for result in results.values()
        )
    
    def get_failed_rules(self, results: Dict[str, RiskRuleResult]) -> Dict[str, RiskRuleResult]:
        """获取失败的规则"""
        return {name: result for name, result in results.items() if not result.passed}
    
    def get_engine_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            'total_rules': len(self.rules),
            'enabled_rules': len([r for r in self.rules.values() if r.is_enabled()]),
            'rule_stats': {name: rule.get_stats() for name, rule in self.rules.items()}
        }
