"""
测试参数优化功能

简单测试脚本，验证优化器是否正常工作。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    # 导入核心模块
    from strategies.single.macd import MACDStrategy
    from dataseed.mock_source import MockDataSource
    from utils.logger import init_logger, get_logger

    # 尝试导入优化器
    try:
        from optimizer.manager import optimization_manager
    except ImportError:
        print("优化器模块导入失败，尝试直接导入...")
        from optimizer.grid_search import GridSearchOptimizer
        from optimizer.random_search import RandomSearchOptimizer
        from optimizer.manager import OptimizationManager
        optimization_manager = OptimizationManager()

    # 初始化日志
    init_logger()
    logger = get_logger(__name__)

    def test_grid_search():
        """测试网格搜索"""
        logger.info("测试网格搜索优化...")

        # 创建模拟数据源
        data_source = MockDataSource(name="test_mock")
        data = data_source.get_data("TEST", "2023-01-01", "2023-12-31")

        # 定义参数空间
        param_space = {
            'fast_period': [8, 12],
            'slow_period': [21, 26],
            'signal_period': [5, 9]
        }

        # 执行优化
        result = optimization_manager.optimize(
            strategy_class=MACDStrategy,
            param_space=param_space,
            data=data,
            method='grid',
            objective='sharpe_ratio',
            direction='maximize',
            validation_split=0.2
        )

        logger.info(f"网格搜索完成: 最佳参数 = {result.best_params}")
        logger.info(f"最佳分数 = {result.best_score:.4f}")
        logger.info(f"试验次数 = {result.n_trials}")

        return result

    def test_random_search():
        """测试随机搜索"""
        logger.info("测试随机搜索优化...")

        # 创建模拟数据源
        data_source = MockDataSource(name="test_mock")
        data = data_source.get_data("TEST", "2023-01-01", "2023-12-31")

        # 定义参数空间
        param_space = {
            'fast_period': {
                'type': 'uniform',
                'low': 5,
                'high': 20,
                'dtype': 'int'
            },
            'slow_period': {
                'type': 'uniform',
                'low': 20,
                'high': 50,
                'dtype': 'int'
            },
            'signal_period': {
                'type': 'uniform',
                'low': 3,
                'high': 15,
                'dtype': 'int'
            }
        }

        # 执行优化
        result = optimization_manager.optimize(
            strategy_class=MACDStrategy,
            param_space=param_space,
            data=data,
            method='random',
            objective='sharpe_ratio',
            direction='maximize',
            validation_split=0.2,
            n_trials=20
        )

        logger.info(f"随机搜索完成: 最佳参数 = {result.best_params}")
        logger.info(f"最佳分数 = {result.best_score:.4f}")
        logger.info(f"试验次数 = {result.n_trials}")

        return result

    def test_method_comparison():
        """测试方法比较"""
        logger.info("测试优化方法比较...")

        # 创建模拟数据源
        data_source = MockDataSource(name="test_mock")
        data = data_source.get_data("TEST", "2023-01-01", "2023-12-31")

        # 定义参数空间
        param_space = {
            'fast_period': [8, 12],
            'slow_period': [21, 26],
            'signal_period': [5, 9]
        }

        # 比较方法
        results = optimization_manager.compare_methods(
            strategy_class=MACDStrategy,
            param_space=param_space,
            data=data,
            methods=['grid', 'random'],
            objective='sharpe_ratio',
            direction='maximize',
            n_trials=10
        )

        logger.info("方法比较完成:")
        for method, result in results.items():
            logger.info(f"  {method}: 分数 = {result.best_score:.4f}, 时间 = {result.optimization_time:.2f}s")

        return results

    def main():
        """主测试函数"""
        logger.info("=" * 60)
        logger.info("参数优化功能测试")
        logger.info("=" * 60)

        try:
            # 测试网格搜索
            logger.info("\n1. 网格搜索测试")
            logger.info("-" * 30)
            test_grid_search()

            # 测试随机搜索
            logger.info("\n2. 随机搜索测试")
            logger.info("-" * 30)
            test_random_search()

            # 测试方法比较
            logger.info("\n3. 方法比较测试")
            logger.info("-" * 30)
            test_method_comparison()

            # 显示优化历史
            history = optimization_manager.get_optimization_history()
            logger.info(f"\n优化历史: 总共执行了 {len(history)} 次优化")

            logger.info("\n=" * 60)
            logger.info("所有测试完成！")
            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            raise

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在quant_01目录下运行此脚本")
    sys.exit(1)
except Exception as e:
    print(f"运行错误: {e}")
    sys.exit(1)
