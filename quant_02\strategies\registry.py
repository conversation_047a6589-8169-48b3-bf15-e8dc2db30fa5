"""
策略注册器模块

提供策略的注册、发现和管理功能。
"""

from typing import Dict, Type, Any, Optional, List, Callable
import inspect
from functools import wraps

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from .base.strategy import BaseStrategy, StrategyConfig
from .factory import strategy_factory
from core.exceptions import StrategyException
from utils.logger import get_logger

logger = get_logger(__name__)


class StrategyRegistry:
    """策略注册器
    
    提供策略的注册、发现和管理功能。
    """
    
    def __init__(self):
        self._registry: Dict[str, Dict[str, Any]] = {}
        self._categories: Dict[str, List[str]] = {}
        self._tags: Dict[str, List[str]] = {}
        
        logger.info("策略注册器初始化完成")
    
    def register(
        self,
        name: str,
        strategy_class: Type[BaseStrategy],
        config_class: Optional[Type[StrategyConfig]] = None,
        category: str = "general",
        tags: Optional[List[str]] = None,
        description: str = "",
        author: str = "",
        version: str = "1.0.0"
    ):
        """注册策略
        
        Args:
            name: 策略名称
            strategy_class: 策略类
            config_class: 配置类
            category: 策略分类
            tags: 标签列表
            description: 描述
            author: 作者
            version: 版本
        """
        # 注册到工厂
        strategy_factory.register(name, strategy_class, config_class)
        
        # 注册到注册器
        self._registry[name] = {
            'strategy_class': strategy_class,
            'config_class': config_class or StrategyConfig,
            'category': category,
            'tags': tags or [],
            'description': description,
            'author': author,
            'version': version,
            'registered_at': None,  # 可以添加时间戳
        }
        
        # 更新分类索引
        if category not in self._categories:
            self._categories[category] = []
        self._categories[category].append(name)
        
        # 更新标签索引
        for tag in (tags or []):
            if tag not in self._tags:
                self._tags[tag] = []
            self._tags[tag].append(name)
        
        logger.info(f"策略已注册到注册器: {name} (分类: {category})")
    
    def unregister(self, name: str):
        """注销策略"""
        if name in self._registry:
            strategy_info = self._registry[name]
            
            # 从工厂注销
            strategy_factory.unregister(name)
            
            # 从分类索引移除
            category = strategy_info['category']
            if category in self._categories and name in self._categories[category]:
                self._categories[category].remove(name)
                if not self._categories[category]:
                    del self._categories[category]
            
            # 从标签索引移除
            for tag in strategy_info['tags']:
                if tag in self._tags and name in self._tags[tag]:
                    self._tags[tag].remove(name)
                    if not self._tags[tag]:
                        del self._tags[tag]
            
            # 从注册器移除
            del self._registry[name]
            
            logger.info(f"策略已从注册器注销: {name}")
    
    def get(self, name: str) -> Optional[Type[BaseStrategy]]:
        """获取策略类"""
        if name in self._registry:
            return self._registry[name]['strategy_class']
        return None
    
    def get_info(self, name: str) -> Optional[Dict[str, Any]]:
        """获取策略信息"""
        return self._registry.get(name)
    
    def list_all(self) -> List[str]:
        """列出所有策略"""
        return list(self._registry.keys())
    
    def list_by_category(self, category: str) -> List[str]:
        """按分类列出策略"""
        return self._categories.get(category, [])
    
    def list_by_tag(self, tag: str) -> List[str]:
        """按标签列出策略"""
        return self._tags.get(tag, [])
    
    def get_categories(self) -> List[str]:
        """获取所有分类"""
        return list(self._categories.keys())
    
    def get_tags(self) -> List[str]:
        """获取所有标签"""
        return list(self._tags.keys())
    
    def search(
        self,
        keyword: str = "",
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        author: Optional[str] = None
    ) -> List[str]:
        """搜索策略
        
        Args:
            keyword: 关键词（在名称和描述中搜索）
            category: 分类过滤
            tags: 标签过滤
            author: 作者过滤
            
        Returns:
            匹配的策略名称列表
        """
        results = []
        
        for name, info in self._registry.items():
            # 关键词匹配
            if keyword:
                if (keyword.lower() not in name.lower() and 
                    keyword.lower() not in info['description'].lower()):
                    continue
            
            # 分类过滤
            if category and info['category'] != category:
                continue
            
            # 标签过滤
            if tags:
                if not any(tag in info['tags'] for tag in tags):
                    continue
            
            # 作者过滤
            if author and info['author'] != author:
                continue
            
            results.append(name)
        
        return results
    
    def get_summary(self) -> Dict[str, Any]:
        """获取注册器摘要"""
        return {
            'total_strategies': len(self._registry),
            'categories': {cat: len(strategies) for cat, strategies in self._categories.items()},
            'tags': {tag: len(strategies) for tag, strategies in self._tags.items()},
            'strategies': list(self._registry.keys()),
        }
    
    def validate_strategy(self, strategy_class: Type[BaseStrategy]) -> bool:
        """验证策略类"""
        try:
            # 检查是否继承自BaseStrategy
            if not issubclass(strategy_class, BaseStrategy):
                return False
            
            # 检查必要方法
            required_methods = ['generate_signals', 'get_name', 'get_parameters']
            for method in required_methods:
                if not hasattr(strategy_class, method):
                    return False
            
            return True
            
        except Exception:
            return False
    
    def clear(self):
        """清空注册器"""
        self._registry.clear()
        self._categories.clear()
        self._tags.clear()
        strategy_factory.clear()
        logger.info("策略注册器已清空")


# 全局策略注册器实例
strategy_registry = StrategyRegistry()


# 装饰器函数
def register_strategy(
    name: Optional[str] = None,
    category: str = "general",
    tags: Optional[List[str]] = None,
    description: str = "",
    author: str = "",
    version: str = "1.0.0"
):
    """策略注册装饰器
    
    Args:
        name: 策略名称，如果为None则使用类名
        category: 策略分类
        tags: 标签列表
        description: 描述
        author: 作者
        version: 版本
    """
    def decorator(cls):
        strategy_name = name or cls.__name__.lower().replace('strategy', '')
        
        # 查找配置类
        config_class = None
        config_name = cls.__name__.replace('Strategy', 'Config')
        
        # 在同一模块中查找配置类
        module = inspect.getmodule(cls)
        if hasattr(module, config_name):
            config_class = getattr(module, config_name)
        
        strategy_registry.register(
            strategy_name,
            cls,
            config_class,
            category,
            tags,
            description,
            author,
            version
        )
        
        return cls
    
    return decorator


# 便捷函数
def get_strategy(name: str) -> Optional[Type[BaseStrategy]]:
    """获取策略类的便捷函数"""
    return strategy_registry.get(name)


def list_strategies_by_category(category: str) -> List[str]:
    """按分类列出策略的便捷函数"""
    return strategy_registry.list_by_category(category)


def search_strategies(keyword: str) -> List[str]:
    """搜索策略的便捷函数"""
    return strategy_registry.search(keyword)
