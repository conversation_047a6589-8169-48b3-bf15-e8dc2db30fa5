"""
简化的参数优化器

直接集成到main.py中使用，避免复杂的导入问题。
"""

import itertools
import random
from typing import Dict, List, Any, Optional
from datetime import datetime
import pandas as pd
import numpy as np


class SimpleOptimizationResult:
    """简化的优化结果"""

    def __init__(self, best_params, best_score, n_trials, optimization_time, all_results):
        self.best_params = best_params
        self.best_score = best_score
        self.n_trials = n_trials
        self.optimization_time = optimization_time
        self.all_results = all_results
        self.validation_score = None
        self.overfitting_score = None


class SimpleGridOptimizer:
    """简化的网格搜索优化器"""

    def __init__(self, strategy_class, param_space, objective='sharpe_ratio', direction='maximize'):
        self.strategy_class = strategy_class
        self.param_space = param_space
        self.objective = objective
        self.direction = direction

    def optimize(self, data, validation_data=None, max_combinations=1000):
        """执行网格搜索优化"""
        start_time = datetime.now()

        # 生成所有参数组合
        keys = list(self.param_space.keys())
        values = list(self.param_space.values())
        combinations = list(itertools.product(*values))

        # 限制组合数量
        if len(combinations) > max_combinations:
            print(f"参数组合数量 {len(combinations)} 超过限制 {max_combinations}，随机采样")
            combinations = random.sample(combinations, max_combinations)

        print(f"开始网格搜索: {len(combinations)} 个参数组合")

        best_score = float('-inf') if self.direction == 'maximize' else float('inf')
        best_params = None
        all_results = []

        for i, combination in enumerate(combinations):
            params = dict(zip(keys, combination))

            try:
                # 评估参数组合
                train_score = self._evaluate_strategy(params, data)

                val_score = None
                if validation_data is not None:
                    val_score = self._evaluate_strategy(params, validation_data)

                score = val_score if val_score is not None else train_score

                all_results.append({
                    'params': params,
                    'train_score': train_score,
                    'val_score': val_score,
                    'score': score
                })

                # 更新最佳结果
                if self._is_better_score(score, best_score):
                    best_score = score
                    best_params = params

                # 进度报告
                if (i + 1) % 10 == 0:
                    print(f"优化进度: {i + 1}/{len(combinations)}")

            except Exception as e:
                print(f"参数组合 {params} 评估失败: {e}")
                continue

        # 计算优化时间
        optimization_time = (datetime.now() - start_time).total_seconds()

        result = SimpleOptimizationResult(
            best_params=best_params,
            best_score=best_score,
            n_trials=len(all_results),
            optimization_time=optimization_time,
            all_results=all_results
        )

        # 计算验证分数和过拟合分数
        if validation_data is not None and best_params is not None:
            best_result = next((r for r in all_results if r['params'] == best_params), None)
            if best_result and best_result['val_score'] is not None:
                result.validation_score = best_result['val_score']
                result.overfitting_score = self._calculate_overfitting_score(
                    best_result['train_score'],
                    best_result['val_score']
                )

        return result

    def _evaluate_strategy(self, params, data):
        """评估策略参数"""
        try:
            # 创建策略配置
            from strategies.single.macd import MACDConfig
            config = MACDConfig(**params)
            strategy = self.strategy_class(config)

            # 运行策略
            result = strategy.run(data)

            # 获取目标值
            score = getattr(result, self.objective, 0)
            return float(score)

        except Exception as e:
            print(f"策略评估失败: {e}")
            return float('-inf') if self.direction == 'maximize' else float('inf')

    def _is_better_score(self, new_score, best_score):
        """判断新分数是否更好"""
        if self.direction == 'maximize':
            return new_score > best_score
        else:
            return new_score < best_score

    def _calculate_overfitting_score(self, train_score, val_score):
        """计算过拟合分数"""
        if val_score == 0:
            return float('inf')

        if self.direction == 'maximize':
            return (train_score - val_score) / abs(val_score)
        else:
            return (val_score - train_score) / abs(val_score)


class SimpleRandomOptimizer:
    """简化的随机搜索优化器"""

    def __init__(self, strategy_class, param_space, objective='sharpe_ratio', direction='maximize'):
        self.strategy_class = strategy_class
        self.param_space = param_space
        self.objective = objective
        self.direction = direction

    def optimize(self, data, validation_data=None, n_trials=50):
        """执行随机搜索优化"""
        start_time = datetime.now()

        print(f"开始随机搜索: {n_trials} 次试验")

        best_score = float('-inf') if self.direction == 'maximize' else float('inf')
        best_params = None
        all_results = []

        for i in range(n_trials):
            try:
                # 随机采样参数
                params = self._sample_params()

                # 评估参数组合
                train_score = self._evaluate_strategy(params, data)

                val_score = None
                if validation_data is not None:
                    val_score = self._evaluate_strategy(params, validation_data)

                score = val_score if val_score is not None else train_score

                all_results.append({
                    'params': params,
                    'train_score': train_score,
                    'val_score': val_score,
                    'score': score
                })

                # 更新最佳结果
                if self._is_better_score(score, best_score):
                    best_score = score
                    best_params = params

                # 进度报告
                if (i + 1) % 10 == 0:
                    print(f"随机搜索进度: {i + 1}/{n_trials}")

            except Exception as e:
                print(f"第 {i+1} 次试验失败: {e}")
                continue

        # 计算优化时间
        optimization_time = (datetime.now() - start_time).total_seconds()

        result = SimpleOptimizationResult(
            best_params=best_params,
            best_score=best_score,
            n_trials=len(all_results),
            optimization_time=optimization_time,
            all_results=all_results
        )

        # 计算验证分数和过拟合分数
        if validation_data is not None and best_params is not None:
            best_result = next((r for r in all_results if r['params'] == best_params), None)
            if best_result and best_result['val_score'] is not None:
                result.validation_score = best_result['val_score']
                result.overfitting_score = self._calculate_overfitting_score(
                    best_result['train_score'],
                    best_result['val_score']
                )

        return result

    def _sample_params(self):
        """随机采样参数"""
        params = {}
        for param_name, param_config in self.param_space.items():
            if isinstance(param_config, list):
                params[param_name] = random.choice(param_config)
            elif isinstance(param_config, dict):
                # 简化处理，只支持uniform分布
                if param_config.get('type') == 'uniform':
                    low = param_config['low']
                    high = param_config['high']
                    if param_config.get('dtype') == 'int':
                        params[param_name] = random.randint(int(low), int(high))
                    else:
                        params[param_name] = random.uniform(low, high)
                else:
                    params[param_name] = random.choice(param_config.get('choices', [0, 1]))
        return params

    def _evaluate_strategy(self, params, data):
        """评估策略参数"""
        try:
            # 创建策略配置
            from strategies.single.macd import MACDConfig
            config = MACDConfig(**params)
            strategy = self.strategy_class(config)

            # 运行策略
            result = strategy.run(data)

            # 获取目标值
            score = getattr(result, self.objective, 0)
            return float(score)

        except Exception as e:
            print(f"策略评估失败: {e}")
            return float('-inf') if self.direction == 'maximize' else float('inf')

    def _is_better_score(self, new_score, best_score):
        """判断新分数是否更好"""
        if self.direction == 'maximize':
            return new_score > best_score
        else:
            return new_score < best_score

    def _calculate_overfitting_score(self, train_score, val_score):
        """计算过拟合分数"""
        if val_score == 0:
            return float('inf')

        if self.direction == 'maximize':
            return (train_score - val_score) / abs(val_score)
        else:
            return (val_score - train_score) / abs(val_score)


class SimpleOptimizationManager:
    """简化的优化管理器"""

    def __init__(self):
        self.optimization_history = []

    def optimize(self, strategy_class, param_space, data, method='grid',
                objective='sharpe_ratio', direction='maximize', validation_split=0.0,
                n_trials=50, **kwargs):
        """执行优化"""

        # 数据分割
        validation_data = None
        if validation_split > 0:
            split_idx = int(len(data) * (1 - validation_split))
            train_data = data.iloc[:split_idx]
            validation_data = data.iloc[split_idx:]
        else:
            train_data = data

        # 创建优化器
        if method == 'grid':
            optimizer = SimpleGridOptimizer(strategy_class, param_space, objective, direction)
            result = optimizer.optimize(train_data, validation_data, **kwargs)
        elif method == 'random':
            optimizer = SimpleRandomOptimizer(strategy_class, param_space, objective, direction)
            result = optimizer.optimize(train_data, validation_data, n_trials, **kwargs)
        else:
            raise ValueError(f"不支持的优化方法: {method}")

        # 记录历史
        self.optimization_history.append({
            'timestamp': datetime.now(),
            'method': method,
            'strategy_class': strategy_class.__name__,
            'objective': objective,
            'best_score': result.best_score,
            'best_params': result.best_params
        })

        return result

    def get_optimization_history(self):
        """获取优化历史"""
        return self.optimization_history.copy()

    def compare_methods(self, strategy_class, param_space, data, methods=None,
                       objective='sharpe_ratio', direction='maximize', n_trials=30, **kwargs):
        """比较不同优化方法"""
        if methods is None:
            methods = ['grid', 'random']

        print(f"开始比较优化方法: {methods}")

        results = {}
        for method in methods:
            try:
                print(f"运行 {method} 优化...")
                result = self.optimize(
                    strategy_class=strategy_class,
                    param_space=param_space,
                    data=data,
                    method=method,
                    objective=objective,
                    direction=direction,
                    n_trials=n_trials,
                    **kwargs
                )
                results[method] = result
            except Exception as e:
                print(f"{method} 优化失败: {e}")
                continue

        # 输出比较结果
        print("=" * 60)
        print("优化方法比较结果")
        print("=" * 60)

        for method, result in results.items():
            print(f"{method:>12}: {objective} = {result.best_score:.4f}, "
                 f"时间 = {result.optimization_time:.2f}s, "
                 f"试验 = {result.n_trials}")

        print("=" * 60)

        return results


# 创建全局实例
simple_optimization_manager = SimpleOptimizationManager()
