"""
参数优化基类模块

定义参数优化的基础接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import pandas as pd
import numpy as np

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.interfaces import IStrategy
from core.engine.single import SingleAssetEngine
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ParameterSpace:
    """参数空间定义"""
    name: str
    min_value: float
    max_value: float
    step: Optional[float] = None
    values: Optional[List[Any]] = None
    param_type: str = 'float'  # 'float', 'int', 'choice'
    
    def generate_values(self, num_points: Optional[int] = None) -> List[Any]:
        """生成参数值列表"""
        if self.values is not None:
            return self.values.copy()
        
        if self.param_type == 'choice':
            return self.values or []
        
        if self.step is not None:
            if self.param_type == 'int':
                return list(range(int(self.min_value), int(self.max_value) + 1, int(self.step)))
            else:
                values = []
                current = self.min_value
                while current <= self.max_value:
                    values.append(current)
                    current += self.step
                return values
        
        if num_points is None:
            num_points = 10
        
        if self.param_type == 'int':
            return list(range(int(self.min_value), int(self.max_value) + 1))
        else:
            return list(np.linspace(self.min_value, self.max_value, num_points))


@dataclass
class OptimizationResult:
    """优化结果"""
    best_params: Dict[str, Any]
    best_score: float
    all_results: List[Dict[str, Any]]
    optimization_time: float
    total_iterations: int
    convergence_info: Dict[str, Any] = field(default_factory=dict)
    
    def get_top_results(self, n: int = 10) -> List[Dict[str, Any]]:
        """获取前N个最佳结果"""
        sorted_results = sorted(self.all_results, key=lambda x: x['score'], reverse=True)
        return sorted_results[:n]
    
    def to_dataframe(self) -> pd.DataFrame:
        """转换为DataFrame"""
        return pd.DataFrame(self.all_results)


class BaseOptimizer(ABC):
    """参数优化器基类
    
    所有参数优化器都应该继承此类。
    """
    
    def __init__(
        self,
        objective_function: Optional[Callable] = None,
        maximize: bool = True,
        random_state: Optional[int] = None
    ):
        """
        初始化优化器
        
        Args:
            objective_function: 目标函数
            maximize: 是否最大化目标函数
            random_state: 随机种子
        """
        self.objective_function = objective_function
        self.maximize = maximize
        self.random_state = random_state
        
        # 优化状态
        self._parameter_spaces: Dict[str, ParameterSpace] = {}
        self._results: List[Dict[str, Any]] = []
        self._best_score = float('-inf') if maximize else float('inf')
        self._best_params = {}
        
        # 统计信息
        self._stats = {
            'total_evaluations': 0,
            'start_time': None,
            'end_time': None,
            'best_score_history': []
        }
        
        if random_state is not None:
            np.random.seed(random_state)
        
        logger.info(f"参数优化器初始化: {self.__class__.__name__}")
    
    def add_parameter(
        self,
        name: str,
        min_value: float,
        max_value: float,
        step: Optional[float] = None,
        values: Optional[List[Any]] = None,
        param_type: str = 'float'
    ):
        """
        添加参数空间
        
        Args:
            name: 参数名称
            min_value: 最小值
            max_value: 最大值
            step: 步长
            values: 候选值列表
            param_type: 参数类型
        """
        param_space = ParameterSpace(
            name=name,
            min_value=min_value,
            max_value=max_value,
            step=step,
            values=values,
            param_type=param_type
        )
        
        self._parameter_spaces[name] = param_space
        logger.debug(f"添加参数空间: {name} [{min_value}, {max_value}]")
    
    @abstractmethod
    def optimize(
        self,
        strategy: IStrategy,
        data: pd.DataFrame,
        max_iterations: Optional[int] = None,
        **kwargs
    ) -> OptimizationResult:
        """
        执行参数优化
        
        Args:
            strategy: 策略对象
            data: 历史数据
            max_iterations: 最大迭代次数
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        pass
    
    def evaluate_parameters(
        self,
        params: Dict[str, Any],
        strategy: IStrategy,
        data: pd.DataFrame
    ) -> float:
        """
        评估参数组合
        
        Args:
            params: 参数字典
            strategy: 策略对象
            data: 历史数据
            
        Returns:
            评估分数
        """
        try:
            # 更新策略参数
            for param_name, param_value in params.items():
                strategy.set_parameter(param_name, param_value)
            
            # 使用目标函数或默认评估
            if self.objective_function:
                score = self.objective_function(strategy, data, params)
            else:
                score = self._default_objective_function(strategy, data)
            
            # 记录结果
            result = {
                'params': params.copy(),
                'score': score,
                'timestamp': datetime.now()
            }
            self._results.append(result)
            
            # 更新最佳结果
            if self._is_better_score(score):
                self._best_score = score
                self._best_params = params.copy()
                self._stats['best_score_history'].append({
                    'iteration': self._stats['total_evaluations'],
                    'score': score,
                    'params': params.copy()
                })
            
            self._stats['total_evaluations'] += 1
            
            logger.debug(f"参数评估: {params} -> {score:.4f}")
            
            return score
            
        except Exception as e:
            logger.error(f"参数评估失败: {params} - {e}")
            # 返回最差分数
            return float('-inf') if self.maximize else float('inf')
    
    def _default_objective_function(self, strategy: IStrategy, data: pd.DataFrame) -> float:
        """默认目标函数：使用夏普比率"""
        try:
            # 生成信号
            signals = strategy.generate_signals(data)
            
            # 计算收益率
            returns = self._calculate_strategy_returns(signals, data)
            
            if len(returns) == 0 or returns.std() == 0:
                return 0.0
            
            # 计算夏普比率
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252)
            
            return sharpe_ratio
            
        except Exception as e:
            logger.error(f"默认目标函数计算失败: {e}")
            return 0.0
    
    def _calculate_strategy_returns(self, signals: pd.DataFrame, data: pd.DataFrame) -> pd.Series:
        """计算策略收益率"""
        if 'signal' not in signals.columns:
            return pd.Series(dtype=float)
        
        # 简化的收益率计算
        price_returns = data['close'].pct_change().fillna(0)
        strategy_returns = signals['signal'].shift(1) * price_returns
        
        return strategy_returns.dropna()
    
    def _is_better_score(self, score: float) -> bool:
        """判断是否是更好的分数"""
        if self.maximize:
            return score > self._best_score
        else:
            return score < self._best_score
    
    def get_parameter_spaces(self) -> Dict[str, ParameterSpace]:
        """获取参数空间"""
        return self._parameter_spaces.copy()
    
    def get_results(self) -> List[Dict[str, Any]]:
        """获取所有结果"""
        return self._results.copy()
    
    def get_best_params(self) -> Dict[str, Any]:
        """获取最佳参数"""
        return self._best_params.copy()
    
    def get_best_score(self) -> float:
        """获取最佳分数"""
        return self._best_score
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self._stats.copy()
        
        if stats['start_time'] and stats['end_time']:
            stats['total_time'] = (stats['end_time'] - stats['start_time']).total_seconds()
        
        if stats['total_evaluations'] > 0:
            stats['avg_time_per_evaluation'] = stats.get('total_time', 0) / stats['total_evaluations']
        
        return stats
    
    def reset(self):
        """重置优化器状态"""
        self._results.clear()
        self._best_score = float('-inf') if self.maximize else float('inf')
        self._best_params = {}
        self._stats = {
            'total_evaluations': 0,
            'start_time': None,
            'end_time': None,
            'best_score_history': []
        }
        
        logger.info("优化器状态已重置")
    
    def save_results(self, filepath: str):
        """保存结果到文件"""
        try:
            df = pd.DataFrame(self._results)
            df.to_csv(filepath, index=False)
            logger.info(f"优化结果已保存: {filepath}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def load_results(self, filepath: str):
        """从文件加载结果"""
        try:
            df = pd.read_csv(filepath)
            self._results = df.to_dict('records')
            
            # 重新计算最佳结果
            for result in self._results:
                score = result['score']
                if self._is_better_score(score):
                    self._best_score = score
                    self._best_params = result['params']
            
            logger.info(f"优化结果已加载: {filepath}")
        except Exception as e:
            logger.error(f"加载结果失败: {e}")


def create_parameter_space(
    name: str,
    min_value: float,
    max_value: float,
    step: Optional[float] = None,
    param_type: str = 'float'
) -> ParameterSpace:
    """创建参数空间的便捷函数"""
    return ParameterSpace(
        name=name,
        min_value=min_value,
        max_value=max_value,
        step=step,
        param_type=param_type
    )


def create_choice_parameter(name: str, choices: List[Any]) -> ParameterSpace:
    """创建选择参数空间的便捷函数"""
    return ParameterSpace(
        name=name,
        min_value=0,
        max_value=len(choices) - 1,
        values=choices,
        param_type='choice'
    )
