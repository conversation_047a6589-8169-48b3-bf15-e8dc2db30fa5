"""
投资组合数据结构

定义投资组合相关的数据结构。
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np

from .trading import Position, Trade, Order


@dataclass
class Portfolio:
    """投资组合数据结构
    
    管理多个标的的持仓和交易记录。
    """
    
    portfolio_id: str
    name: str
    initial_capital: float
    timestamp: datetime
    
    # 持仓信息
    positions: Dict[str, Position] = field(default_factory=dict)
    cash: float = 0.0
    
    # 交易记录
    trades: List[Trade] = field(default_factory=list)
    orders: List[Order] = field(default_factory=list)
    
    # 历史数据
    value_history: pd.Series = field(default_factory=pd.Series)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if self.cash == 0.0:
            self.cash = self.initial_capital
    
    @property
    def total_value(self) -> float:
        """总价值（需要当前价格）"""
        position_value = sum(pos.market_value for pos in self.positions.values())
        return self.cash + position_value
    
    @property
    def position_value(self) -> float:
        """持仓价值"""
        return sum(pos.market_value for pos in self.positions.values())
    
    @property
    def total_pnl(self) -> float:
        """总盈亏"""
        return sum(pos.total_pnl for pos in self.positions.values())
    
    @property
    def realized_pnl(self) -> float:
        """已实现盈亏"""
        return sum(pos.realized_pnl for pos in self.positions.values())
    
    @property
    def unrealized_pnl(self) -> float:
        """未实现盈亏"""
        return sum(pos.unrealized_pnl for pos in self.positions.values())
    
    def add_position(self, symbol: str, position: Position):
        """添加持仓"""
        self.positions[symbol] = position
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取持仓"""
        return self.positions.get(symbol)
    
    def remove_position(self, symbol: str):
        """移除持仓"""
        if symbol in self.positions:
            del self.positions[symbol]
    
    def add_trade(self, trade: Trade):
        """添加交易记录"""
        self.trades.append(trade)
    
    def add_order(self, order: Order):
        """添加订单记录"""
        self.orders.append(order)
    
    def update_value_history(self, timestamp: datetime, value: float):
        """更新价值历史"""
        self.value_history.loc[timestamp] = value
    
    def get_symbols(self) -> List[str]:
        """获取所有持仓标的"""
        return list(self.positions.keys())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'portfolio_id': self.portfolio_id,
            'name': self.name,
            'initial_capital': self.initial_capital,
            'cash': self.cash,
            'total_value': self.total_value,
            'position_value': self.position_value,
            'total_pnl': self.total_pnl,
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl,
            'positions_count': len(self.positions),
            'trades_count': len(self.trades),
            'orders_count': len(self.orders),
            'timestamp': self.timestamp,
            'metadata': self.metadata,
        }
