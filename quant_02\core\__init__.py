"""
核心模块

包含回测引擎、配置管理、数据结构等核心功能。
"""

# 数据结构
from .structures import (
    OHLCV, MarketData, Tick, Quote,
    Order, Position, Trade, Portfolio, StrategyResult,
    OrderSide, OrderType, OrderStatus, PositionSide
)

# 配置管理
from .config import (
    BaseConfig, ConfigManager, config_manager,
    GlobalConfig, global_config, BacktestConfig, RiskConfig
)

# 异常定义
from .exceptions import (
    QuantBaseException, DataSourceException, StrategyException,
    EngineException, RiskManagementException, OrderException
)

# 接口定义
from .interfaces import (
    IDataSource, IStrategy, IIndicator, IEngine, IRiskManager,
    IOptimizer, IReportGenerator, ICacheManager, ILogger, IConfigManager
)

__all__ = [
    # 数据结构
    "OHLCV",
    "MarketData",
    "Tick", 
    "Quote",
    "Order",
    "Position",
    "Trade",
    "Portfolio",
    "StrategyResult",
    "OrderSide",
    "OrderType",
    "OrderStatus", 
    "PositionSide",
    
    # 配置管理
    "BaseConfig",
    "ConfigManager",
    "config_manager",
    "GlobalConfig",
    "global_config",
    "BacktestConfig",
    "RiskConfig",
    
    # 异常定义
    "QuantBaseException",
    "DataSourceException",
    "StrategyException",
    "EngineException", 
    "RiskManagementException",
    "OrderException",
    
    # 接口定义
    "IDataSource",
    "IStrategy",
    "IIndicator",
    "IEngine",
    "IRiskManager",
    "IOptimizer",
    "IReportGenerator",
    "ICacheManager",
    "ILogger",
    "IConfigManager",
]
