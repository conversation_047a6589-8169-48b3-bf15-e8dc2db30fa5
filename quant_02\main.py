"""
Quant_02 主入口文件

提供命令行接口和快速开始功能。
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.config.global_config import GlobalConfig
from dataseed.factory import create_data_source
from strategies.factory import create_strategy, list_strategies
from indicators.factory import create_indicator, list_indicators
from utils.logger import get_logger, init_logger


def setup_logging(debug: bool = False):
    """设置日志"""
    log_config = {
        'level': 'DEBUG' if debug else 'INFO',
        'console_enabled': True,
        'file_enabled': True,
        'colorize': True,
    }
    init_logger(log_config)


def demo_backtest():
    """演示回测功能"""
    logger = get_logger(__name__)
    logger.info("开始演示回测功能")

    try:
        # 创建配置
        config = GlobalConfig.create_default()
        logger.info(f"配置创建完成: {config.app_name} v{config.app_version}")

        # 创建数据源
        data_source = create_data_source("mock", market_state="normal")
        logger.info("Mock数据源创建完成")

        # 获取测试数据
        symbol = "000001"
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

        logger.info(f"获取数据: {symbol}, {start_date} 到 {end_date}")
        data = data_source.get_data(symbol, start_date, end_date)

        logger.info(f"数据获取完成，共 {len(data)} 条记录")
        logger.info(f"数据范围: {data.index[0]} 到 {data.index[-1]}")
        logger.info(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")

        # 显示数据统计
        print("\n=== 数据统计 ===")
        print(f"股票代码: {symbol}")
        print(f"数据条数: {len(data)}")
        print(f"日期范围: {data.index[0].strftime('%Y-%m-%d')} 到 {data.index[-1].strftime('%Y-%m-%d')}")
        print(f"开盘价: {data['open'].iloc[0]:.2f}")
        print(f"收盘价: {data['close'].iloc[-1]:.2f}")
        print(f"最高价: {data['high'].max():.2f}")
        print(f"最低价: {data['low'].min():.2f}")
        print(f"平均成交量: {data['volume'].mean():.0f}")

        # 计算简单收益率
        total_return = (data['close'].iloc[-1] / data['close'].iloc[0] - 1) * 100
        print(f"总收益率: {total_return:.2f}%")

        # 演示MACD策略
        print("\n=== MACD策略演示 ===")
        try:
            from strategies.single.macd import MACDStrategy, MACDConfig

            # 创建MACD策略配置
            macd_config = MACDConfig(
                strategy_name="macd_demo",
                fast_period=12,
                slow_period=26,
                signal_period=9,
                min_signal_strength=0.3
            )

            # 创建策略实例
            strategy = MACDStrategy(macd_config)
            logger.info("MACD策略创建完成")

            # 生成交易信号
            signals = strategy.generate_signals(data)

            # 统计信号
            buy_signals = (signals['signal'] == 1).sum()
            sell_signals = (signals['signal'] == -1).sum()
            avg_strength = signals[signals['signal'] != 0]['strength'].mean()

            print(f"买入信号: {buy_signals}")
            print(f"卖出信号: {sell_signals}")
            print(f"平均信号强度: {avg_strength:.3f}")

            # 显示最近几个信号
            recent_signals = signals[signals['signal'] != 0].tail(5)
            if not recent_signals.empty:
                print("\n最近5个交易信号:")
                for idx, row in recent_signals.iterrows():
                    signal_type = "买入" if row['signal'] == 1 else "卖出"
                    print(f"  {idx.strftime('%Y-%m-%d')}: {signal_type}, 强度: {row['strength']:.3f}, 价格: {row['price']:.2f}")

        except Exception as e:
            logger.warning(f"MACD策略演示失败: {e}")

        # 演示技术指标
        print("\n=== 技术指标演示 ===")
        try:
            from indicators.trend.macd import MACD
            from indicators.trend.moving_average import SMA, EMA

            # MACD指标
            macd_indicator = MACD(fast_period=12, slow_period=26, signal_period=9)
            macd_result = macd_indicator.calculate(data)

            print(f"MACD指标计算完成，最新值:")
            print(f"  MACD线: {macd_result['macd'].iloc[-1]:.4f}")
            print(f"  信号线: {macd_result['signal'].iloc[-1]:.4f}")
            print(f"  柱状图: {macd_result['histogram'].iloc[-1]:.4f}")

            # 移动平均
            sma20 = SMA(period=20).calculate(data)
            ema20 = EMA(period=20).calculate(data)

            print(f"\n移动平均指标:")
            print(f"  SMA(20): {sma20.iloc[-1]:.2f}")
            print(f"  EMA(20): {ema20.iloc[-1]:.2f}")
            print(f"  当前价格: {data['close'].iloc[-1]:.2f}")

        except Exception as e:
            logger.warning(f"技术指标演示失败: {e}")

        logger.info("演示完成")

    except Exception as e:
        logger.error(f"演示失败: {e}")
        raise


def list_data_sources():
    """列出可用的数据源"""
    from dataseed import list_data_sources, check_dependencies

    print("\n=== 可用数据源 ===")
    sources = list_data_sources()
    dependencies = check_dependencies()

    for name, class_name in sources.items():
        status = "✓" if dependencies.get(name, False) else "✗"
        print(f"{status} {name}: {class_name}")

    print("\n说明:")
    print("✓ - 可用")
    print("✗ - 不可用（缺少依赖）")


def list_strategies():
    """列出可用的策略"""
    from strategies import get_available_strategies, check_strategy_dependencies

    print("\n=== 可用策略 ===")
    strategies = get_available_strategies()
    dependencies = check_strategy_dependencies()

    for strategy in strategies:
        status = "✓" if dependencies.get(strategy, False) else "✗"
        print(f"{status} {strategy}")

    print("\n说明:")
    print("✓ - 可用")
    print("✗ - 不可用（缺少依赖）")


def list_indicators():
    """列出可用的技术指标"""
    from indicators import get_available_indicators, check_indicator_dependencies, get_indicator_categories

    print("\n=== 可用技术指标 ===")

    categories = get_indicator_categories()
    dependencies = check_indicator_dependencies()

    for category in categories:
        status = "✓" if dependencies.get(category, False) else "✗"
        print(f"\n{status} {category.upper()}:")

        from indicators.factory import indicator_factory
        indicators = indicator_factory.list_by_category(category)

        for indicator in indicators:
            print(f"  - {indicator}")

    print("\n说明:")
    print("✓ - 可用")
    print("✗ - 不可用（缺少依赖）")


def show_config():
    """显示配置信息"""
    config = GlobalConfig.create_default()

    print("\n=== 系统配置 ===")
    runtime_info = config.get_runtime_info()

    print(f"应用名称: {runtime_info['app_name']}")
    print(f"应用版本: {runtime_info['app_version']}")
    print(f"运行环境: {runtime_info['environment']}")
    print(f"调试模式: {runtime_info['debug']}")

    print("\n目录配置:")
    for name, path in runtime_info['directories'].items():
        print(f"  {name}: {path}")

    print("\n功能特性:")
    for name, enabled in runtime_info['features'].items():
        status = "启用" if enabled else "禁用"
        print(f"  {name}: {status}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Quant_02 - 下一代高性能量化回测引擎",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py demo              # 运行演示
  python main.py sources           # 列出数据源
  python main.py config            # 显示配置
  python main.py demo --debug      # 调试模式运行演示
        """
    )

    parser.add_argument(
        'command',
        choices=['demo', 'sources', 'strategies', 'indicators', 'config', 'test', 'optimize', 'report', 'risk'],
        help='要执行的命令'
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='Quant_02 v2.0.0'
    )

    args = parser.parse_args()

    # 设置日志
    setup_logging(args.debug)

    # 执行命令
    try:
        if args.command == 'demo':
            demo_backtest()
        elif args.command == 'sources':
            list_data_sources()
        elif args.command == 'strategies':
            list_strategies()
        elif args.command == 'indicators':
            list_indicators()
        elif args.command == 'config':
            show_config()
        elif args.command == 'test':
            run_tests()
        elif args.command == 'optimize':
            demo_optimization()
        elif args.command == 'report':
            demo_report()
        elif args.command == 'risk':
            demo_risk_analysis()

    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"执行失败: {e}")
        if args.debug:
            raise
        sys.exit(1)


def run_tests():
    """运行测试"""
    print("\n=== 运行测试 ===")
    try:
        import subprocess
        import sys

        test_script = Path(__file__).parent / "tests" / "run_tests.py"

        if test_script.exists():
            result = subprocess.run([sys.executable, str(test_script)],
                                  capture_output=True, text=True)

            print(result.stdout)
            if result.stderr:
                print("错误输出:")
                print(result.stderr)

            if result.returncode == 0:
                print("✅ 所有测试通过")
            else:
                print("❌ 部分测试失败")
        else:
            print("❌ 测试脚本不存在")

    except Exception as e:
        print(f"❌ 运行测试失败: {e}")


def demo_optimization():
    """演示参数优化"""
    print("\n=== 参数优化演示 ===")
    try:
        from optimizer.gridsearch import GridSearchOptimizer
        from strategies.single.macd import MACDStrategy, MACDConfig
        from dataseed.factory import create_data_source

        # 创建数据源和获取数据
        data_source = create_data_source("mock", market_state="normal")
        symbol = "000001"
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=180)).strftime("%Y-%m-%d")

        data = data_source.get_data(symbol, start_date, end_date)

        # 创建优化器
        optimizer = GridSearchOptimizer(maximize=True)

        # 添加参数空间
        optimizer.add_parameter('fast_period', 8, 16, step=2, param_type='int')
        optimizer.add_parameter('slow_period', 20, 30, step=2, param_type='int')
        optimizer.add_parameter('signal_period', 6, 12, step=2, param_type='int')

        # 创建策略
        config = MACDConfig(strategy_name="optimization_test")
        strategy = MACDStrategy(config)

        print(f"开始优化MACD策略参数...")
        print(f"数据期间: {start_date} 到 {end_date}")
        print(f"参数空间: fast_period[8-16], slow_period[20-30], signal_period[6-12]")

        # 执行优化
        result = optimizer.optimize(strategy, data, max_iterations=20)

        print(f"\n优化完成!")
        print(f"最佳参数: {result.best_params}")
        print(f"最佳分数: {result.best_score:.4f}")
        print(f"总迭代次数: {result.total_iterations}")
        print(f"优化时间: {result.optimization_time:.2f}秒")

    except Exception as e:
        print(f"❌ 参数优化演示失败: {e}")


def demo_report():
    """演示报告生成"""
    print("\n=== 报告生成演示 ===")
    try:
        from reports.backtest import BacktestReportGenerator, ReportConfig
        from core.engine.single import SingleAssetEngine
        from strategies.single.macd import MACDStrategy, MACDConfig
        from dataseed.factory import create_data_source

        # 创建数据和运行回测
        data_source = create_data_source("mock", market_state="normal")
        symbol = "000001"
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")

        # 创建引擎和策略
        engine = SingleAssetEngine(data_source)
        config = MACDConfig(strategy_name="report_demo")
        strategy = MACDStrategy(config)

        print(f"运行回测生成报告...")

        # 运行回测
        result = engine.run_single_backtest(
            strategy, symbol, start_date, end_date, initial_capital=100000
        )

        # 生成报告
        report_config = ReportConfig(
            title="MACD策略回测报告",
            output_format="html",
            include_charts=False  # 简化演示
        )

        generator = BacktestReportGenerator(report_config)
        report_path = generator.generate(result)

        print(f"✅ 报告已生成: {report_path}")
        print(f"策略收益率: {result.total_return:.2%}")
        print(f"夏普比率: {result.sharpe_ratio:.2f}")
        print(f"最大回撤: {result.max_drawdown:.2%}")

    except Exception as e:
        print(f"❌ 报告生成演示失败: {e}")


def demo_risk_analysis():
    """演示风险分析"""
    print("\n=== 风险分析演示 ===")
    try:
        from risk.manager import RiskManager
        from risk.metrics import RiskMetricsCalculator
        from core.config.risk import RiskConfig
        from core.structures.portfolio import Portfolio
        from dataseed.factory import create_data_source

        # 创建风险配置
        risk_config = RiskConfig(
            max_position_size=0.1,
            max_leverage=2.0,
            max_drawdown=0.2,
            enable_stop_loss=True
        )

        # 创建风险管理器
        risk_manager = RiskManager(risk_config)

        # 创建模拟投资组合
        portfolio = Portfolio(
            portfolio_id="risk_demo",
            name="风险分析演示",
            initial_capital=100000,
            timestamp=datetime.now()
        )

        # 创建风险指标计算器
        calculator = RiskMetricsCalculator()

        print(f"风险管理配置:")
        print(f"  最大持仓比例: {risk_config.max_position_size:.1%}")
        print(f"  最大杠杆: {risk_config.max_leverage:.1f}")
        print(f"  最大回撤: {risk_config.max_drawdown:.1%}")
        print(f"  启用止损: {risk_config.enable_stop_loss}")

        # 更新风险指标
        risk_manager.update_risk_metrics(portfolio)
        metrics = risk_manager.get_risk_metrics()

        print(f"\n当前风险指标:")
        print(f"  投资组合价值: ¥{metrics.portfolio_value:,.2f}")
        print(f"  总敞口: ¥{metrics.total_exposure:,.2f}")
        print(f"  杠杆倍数: {metrics.leverage:.2f}")
        print(f"  现金比例: {metrics.cash_ratio:.1%}")
        print(f"  当前回撤: {metrics.current_drawdown:.2%}")

        # 获取统计信息
        stats = risk_manager.get_stats()
        print(f"\n风险管理统计:")
        print(f"  风险检查次数: {stats['risk_checks']}")
        print(f"  违规次数: {stats['violations']}")
        print(f"  警报生成次数: {stats['alerts_generated']}")

        print("✅ 风险分析演示完成")

    except Exception as e:
        print(f"❌ 风险分析演示失败: {e}")


if __name__ == "__main__":
    main()
