"""
量化回测引擎演示脚本

简化版本，用于测试核心功能。
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

print(f"当前工作目录: {current_dir}")
print(f"Python路径: {sys.path[:3]}")

def create_mock_data(symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
    """创建模拟数据"""
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date)
    
    # 生成交易日
    dates = pd.bdate_range(start=start, end=end, freq='B')
    periods = len(dates)
    
    if periods == 0:
        return pd.DataFrame()
    
    # 设置随机种子
    np.random.seed(hash(symbol) % (2**32))
    
    # 生成价格序列（几何布朗运动）
    base_price = 100.0
    returns = np.random.normal(0.0005, 0.02, periods)
    prices = base_price * np.exp(np.cumsum(returns))
    
    # 生成OHLC
    open_prices = np.concatenate([[base_price], prices[:-1]])
    close_prices = prices
    
    # 添加日内波动
    daily_range = np.random.uniform(0.01, 0.05, periods)
    high_prices = np.maximum(open_prices, close_prices) * (1 + daily_range/2)
    low_prices = np.minimum(open_prices, close_prices) * (1 - daily_range/2)
    
    # 生成成交量
    volumes = np.random.normal(1000000, 300000, periods)
    volumes = np.maximum(volumes, 100000).astype(int)
    
    # 创建DataFrame
    data = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volumes
    }, index=dates)
    
    # 计算成交额和涨跌幅
    data['amount'] = data['close'] * data['volume']
    data['pct_chg'] = data['close'].pct_change() * 100
    
    return data.dropna()


def calculate_macd(data: pd.Series, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    ema_fast = data.ewm(span=fast).mean()
    ema_slow = data.ewm(span=slow).mean()
    
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram


def calculate_rsi(data: pd.Series, period=14):
    """计算RSI指标"""
    delta = data.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def simple_macd_strategy(data: pd.DataFrame):
    """简单MACD策略"""
    print("\n=== MACD策略回测 ===")
    
    # 计算MACD
    macd_line, signal_line, histogram = calculate_macd(data['close'])
    
    # 生成信号
    signals = pd.DataFrame(index=data.index)
    signals['signal'] = 0
    
    # 买入信号：MACD上穿信号线
    buy_condition = (macd_line > signal_line) & (macd_line.shift(1) <= signal_line.shift(1))
    signals.loc[buy_condition, 'signal'] = 1
    
    # 卖出信号：MACD下穿信号线
    sell_condition = (macd_line < signal_line) & (macd_line.shift(1) >= signal_line.shift(1))
    signals.loc[sell_condition, 'signal'] = -1
    
    # 计算收益
    returns = data['close'].pct_change()
    positions = signals['signal'].shift(1).fillna(0)
    strategy_returns = positions * returns
    
    # 计算累积收益
    cumulative_returns = (1 + strategy_returns).cumprod()
    total_return = cumulative_returns.iloc[-1] - 1
    
    # 计算统计指标
    annual_return = (1 + total_return) ** (252 / len(data)) - 1
    volatility = strategy_returns.std() * np.sqrt(252)
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
    
    # 计算最大回撤
    peak = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = drawdown.min()
    
    # 交易统计
    trades = signals[signals['signal'] != 0]
    total_trades = len(trades)
    
    print(f"数据期间: {data.index[0].date()} 到 {data.index[-1].date()}")
    print(f"数据点数: {len(data)}")
    print(f"总收益: {total_return:.2%}")
    print(f"年化收益: {annual_return:.2%}")
    print(f"波动率: {volatility:.2%}")
    print(f"夏普比率: {sharpe_ratio:.2f}")
    print(f"最大回撤: {abs(max_drawdown):.2%}")
    print(f"交易次数: {total_trades}")
    
    return {
        'total_return': total_return,
        'annual_return': annual_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': abs(max_drawdown),
        'total_trades': total_trades
    }


def simple_rsi_strategy(data: pd.DataFrame):
    """简单RSI策略"""
    print("\n=== RSI策略回测 ===")
    
    # 计算RSI
    rsi = calculate_rsi(data['close'])
    
    # 生成信号
    signals = pd.DataFrame(index=data.index)
    signals['signal'] = 0
    
    # 买入信号：RSI从超卖区域向上突破
    buy_condition = (rsi > 30) & (rsi.shift(1) <= 30)
    signals.loc[buy_condition, 'signal'] = 1
    
    # 卖出信号：RSI从超买区域向下突破
    sell_condition = (rsi < 70) & (rsi.shift(1) >= 70)
    signals.loc[sell_condition, 'signal'] = -1
    
    # 计算收益
    returns = data['close'].pct_change()
    positions = signals['signal'].shift(1).fillna(0)
    strategy_returns = positions * returns
    
    # 计算累积收益
    cumulative_returns = (1 + strategy_returns).cumprod()
    total_return = cumulative_returns.iloc[-1] - 1
    
    # 计算统计指标
    annual_return = (1 + total_return) ** (252 / len(data)) - 1
    volatility = strategy_returns.std() * np.sqrt(252)
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0
    
    # 计算最大回撤
    peak = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - peak) / peak
    max_drawdown = drawdown.min()
    
    # 交易统计
    trades = signals[signals['signal'] != 0]
    total_trades = len(trades)
    
    print(f"数据期间: {data.index[0].date()} 到 {data.index[-1].date()}")
    print(f"数据点数: {len(data)}")
    print(f"总收益: {total_return:.2%}")
    print(f"年化收益: {annual_return:.2%}")
    print(f"波动率: {volatility:.2%}")
    print(f"夏普比率: {sharpe_ratio:.2f}")
    print(f"最大回撤: {abs(max_drawdown):.2%}")
    print(f"交易次数: {total_trades}")
    
    return {
        'total_return': total_return,
        'annual_return': annual_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': abs(max_drawdown),
        'total_trades': total_trades
    }


def batch_test_demo():
    """批量测试演示"""
    print("\n=== 批量测试演示 ===")
    
    symbols = ['000001', '000002', '600000', '600036', '600519']
    results = {}
    
    for symbol in symbols:
        print(f"\n测试股票: {symbol}")
        
        # 生成数据
        data = create_mock_data(symbol, '2023-01-01', '2023-12-31')
        
        if data.empty:
            print(f"  {symbol}: 数据为空")
            continue
        
        # 运行MACD策略
        try:
            result = simple_macd_strategy(data)
            results[symbol] = result
            print(f"  {symbol}: 收益 {result['total_return']:.2%}, 夏普 {result['sharpe_ratio']:.2f}")
        except Exception as e:
            print(f"  {symbol}: 失败 - {e}")
    
    # 汇总结果
    if results:
        print(f"\n=== 汇总结果 ===")
        avg_return = np.mean([r['total_return'] for r in results.values()])
        avg_sharpe = np.mean([r['sharpe_ratio'] for r in results.values()])
        print(f"平均收益: {avg_return:.2%}")
        print(f"平均夏普: {avg_sharpe:.2f}")
        print(f"成功率: {len(results)}/{len(symbols)} = {len(results)/len(symbols):.1%}")


def main():
    """主函数"""
    print("量化回测引擎演示")
    print("=" * 50)
    
    try:
        # 生成测试数据
        print("生成测试数据...")
        data = create_mock_data('000001', '2023-01-01', '2023-12-31')
        
        if data.empty:
            print("数据生成失败")
            return
        
        print(f"数据生成成功: {len(data)} 条记录")
        print(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
        
        # 运行策略测试
        macd_result = simple_macd_strategy(data)
        rsi_result = simple_rsi_strategy(data)
        
        # 批量测试
        batch_test_demo()
        
        print("\n=== 演示完成 ===")
        print("系统核心功能运行正常！")
        
    except Exception as e:
        print(f"演示运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
