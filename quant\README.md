# Quant - 量化交易回测框架

一个用于量化交易策略回测的Python框架，提供了完整的数据获取、策略开发、回测和报告生成功能。

## 特点

- 🚀 **高性能回测引擎** - 基于VectorBT的高效回测计算
- 📊 **多种数据源** - 支持Akshare、数据库、模拟数据
- 🎯 **灵活的策略框架** - 易于扩展的策略开发接口
- 📈 **美观的报告生成** - 支持多种HTML模板和JSON数据导出
- ⚡ **并行处理** - 支持多资产并行回测
- 🛡️ **风险管理** - 内置风险控制和管理功能
- 🔧 **参数优化** - 自动化策略参数优化
- 💾 **数据缓存** - 智能数据缓存提高效率

## 项目结构

```
quant/
├── dataseed/           # 数据源模块
│   ├── base.py         # 数据源基类
│   ├── akshare.py      # Akshare数据源
│   ├── database.py     # 数据库数据源
│   └── mock.py         # 模拟数据源
├── strategies/         # 策略模块
│   ├── base.py         # 策略基类
│   ├── macd.py         # MACD策略
│   └── rsi.py          # RSI策略
├── core/               # 核心模块
│   ├── config.py       # 配置管理
│   ├── engine.py       # 回测引擎
│   ├── report.py       # 报告生成器
│   └── risk.py         # 风险管理器（止盈止损）
├── utils/              # 工具模块
│   ├── cache.py        # 数据缓存
│   ├── indicators.py   # 技术指标
│   └── logger.py       # 日志工具
├── main.py             # 主程序入口
├── requirements.txt    # 依赖文件
└── README.md           # 说明文档
```

## 安装

### 1. 克隆项目

```bash
git clone <repository-url>
cd quant
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 验证安装

```bash
python main.py --help
```

## 快速开始

### 基本用法

```bash
# 运行均线交叉策略回测
python main.py \
    --symbol 600000 \
    --start 2023-01-01 \
    --end 2023-12-31 \
    --strategy ma_cross \
    --params '{"fast_window": 5, "slow_window": 20}' \
    --dataseed akshare \
    --output ./output
```

### 参数说明

- `--symbol`: 证券代码（支持自动添加市场前缀）
- `--start`: 回测开始日期 (YYYY-MM-DD)
- `--end`: 回测结束日期 (YYYY-MM-DD)
- `--strategy`: 策略名称 (ma_cross, rsi)
- `--params`: 策略参数 (JSON格式)
- `--dataseed`: 数据源 (akshare, database, mock)
- `--output`: 输出目录

### 高级用法

#### 多资产回测

```bash
python main.py 
    --symbols 600000 000001 000002 
    --start 2023-01-01 
    --end 2023-12-31 
    --strategy ma_cross 
    --parallel 
    --output ./output
```

#### 参数优化

```bash
python main.py \
    --symbol 600000 \
    --start 2023-01-01 \
    --end 2023-12-31 \
    --strategy ma_cross \
    --optimize \
    --optimize-params '{"fast_window": [3, 5, 10], "slow_window": [15, 20, 30]}' \
    --optimize-metric sharpe_ratio \
    --output ./output
```

## 编程接口

### 基本使用

```python
from quant.dataseed import Akshare
from quant.strategies import MACross
from quant.core import QuantEngine, ReportGenerator

# 创建数据源
dataseed = Akshare()

# 创建策略
strategy = MACross(params={'fast_window': 5, 'slow_window': 20})

# 创建回测引擎
engine = QuantEngine(dataseed, strategy)

# 运行回测
portfolio, metrics = engine.run('sh600000', '2023-01-01', '2023-12-31')

# 生成报告
report = ReportGenerator(portfolio, 'sh600000', 'MACross')
report.save_report_json('./output')
```

### 自定义策略

```python
from quant.strategies.base import BaseStrategy
import pandas as pd
import numpy as np

class MyStrategy(BaseStrategy):
    def __init__(self, params=None):
        super().__init__(params)
        self.period = self.get_param('period', 20)
    
    def run(self, data: pd.DataFrame):
        # 实现你的策略逻辑
        sma = data['close'].rolling(self.period).mean()
        entries = data['close'] > sma
        exits = data['close'] < sma
        return entries, exits
```

## 配置文件

创建 `config.json` 文件来自定义配置：

```json
{
  "dataseed": {
    "akshare": {
      "retry_times": 3,
      "timeout": 30
    },
    "cache": {
      "enabled": true,
      "max_age_hours": 24
    }
  },
  "backtest": {
    "default_initial_capital": 100000.0,
    "default_commission": 0.0003
  },
  "risk": {
    "max_drawdown": 0.2,
    "stop_loss": 0.1
  }
}
```

## 支持的策略

### 1. 均线交叉策略 (ma_cross)

基于快速和慢速移动平均线的交叉信号进行交易。

**参数:**
- `fast_window`: 快线周期 (默认: 5)
- `slow_window`: 慢线周期 (默认: 20)

### 2. RSI策略 (rsi)

基于相对强弱指数的超买超卖信号进行交易。

**参数:**
- `window`: RSI计算周期 (默认: 14)
- `overbought`: 超买阈值 (默认: 70)
- `oversold`: 超卖阈值 (默认: 30)

## 支持的数据源

### 1. Akshare数据源 (akshare)

从Akshare获取A股市场数据，支持前复权处理。

### 2. 数据库数据源 (database)

从SQL数据库获取历史数据，支持SQLite、MySQL、PostgreSQL等。

### 3. 模拟数据源 (mock)

生成符合真实市场特征的模拟数据，用于测试和开发。

## 报告模板

### 模板1 (report_template_01.html)

- 简洁明亮的设计风格
- 完整的性能指标展示
- 交互式图表
- 详细的交易记录

### 模板2 (report_template_02.html)

- 深色主题设计
- 仪表板风格布局
- 风险评估面板
- 专业的数据可视化

## 开发指南

### 添加新策略

1. 在 `strategies/` 目录下创建新的策略文件
2. 继承 `BaseStrategy` 类
3. 实现 `run()` 方法
4. 在 `strategies/__init__.py` 中导入
5. 在 `main.py` 中添加策略映射

### 添加新数据源

1. 在 `dataseed/` 目录下创建新的数据源文件
2. 继承 `BaseDataSeed` 类
3. 实现 `get_data()` 方法
4. 在 `dataseed/__init__.py` 中导入

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue: [GitHub Issues]
- 邮箱: [<EMAIL>]
