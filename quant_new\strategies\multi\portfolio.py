"""
投资组合策略

基于多个标的构建投资组合的策略。
"""

from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
from pydantic import Field, validator

from ..base import BaseStrategy, StrategyConfig, StrategyResult
from ...utils.logger import get_logger

logger = get_logger(__name__)


class PortfolioConfig(StrategyConfig):
    """投资组合策略配置"""
    
    strategy_name: str = Field(default="投资组合策略", description="策略名称")
    
    # 组合参数
    symbols: List[str] = Field(default_factory=list, description="股票代码列表")
    weights: Optional[Dict[str, float]] = Field(default=None, description="权重分配")
    rebalance_frequency: str = Field(default="monthly", description="再平衡频率")
    
    # 选股参数
    momentum_period: int = Field(default=20, description="动量周期")
    volatility_period: int = Field(default=20, description="波动率周期")
    
    # 风控参数
    max_single_weight: float = Field(default=0.2, description="单个标的最大权重")
    min_single_weight: float = Field(default=0.05, description="单个标的最小权重")
    
    # 再平衡参数
    rebalance_threshold: float = Field(default=0.05, description="再平衡阈值")
    transaction_cost: float = Field(default=0.001, description="交易成本")
    
    @validator('weights')
    def validate_weights(cls, v, values):
        """验证权重"""
        if v is not None:
            if abs(sum(v.values()) - 1.0) > 0.01:
                raise ValueError("权重总和必须等于1")
            
            symbols = values.get('symbols', [])
            if symbols and set(v.keys()) != set(symbols):
                raise ValueError("权重键必须与股票代码列表匹配")
        
        return v


class PortfolioStrategy(BaseStrategy):
    """投资组合策略实现"""
    
    def __init__(self, config: PortfolioConfig):
        """
        初始化投资组合策略
        
        Args:
            config: 投资组合策略配置
        """
        super().__init__(config)
        self.config: PortfolioConfig = config
        
        # 内部状态
        self._current_weights = {}
        self._target_weights = {}
        self._price_data = {}
        self._returns_data = {}
    
    def set_data(self, data_dict: Dict[str, pd.DataFrame]):
        """
        设置多标的数据
        
        Args:
            data_dict: {symbol: DataFrame} 数据字典
        """
        self._price_data = data_dict
        
        # 计算收益率数据
        self._returns_data = {}
        for symbol, data in data_dict.items():
            if not data.empty and 'close' in data.columns:
                self._returns_data[symbol] = data['close'].pct_change().dropna()
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成投资组合信号
        
        Args:
            data: 主要标的数据（用于时间索引）
            
        Returns:
            组合信号DataFrame
        """
        if not self._price_data:
            raise ValueError("请先使用set_data()设置多标的数据")
        
        # 创建信号DataFrame
        signals = pd.DataFrame(index=data.index)
        
        # 初始化权重列
        for symbol in self.config.symbols:
            signals[f'{symbol}_weight'] = 0.0
        
        signals['rebalance'] = False
        
        # 计算目标权重
        if self.config.weights:
            # 使用固定权重
            target_weights = self.config.weights
        else:
            # 使用动态权重计算
            target_weights = self._calculate_dynamic_weights(data.index)
        
        # 生成再平衡信号
        rebalance_dates = self._get_rebalance_dates(data.index)
        
        current_weights = {symbol: 0.0 for symbol in self.config.symbols}
        
        for date in data.index:
            # 检查是否需要再平衡
            if date in rebalance_dates:
                # 更新目标权重
                if not self.config.weights:
                    target_weights = self._calculate_dynamic_weights_at_date(date)
                
                # 检查权重偏离
                need_rebalance = self._check_rebalance_need(current_weights, target_weights)
                
                if need_rebalance:
                    signals.loc[date, 'rebalance'] = True
                    current_weights = target_weights.copy()
            
            # 设置当前权重
            for symbol in self.config.symbols:
                signals.loc[date, f'{symbol}_weight'] = current_weights.get(symbol, 0.0)
        
        logger.debug(f"生成投资组合信号: {len(rebalance_dates)} 个再平衡点")
        
        return signals
    
    def _calculate_dynamic_weights(self, date_index: pd.DatetimeIndex) -> Dict[str, float]:
        """计算动态权重（基于最新数据）"""
        if not date_index.empty:
            return self._calculate_dynamic_weights_at_date(date_index[-1])
        return {symbol: 1.0/len(self.config.symbols) for symbol in self.config.symbols}
    
    def _calculate_dynamic_weights_at_date(self, date) -> Dict[str, float]:
        """在特定日期计算动态权重"""
        try:
            # 等权重作为基础
            n_symbols = len(self.config.symbols)
            base_weight = 1.0 / n_symbols
            
            weights = {}
            
            # 基于动量和波动率调整权重
            for symbol in self.config.symbols:
                if symbol in self._returns_data:
                    returns = self._returns_data[symbol]
                    
                    # 获取到指定日期的数据
                    returns_to_date = returns[returns.index <= date]
                    
                    if len(returns_to_date) >= self.config.momentum_period:
                        # 计算动量得分
                        momentum = returns_to_date.tail(self.config.momentum_period).mean()
                        
                        # 计算波动率得分（波动率越低越好）
                        volatility = returns_to_date.tail(self.config.volatility_period).std()
                        
                        # 综合得分（动量高、波动率低的权重更高）
                        if volatility > 0:
                            score = momentum / volatility
                        else:
                            score = momentum
                        
                        # 调整权重（限制在合理范围内）
                        weight_adjustment = np.tanh(score) * 0.1  # 最大调整10%
                        adjusted_weight = base_weight + weight_adjustment
                        
                        # 应用权重限制
                        adjusted_weight = max(self.config.min_single_weight, 
                                            min(self.config.max_single_weight, adjusted_weight))
                        
                        weights[symbol] = adjusted_weight
                    else:
                        weights[symbol] = base_weight
                else:
                    weights[symbol] = base_weight
            
            # 标准化权重
            total_weight = sum(weights.values())
            if total_weight > 0:
                weights = {k: v/total_weight for k, v in weights.items()}
            
            return weights
            
        except Exception as e:
            logger.warning(f"动态权重计算失败: {e}")
            # 返回等权重
            return {symbol: 1.0/len(self.config.symbols) for symbol in self.config.symbols}
    
    def _get_rebalance_dates(self, date_index: pd.DatetimeIndex) -> List:
        """获取再平衡日期"""
        if self.config.rebalance_frequency == "daily":
            return date_index.tolist()
        elif self.config.rebalance_frequency == "weekly":
            return date_index[date_index.weekday == 0].tolist()  # 每周一
        elif self.config.rebalance_frequency == "monthly":
            return date_index[date_index.is_month_end].tolist()  # 月末
        elif self.config.rebalance_frequency == "quarterly":
            return date_index[date_index.is_quarter_end].tolist()  # 季末
        else:
            # 默认月度再平衡
            return date_index[date_index.is_month_end].tolist()
    
    def _check_rebalance_need(
        self, 
        current_weights: Dict[str, float], 
        target_weights: Dict[str, float]
    ) -> bool:
        """检查是否需要再平衡"""
        for symbol in self.config.symbols:
            current = current_weights.get(symbol, 0.0)
            target = target_weights.get(symbol, 0.0)
            
            if abs(current - target) > self.config.rebalance_threshold:
                return True
        
        return False
    
    def calculate_portfolio_returns(self, signals: pd.DataFrame) -> pd.Series:
        """
        计算投资组合收益
        
        Args:
            signals: 信号DataFrame
            
        Returns:
            投资组合收益序列
        """
        portfolio_returns = pd.Series(0.0, index=signals.index)
        
        for date in signals.index:
            daily_return = 0.0
            
            for symbol in self.config.symbols:
                weight = signals.loc[date, f'{symbol}_weight']
                
                if symbol in self._returns_data and date in self._returns_data[symbol].index:
                    asset_return = self._returns_data[symbol].loc[date]
                    daily_return += weight * asset_return
            
            # 扣除交易成本（在再平衡日）
            if signals.loc[date, 'rebalance']:
                daily_return -= self.config.transaction_cost
            
            portfolio_returns.loc[date] = daily_return
        
        return portfolio_returns
    
    def get_portfolio_metrics(self, signals: pd.DataFrame) -> Dict[str, Any]:
        """
        获取投资组合指标
        
        Args:
            signals: 信号DataFrame
            
        Returns:
            投资组合指标字典
        """
        portfolio_returns = self.calculate_portfolio_returns(signals)
        
        # 基础指标
        total_return = (1 + portfolio_returns).prod() - 1
        annual_return = (1 + total_return) ** (252 / len(portfolio_returns)) - 1
        volatility = portfolio_returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # 最大回撤
        cumulative = (1 + portfolio_returns).cumprod()
        peak = cumulative.expanding().max()
        drawdown = (cumulative - peak) / peak
        max_drawdown = drawdown.min()
        
        # 再平衡统计
        rebalance_count = signals['rebalance'].sum()
        
        # 权重统计
        weight_columns = [col for col in signals.columns if col.endswith('_weight')]
        avg_weights = signals[weight_columns].mean()
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'rebalance_count': rebalance_count,
            'avg_weights': avg_weights.to_dict(),
            'portfolio_returns': portfolio_returns
        }
    
    @classmethod
    def get_param_description(cls) -> Dict[str, str]:
        """获取参数描述"""
        base_desc = super().get_param_description()
        portfolio_desc = {
            'symbols': '股票代码列表',
            'weights': '权重分配',
            'rebalance_frequency': '再平衡频率',
            'momentum_period': '动量周期',
            'volatility_period': '波动率周期',
            'max_single_weight': '单个标的最大权重',
            'min_single_weight': '单个标的最小权重',
            'rebalance_threshold': '再平衡阈值',
            'transaction_cost': '交易成本'
        }
        return {**base_desc, **portfolio_desc}


__all__ = [
    "PortfolioStrategy",
    "PortfolioConfig"
]
