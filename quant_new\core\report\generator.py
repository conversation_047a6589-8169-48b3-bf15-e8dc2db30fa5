"""
报告生成器基类

提供统一的报告生成接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import pandas as pd
import numpy as np

from ...strategies.base import StrategyResult
from ...utils.logger import get_logger

logger = get_logger(__name__)


class ReportGenerator(ABC):
    """报告生成器抽象基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化报告生成器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.template_path = self.config.get('template_path', 'templates')
        self.output_path = self.config.get('output_path', 'reports')
        
        logger.info("报告生成器初始化完成")
    
    @abstractmethod
    def generate_single_strategy_report(
        self,
        result: StrategyResult,
        data: pd.DataFrame,
        output_file: str,
        **kwargs
    ) -> str:
        """
        生成单策略报告
        
        Args:
            result: 策略结果
            data: 原始数据
            output_file: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            生成的报告文件路径
        """
        pass
    
    @abstractmethod
    def generate_multi_strategy_report(
        self,
        results: Dict[str, StrategyResult],
        output_file: str,
        **kwargs
    ) -> str:
        """
        生成多策略对比报告
        
        Args:
            results: 多个策略结果
            output_file: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            生成的报告文件路径
        """
        pass
    
    @abstractmethod
    def generate_optimization_report(
        self,
        optimization_result: Dict[str, Any],
        output_file: str,
        **kwargs
    ) -> str:
        """
        生成参数优化报告
        
        Args:
            optimization_result: 优化结果
            output_file: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            生成的报告文件路径
        """
        pass
    
    def calculate_performance_metrics(self, result: StrategyResult) -> Dict[str, Any]:
        """
        计算绩效指标
        
        Args:
            result: 策略结果
            
        Returns:
            绩效指标字典
        """
        metrics = {
            'basic_metrics': {
                'total_return': result.total_return,
                'annual_return': result.annual_return,
                'volatility': result.volatility,
                'sharpe_ratio': result.sharpe_ratio,
                'max_drawdown': result.max_drawdown,
                'calmar_ratio': result.calmar_ratio,
                'sortino_ratio': result.sortino_ratio,
                'win_rate': result.win_rate
            },
            'trade_metrics': {
                'total_trades': result.total_trades,
                'winning_trades': result.winning_trades,
                'losing_trades': result.losing_trades,
                'avg_trade_return': result.avg_trade_return,
                'profit_factor': result.profit_factor
            },
            'risk_metrics': {
                'max_drawdown': result.max_drawdown,
                'volatility': result.volatility,
                'var_95': getattr(result, 'var_95', 0),
                'cvar_95': getattr(result, 'cvar_95', 0)
            }
        }
        
        return metrics
    
    def calculate_monthly_returns(self, returns: pd.Series) -> pd.DataFrame:
        """
        计算月度收益表
        
        Args:
            returns: 收益序列
            
        Returns:
            月度收益DataFrame
        """
        if returns.empty:
            return pd.DataFrame()
        
        # 计算月度收益
        monthly_returns = returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
        
        # 创建月度收益表
        monthly_table = pd.DataFrame()
        
        for year in monthly_returns.index.year.unique():
            year_data = monthly_returns[monthly_returns.index.year == year]
            
            year_row = {}
            for month in range(1, 13):
                month_data = year_data[year_data.index.month == month]
                if not month_data.empty:
                    year_row[f'M{month:02d}'] = month_data.iloc[0]
                else:
                    year_row[f'M{month:02d}'] = np.nan
            
            # 计算年度收益
            year_returns = returns[returns.index.year == year]
            if not year_returns.empty:
                year_row['Year'] = (1 + year_returns).prod() - 1
            else:
                year_row['Year'] = np.nan
            
            monthly_table[year] = pd.Series(year_row)
        
        return monthly_table.T
    
    def calculate_rolling_metrics(
        self, 
        returns: pd.Series, 
        window: int = 252
    ) -> Dict[str, pd.Series]:
        """
        计算滚动指标
        
        Args:
            returns: 收益序列
            window: 滚动窗口
            
        Returns:
            滚动指标字典
        """
        if len(returns) < window:
            return {}
        
        rolling_metrics = {}
        
        # 滚动收益率
        rolling_metrics['rolling_return'] = returns.rolling(window).apply(
            lambda x: (1 + x).prod() - 1
        )
        
        # 滚动波动率
        rolling_metrics['rolling_volatility'] = returns.rolling(window).std() * np.sqrt(252)
        
        # 滚动夏普比率
        rolling_metrics['rolling_sharpe'] = (
            rolling_metrics['rolling_return'] / rolling_metrics['rolling_volatility']
        )
        
        # 滚动最大回撤
        cumulative = (1 + returns).cumprod()
        rolling_peak = cumulative.rolling(window, min_periods=1).max()
        rolling_drawdown = (cumulative - rolling_peak) / rolling_peak
        rolling_metrics['rolling_max_drawdown'] = rolling_drawdown.rolling(window).min()
        
        return rolling_metrics
    
    def create_drawdown_series(self, returns: pd.Series) -> pd.Series:
        """
        创建回撤序列
        
        Args:
            returns: 收益序列
            
        Returns:
            回撤序列
        """
        cumulative = (1 + returns).cumprod()
        peak = cumulative.expanding().max()
        drawdown = (cumulative - peak) / peak
        
        return drawdown
    
    def identify_drawdown_periods(self, drawdown: pd.Series) -> List[Dict[str, Any]]:
        """
        识别回撤期
        
        Args:
            drawdown: 回撤序列
            
        Returns:
            回撤期列表
        """
        periods = []
        in_drawdown = False
        start_date = None
        peak_value = 0
        
        for date, dd in drawdown.items():
            if dd < 0 and not in_drawdown:
                # 开始回撤
                in_drawdown = True
                start_date = date
                peak_value = dd
            elif dd < 0 and in_drawdown:
                # 继续回撤
                peak_value = min(peak_value, dd)
            elif dd >= 0 and in_drawdown:
                # 回撤结束
                in_drawdown = False
                if start_date:
                    periods.append({
                        'start_date': start_date,
                        'end_date': date,
                        'duration_days': (date - start_date).days,
                        'max_drawdown': peak_value,
                        'recovery_date': date
                    })
        
        # 处理未结束的回撤
        if in_drawdown and start_date:
            periods.append({
                'start_date': start_date,
                'end_date': drawdown.index[-1],
                'duration_days': (drawdown.index[-1] - start_date).days,
                'max_drawdown': peak_value,
                'recovery_date': None
            })
        
        return periods
    
    def calculate_benchmark_comparison(
        self, 
        strategy_returns: pd.Series, 
        benchmark_returns: pd.Series
    ) -> Dict[str, Any]:
        """
        计算与基准的对比
        
        Args:
            strategy_returns: 策略收益
            benchmark_returns: 基准收益
            
        Returns:
            对比结果
        """
        # 对齐数据
        aligned_strategy, aligned_benchmark = strategy_returns.align(
            benchmark_returns, join='inner'
        )
        
        if aligned_strategy.empty or aligned_benchmark.empty:
            return {}
        
        # 计算超额收益
        excess_returns = aligned_strategy - aligned_benchmark
        
        # 基础指标
        strategy_total = (1 + aligned_strategy).prod() - 1
        benchmark_total = (1 + aligned_benchmark).prod() - 1
        excess_total = strategy_total - benchmark_total
        
        # 年化指标
        periods = len(aligned_strategy)
        strategy_annual = (1 + strategy_total) ** (252 / periods) - 1
        benchmark_annual = (1 + benchmark_total) ** (252 / periods) - 1
        
        # 波动率
        strategy_vol = aligned_strategy.std() * np.sqrt(252)
        benchmark_vol = aligned_benchmark.std() * np.sqrt(252)
        
        # 跟踪误差
        tracking_error = excess_returns.std() * np.sqrt(252)
        
        # 信息比率
        information_ratio = (
            excess_returns.mean() / excess_returns.std() * np.sqrt(252)
            if excess_returns.std() > 0 else 0
        )
        
        # 贝塔系数
        covariance = np.cov(aligned_strategy, aligned_benchmark)[0, 1]
        benchmark_variance = np.var(aligned_benchmark)
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
        
        # 阿尔法
        alpha = strategy_annual - beta * benchmark_annual
        
        # 相关系数
        correlation = aligned_strategy.corr(aligned_benchmark)
        
        return {
            'strategy_total_return': strategy_total,
            'benchmark_total_return': benchmark_total,
            'excess_return': excess_total,
            'strategy_annual_return': strategy_annual,
            'benchmark_annual_return': benchmark_annual,
            'strategy_volatility': strategy_vol,
            'benchmark_volatility': benchmark_vol,
            'tracking_error': tracking_error,
            'information_ratio': information_ratio,
            'beta': beta,
            'alpha': alpha,
            'correlation': correlation
        }
    
    def format_percentage(self, value: float, decimals: int = 2) -> str:
        """格式化百分比"""
        if pd.isna(value):
            return "N/A"
        return f"{value * 100:.{decimals}f}%"
    
    def format_number(self, value: float, decimals: int = 2) -> str:
        """格式化数字"""
        if pd.isna(value):
            return "N/A"
        return f"{value:.{decimals}f}"
    
    def format_currency(self, value: float, currency: str = "¥") -> str:
        """格式化货币"""
        if pd.isna(value):
            return "N/A"
        return f"{currency}{value:,.2f}"


__all__ = [
    "ReportGenerator"
]
