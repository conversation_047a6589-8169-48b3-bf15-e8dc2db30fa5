"""
MACD策略

基于MACD指标的趋势跟踪策略。
"""

from typing import Dict
import pandas as pd
import numpy as np
from pydantic import Field

from ..base import BaseStrategy, StrategyConfig
from ...utils.indicators.trend import macd
from ...utils.logger import get_logger

logger = get_logger(__name__)


class MACDConfig(StrategyConfig):
    """MACD策略配置"""
    
    strategy_name: str = Field(default="MACD策略", description="策略名称")
    
    # MACD参数
    fast_period: int = Field(default=12, description="快线周期")
    slow_period: int = Field(default=26, description="慢线周期")
    signal_period: int = Field(default=9, description="信号线周期")
    
    # 信号参数
    macd_threshold: float = Field(default=0.0, description="MACD阈值")
    histogram_threshold: float = Field(default=0.0, description="柱状图阈值")
    
    # 过滤条件
    volume_filter: bool = Field(default=True, description="是否启用成交量过滤")
    volume_ma_period: int = Field(default=20, description="成交量均线周期")
    volume_ratio_threshold: float = Field(default=1.2, description="成交量比率阈值")
    
    # 趋势过滤
    trend_filter: bool = Field(default=True, description="是否启用趋势过滤")
    trend_ma_period: int = Field(default=50, description="趋势均线周期")
    
    def __init__(self, **data):
        super().__init__(**data)
        # 确保慢线周期大于快线周期
        if self.fast_period >= self.slow_period:
            raise ValueError("快线周期必须小于慢线周期")


class MACDStrategy(BaseStrategy):
    """MACD策略实现"""
    
    def __init__(self, config: MACDConfig):
        """
        初始化MACD策略
        
        Args:
            config: MACD策略配置
        """
        super().__init__(config)
        self.config: MACDConfig = config
    
    def calculate_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        计算技术指标
        
        Args:
            data: 价格数据
            
        Returns:
            技术指标字典
        """
        indicators = {}
        
        # 计算MACD指标
        macd_line, signal_line, histogram = macd(
            data['close'], 
            self.config.fast_period,
            self.config.slow_period,
            self.config.signal_period
        )
        
        indicators['macd'] = macd_line
        indicators['signal'] = signal_line
        indicators['histogram'] = histogram
        
        # 计算成交量指标（如果启用成交量过滤）
        if self.config.volume_filter:
            indicators['volume_ma'] = data['volume'].rolling(
                window=self.config.volume_ma_period,
                min_periods=self.config.volume_ma_period
            ).mean()
            indicators['volume_ratio'] = data['volume'] / indicators['volume_ma']
        
        # 计算趋势指标（如果启用趋势过滤）
        if self.config.trend_filter:
            indicators['trend_ma'] = data['close'].rolling(
                window=self.config.trend_ma_period,
                min_periods=self.config.trend_ma_period
            ).mean()
        
        return indicators
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            
        Returns:
            信号DataFrame
        """
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['strength'] = 0.0
        signals['price'] = data['close']
        
        # 获取技术指标
        macd_line = self._indicators['macd']
        signal_line = self._indicators['signal']
        histogram = self._indicators['histogram']
        
        # 基本MACD信号
        # 买入信号：MACD线上穿信号线，且MACD > 阈值
        buy_condition = (
            (macd_line > signal_line) & 
            (macd_line.shift(1) <= signal_line.shift(1)) &
            (macd_line > self.config.macd_threshold)
        )
        
        # 卖出信号：MACD线下穿信号线，或MACD < 阈值
        sell_condition = (
            (macd_line < signal_line) & 
            (macd_line.shift(1) >= signal_line.shift(1))
        ) | (macd_line < -self.config.macd_threshold)
        
        # 应用成交量过滤
        if self.config.volume_filter:
            volume_ratio = self._indicators['volume_ratio']
            volume_condition = volume_ratio > self.config.volume_ratio_threshold
            buy_condition = buy_condition & volume_condition
        
        # 应用趋势过滤
        if self.config.trend_filter:
            trend_ma = self._indicators['trend_ma']
            trend_up = data['close'] > trend_ma
            trend_down = data['close'] < trend_ma
            
            buy_condition = buy_condition & trend_up
            sell_condition = sell_condition & trend_down
        
        # 设置信号
        signals.loc[buy_condition, 'signal'] = 1
        signals.loc[sell_condition, 'signal'] = -1
        
        # 计算信号强度
        self._calculate_signal_strength(signals, data)
        
        # 过滤弱信号
        weak_signals = signals['strength'] < self.config.signal_threshold
        signals.loc[weak_signals, 'signal'] = 0
        signals.loc[weak_signals, 'strength'] = 0.0
        
        logger.debug(f"生成MACD信号: 买入{(signals['signal'] > 0).sum()}个, 卖出{(signals['signal'] < 0).sum()}个")
        
        return signals
    
    def _calculate_signal_strength(self, signals: pd.DataFrame, data: pd.DataFrame):
        """
        计算信号强度
        
        Args:
            signals: 信号DataFrame
            data: 价格数据
        """
        macd_line = self._indicators['macd']
        signal_line = self._indicators['signal']
        histogram = self._indicators['histogram']
        
        # 基于MACD线与信号线的距离计算强度
        macd_diff = abs(macd_line - signal_line)
        macd_diff_normalized = macd_diff / data['close'] * 100  # 标准化为百分比
        
        # 基于柱状图的变化率计算强度
        histogram_change = histogram.diff()
        histogram_strength = abs(histogram_change) / data['close'] * 100
        
        # 综合强度计算
        base_strength = np.minimum(macd_diff_normalized * 10, 1.0)  # 限制在0-1之间
        histogram_boost = np.minimum(histogram_strength * 5, 0.3)   # 柱状图加成
        
        total_strength = np.minimum(base_strength + histogram_boost, 1.0)
        
        # 只对有信号的点计算强度
        signal_mask = signals['signal'] != 0
        signals.loc[signal_mask, 'strength'] = total_strength[signal_mask]
    
    @classmethod
    def get_param_description(cls) -> Dict[str, str]:
        """获取参数描述"""
        base_desc = super().get_param_description()
        macd_desc = {
            'fast_period': '快线周期',
            'slow_period': '慢线周期',
            'signal_period': '信号线周期',
            'macd_threshold': 'MACD阈值',
            'histogram_threshold': '柱状图阈值',
            'volume_filter': '成交量过滤',
            'volume_ma_period': '成交量均线周期',
            'volume_ratio_threshold': '成交量比率阈值',
            'trend_filter': '趋势过滤',
            'trend_ma_period': '趋势均线周期'
        }
        return {**base_desc, **macd_desc}
    
    @classmethod
    def get_param_constraints(cls) -> Dict[str, Dict]:
        """获取参数约束"""
        base_constraints = super().get_param_constraints()
        macd_constraints = {
            'fast_period': {'min': 5, 'max': 20, 'type': 'int'},
            'slow_period': {'min': 15, 'max': 50, 'type': 'int'},
            'signal_period': {'min': 3, 'max': 15, 'type': 'int'},
            'macd_threshold': {'min': -1.0, 'max': 1.0, 'type': 'float'},
            'histogram_threshold': {'min': -1.0, 'max': 1.0, 'type': 'float'},
            'volume_ma_period': {'min': 5, 'max': 50, 'type': 'int'},
            'volume_ratio_threshold': {'min': 1.0, 'max': 3.0, 'type': 'float'},
            'trend_ma_period': {'min': 20, 'max': 200, 'type': 'int'}
        }
        return {**base_constraints, **macd_constraints}


__all__ = [
    "MACDStrategy",
    "MACDConfig"
]
