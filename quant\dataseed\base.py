"""
数据源基类

定义数据源的统一接口，所有具体的数据源实现都应该继承此基类。
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, List, Union, Any
import pandas as pd


class BaseDataSeed(ABC):
    """数据源基类，定义获取数据的统一接口"""
    
    @abstractmethod
    def get_data(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d') -> pd.DataFrame:
        """
        获取历史数据
        
        参数:
            symbol (str): 证券代码
            start_date (str): 开始日期，格式：YYYY-MM-DD
            end_date (str): 结束日期，格式：YYYY-MM-DD
            timeframe (str): 时间周期，支持 '1m', '1h', '1d'
            
        返回:
            pd.DataFrame: 包含OHLCV数据的DataFrame，列名为：
                         ['open', 'high', 'low', 'close', 'volume']
        """
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证数据格式是否正确
        
        参数:
            data (pd.DataFrame): 待验证的数据
            
        返回:
            bool: 数据格式是否正确
        """
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        # 检查必需的列是否存在
        if not all(col in data.columns for col in required_columns):
            return False
            
        # 检查数据是否为空
        if data.empty:
            return False
            
        # 检查是否有NaN值
        if data[required_columns].isnull().any().any():
            return False
            
        return True
    
    def standardize_columns(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名，确保列名符合要求
        
        参数:
            data (pd.DataFrame): 原始数据
            
        返回:
            pd.DataFrame: 标准化后的数据
        """
        # 常见的列名映射
        column_mapping = {
            '开盘': 'open', '开盘价': 'open', 'Open': 'open',
            '最高': 'high', '最高价': 'high', 'High': 'high',
            '最低': 'low', '最低价': 'low', 'Low': 'low',
            '收盘': 'close', '收盘价': 'close', 'Close': 'close',
            '成交量': 'volume', 'Volume': 'volume', '成交额': 'volume',
            '日期': 'date', 'Date': 'date', 'datetime': 'date'
        }
        
        # 重命名列
        data = data.rename(columns=column_mapping)
        
        # 确保数值列为float类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data
