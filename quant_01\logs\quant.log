2025-05-27 21:47:05 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:47:05 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:47:06 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:47:06 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:47:06 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:47:06 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:47:06 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:47:06 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:47:06 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:47:06 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:48:24 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:48:24 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:48:25 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:48:25 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:48:25 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:48:25 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:48:26 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:48:26 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:48:26 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:48:26 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:48:53 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:48:53 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:48:53 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:48:53 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:48:53 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:48:53 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:48:54 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:48:54 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:48:54 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:48:54 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:52:25 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:52:25 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:52:26 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:52:26 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:52:26 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:52:26 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:52:26 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:52:26 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:52:26 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:52:26 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:52:26 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: akshare
2025-05-27 21:52:26 | INFO | dataseed.akshare_source:__init__:56 | AkShare数据源初始化完成
2025-05-27 21:52:26 | INFO | __main__:_setup_data_source:170 | 数据源已设置: akshare
2025-05-27 21:52:26 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:52:26 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:52:26 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Fast (None)
2025-05-27 21:52:26 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:52:26 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Slow (None)
2025-05-27 21:52:26 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:52:26 | INFO | __main__:_register_default_strategies:186 | 默认策略已注册
2025-05-27 21:52:26 | INFO | __main__:__init__:149 | Quant_01 快速开始初始化完成
2025-05-27 21:52:26 | INFO | __main__:run_simple_backtest:207 | 开始简单回测: macd on 000001
2025-05-27 21:52:26 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:52:26 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 21:52:26 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:52:26 | INFO | __main__:run_simple_backtest:232 | 简单回测完成
2025-05-27 21:52:26 | INFO | __main__:compare_strategies:261 | 开始策略对比: ['macd', 'macd_fast', 'macd_slow']
2025-05-27 21:52:26 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:52:26 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:52:26 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:52:26 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Fast
2025-05-27 21:52:26 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:52:26 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Fast, 耗时: 0.03秒
2025-05-27 21:52:26 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Slow
2025-05-27 21:52:26 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=7, 卖出信号=6
2025-05-27 21:52:26 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Slow, 耗时: 0.03秒
2025-05-27 21:52:26 | INFO | __main__:compare_strategies:288 | 策略对比完成
2025-05-27 21:52:26 | INFO | __main__:batch_backtest:318 | 开始批量回测: 3个标的
2025-05-27 21:52:26 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:52:26 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:52:26 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:52:27 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:52:27 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=8, 卖出信号=7
2025-05-27 21:52:27 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:52:27 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:52:27 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=6, 卖出信号=6
2025-05-27 21:52:27 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:52:27 | INFO | __main__:batch_backtest:346 | 批量回测完成
2025-05-27 21:52:27 | INFO | __main__:optimize_strategy:394 | 开始策略参数优化: grid 方法
2025-05-27 21:52:27 | ERROR | __main__:optimize_strategy:398 | 优化器模块不可用，请检查导入
2025-05-27 21:54:21 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:54:21 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:54:22 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:54:22 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:54:22 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:54:22 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:54:22 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:54:22 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:54:22 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:54:22 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:54:22 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: akshare
2025-05-27 21:54:22 | INFO | dataseed.akshare_source:__init__:56 | AkShare数据源初始化完成
2025-05-27 21:54:22 | INFO | __main__:_setup_data_source:172 | 数据源已设置: akshare
2025-05-27 21:54:22 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:54:22 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:54:22 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Fast (None)
2025-05-27 21:54:22 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:54:22 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Slow (None)
2025-05-27 21:54:22 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:54:22 | INFO | __main__:_register_default_strategies:188 | 默认策略已注册
2025-05-27 21:54:22 | INFO | __main__:__init__:151 | Quant_01 快速开始初始化完成
2025-05-27 21:54:22 | INFO | __main__:run_simple_backtest:209 | 开始简单回测: macd on 000001
2025-05-27 21:54:22 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:54:22 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 21:54:22 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:54:22 | INFO | __main__:run_simple_backtest:234 | 简单回测完成
2025-05-27 21:54:22 | INFO | __main__:compare_strategies:263 | 开始策略对比: ['macd', 'macd_fast', 'macd_slow']
2025-05-27 21:54:22 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:54:22 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:54:22 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:54:22 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Fast
2025-05-27 21:54:22 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:54:22 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Fast, 耗时: 0.04秒
2025-05-27 21:54:22 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Slow
2025-05-27 21:54:22 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=7, 卖出信号=6
2025-05-27 21:54:22 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Slow, 耗时: 0.03秒
2025-05-27 21:54:22 | INFO | __main__:compare_strategies:290 | 策略对比完成
2025-05-27 21:54:22 | INFO | __main__:batch_backtest:320 | 开始批量回测: 3个标的
2025-05-27 21:54:22 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:54:22 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:54:22 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:54:23 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:54:23 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=8, 卖出信号=7
2025-05-27 21:54:23 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:54:23 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:54:23 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=6, 卖出信号=6
2025-05-27 21:54:23 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:54:23 | INFO | __main__:batch_backtest:348 | 批量回测完成
2025-05-27 21:54:23 | INFO | __main__:optimize_strategy:396 | 开始策略参数优化: grid 方法
2025-05-27 21:54:23 | ERROR | __main__:optimize_strategy:400 | 优化器模块不可用，请检查导入
2025-05-27 21:58:35 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:58:35 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:58:36 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:58:36 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:58:36 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:58:36 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:58:36 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:58:36 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:58:36 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:58:36 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:58:36 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: akshare
2025-05-27 21:58:36 | INFO | dataseed.akshare_source:__init__:56 | AkShare数据源初始化完成
2025-05-27 21:58:36 | INFO | __main__:_setup_data_source:172 | 数据源已设置: akshare
2025-05-27 21:58:36 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:36 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:58:36 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Fast (None)
2025-05-27 21:58:36 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:58:36 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Slow (None)
2025-05-27 21:58:36 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:58:36 | INFO | __main__:_register_default_strategies:188 | 默认策略已注册
2025-05-27 21:58:36 | INFO | __main__:__init__:151 | Quant_01 快速开始初始化完成
2025-05-27 21:58:36 | INFO | __main__:run_simple_backtest:209 | 开始简单回测: macd on 000001
2025-05-27 21:58:36 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:36 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.12秒
2025-05-27 21:58:37 | INFO | __main__:run_simple_backtest:234 | 简单回测完成
2025-05-27 21:58:37 | INFO | __main__:compare_strategies:263 | 开始策略对比: ['macd', 'macd_fast', 'macd_slow']
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Fast
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Fast, 耗时: 0.04秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Slow
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=7, 卖出信号=6
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Slow, 耗时: 0.03秒
2025-05-27 21:58:37 | INFO | __main__:compare_strategies:290 | 策略对比完成
2025-05-27 21:58:37 | INFO | __main__:batch_backtest:320 | 开始批量回测: 3个标的
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=8, 卖出信号=7
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=6, 卖出信号=6
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:58:37 | INFO | __main__:batch_backtest:348 | 批量回测完成
2025-05-27 21:58:37 | INFO | __main__:optimize_strategy:396 | 开始策略参数优化: grid 方法
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=4, 卖出信号=4
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=9
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=9
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=12
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=12
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=5
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=5
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=3
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=9
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=9
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:37 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:37 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:37 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=12
2025-05-27 21:58:37 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:37 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=15, 卖出信号=15
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=5
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=5
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=3
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=9
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=9
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=15
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=2, 卖出信号=1
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=5
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=5
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=14, 卖出信号=14
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=5
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=5
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=9
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=15, 卖出信号=15
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=9
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:38 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=14
2025-05-27 21:58:38 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 21:58:38 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:38 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=12
2025-05-27 21:58:38 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=5
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=18
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=5
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=9
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=14, 卖出信号=13
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=9
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=12, 卖出信号=12
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=5
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=5
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=9
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=9
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=12
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=12, 卖出信号=12
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=12
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=5
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=15
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=5
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=9
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=14
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=9
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=10, 卖出信号=10
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:58:39 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:58:39 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:58:39 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:58:39 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:58:39 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:58:39 | INFO | __main__:optimize_strategy:448 | 策略参数优化完成
2025-05-27 21:58:39 | INFO | __main__:compare_optimization_methods:497 | 开始比较优化方法: ['grid', 'random']
2025-05-27 21:58:39 | ERROR | __main__:compare_optimization_methods:536 | 优化方法比较失败: 'SimpleOptimizationManager' object has no attribute 'compare_methods'
2025-05-27 21:59:06 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 21:59:06 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 21:59:07 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 21:59:07 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 21:59:07 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 21:59:07 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 21:59:07 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 21:59:07 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 21:59:07 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 21:59:07 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 21:59:08 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: akshare
2025-05-27 21:59:08 | INFO | dataseed.akshare_source:__init__:56 | AkShare数据源初始化完成
2025-05-27 21:59:08 | INFO | __main__:_setup_data_source:172 | 数据源已设置: akshare
2025-05-27 21:59:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:59:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Fast (None)
2025-05-27 21:59:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:59:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Slow (None)
2025-05-27 21:59:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:59:08 | INFO | __main__:_register_default_strategies:188 | 默认策略已注册
2025-05-27 21:59:08 | INFO | __main__:__init__:151 | Quant_01 快速开始初始化完成
2025-05-27 21:59:08 | INFO | __main__:run_simple_backtest:209 | 开始简单回测: macd on 000001
2025-05-27 21:59:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 21:59:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 21:59:08 | INFO | __main__:run_simple_backtest:234 | 简单回测完成
2025-05-27 21:59:08 | INFO | __main__:compare_strategies:263 | 开始策略对比: ['macd', 'macd_fast', 'macd_slow']
2025-05-27 21:59:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:59:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:59:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Fast
2025-05-27 21:59:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:59:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Fast, 耗时: 0.04秒
2025-05-27 21:59:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Slow
2025-05-27 21:59:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=7, 卖出信号=6
2025-05-27 21:59:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Slow, 耗时: 0.05秒
2025-05-27 21:59:08 | INFO | __main__:compare_strategies:290 | 策略对比完成
2025-05-27 21:59:08 | INFO | __main__:batch_backtest:320 | 开始批量回测: 3个标的
2025-05-27 21:59:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 21:59:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:59:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=8, 卖出信号=7
2025-05-27 21:59:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=6, 卖出信号=6
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:59:09 | INFO | __main__:batch_backtest:348 | 批量回测完成
2025-05-27 21:59:09 | INFO | __main__:optimize_strategy:396 | 开始策略参数优化: grid 方法
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=4, 卖出信号=4
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=9
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=9
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=12
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=12
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=5
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=5
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=3
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=9
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=9
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=12
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=15, 卖出信号=15
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=12
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=5
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=5
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=3
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=9
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=9
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=12
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=15
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=12
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 21:59:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=2, 卖出信号=1
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=14, 卖出信号=14
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=9
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=15, 卖出信号=15
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=9
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=14
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=18
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=9
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=14, 卖出信号=13
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=9
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=12, 卖出信号=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 21:59:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=5
2025-05-27 21:59:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=9
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=9
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=12
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=12, 卖出信号=12
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=12
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=5
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=15
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=5
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=9
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=14
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=9
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=10, 卖出信号=10
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 21:59:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 21:59:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 21:59:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 21:59:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 21:59:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 21:59:11 | INFO | __main__:optimize_strategy:448 | 策略参数优化完成
2025-05-27 21:59:11 | INFO | __main__:compare_optimization_methods:497 | 开始比较优化方法: ['grid', 'random']
2025-05-27 21:59:11 | ERROR | __main__:compare_optimization_methods:536 | 优化方法比较失败: 'SimpleOptimizationManager' object has no attribute 'compare_methods'
2025-05-27 22:00:04 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 22:00:04 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 22:00:06 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 22:00:06 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 22:00:06 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 22:00:06 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 22:00:06 | INFO | strategies.factory:register:69 | 策略已注册: macd
2025-05-27 22:00:06 | INFO | strategies.factory:register:69 | 策略已注册: rsi
2025-05-27 22:00:06 | INFO | strategies.factory:_register_builtin_strategies:42 | 内置策略注册完成
2025-05-27 22:00:06 | INFO | strategies.factory:__init__:31 | 策略工厂初始化完成
2025-05-27 22:00:06 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: akshare
2025-05-27 22:00:06 | INFO | dataseed.akshare_source:__init__:56 | AkShare数据源初始化完成
2025-05-27 22:00:06 | INFO | __main__:_setup_data_source:172 | 数据源已设置: akshare
2025-05-27 22:00:06 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:06 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 22:00:06 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Fast (None)
2025-05-27 22:00:06 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 22:00:06 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD_Slow (None)
2025-05-27 22:00:06 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 22:00:06 | INFO | __main__:_register_default_strategies:188 | 默认策略已注册
2025-05-27 22:00:06 | INFO | __main__:__init__:151 | Quant_01 快速开始初始化完成
2025-05-27 22:00:06 | INFO | __main__:run_simple_backtest:209 | 开始简单回测: macd on 000001
2025-05-27 22:00:06 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:06 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 22:00:06 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:06 | INFO | __main__:run_simple_backtest:234 | 简单回测完成
2025-05-27 22:00:06 | INFO | __main__:compare_strategies:263 | 开始策略对比: ['macd', 'macd_fast', 'macd_slow']
2025-05-27 22:00:07 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:07 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 22:00:07 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 22:00:07 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Fast
2025-05-27 22:00:07 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 22:00:07 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Fast, 耗时: 0.04秒
2025-05-27 22:00:07 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD_Slow
2025-05-27 22:00:07 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=7, 卖出信号=6
2025-05-27 22:00:07 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD_Slow, 耗时: 0.03秒
2025-05-27 22:00:07 | INFO | __main__:compare_strategies:290 | 策略对比完成
2025-05-27 22:00:07 | INFO | __main__:batch_backtest:320 | 开始批量回测: 3个标的
2025-05-27 22:00:07 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:07 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=11, 卖出信号=10
2025-05-27 22:00:07 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.04秒
2025-05-27 22:00:07 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:07 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=8, 卖出信号=7
2025-05-27 22:00:07 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 22:00:07 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:07 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=6, 卖出信号=6
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.03秒
2025-05-27 22:00:08 | INFO | __main__:batch_backtest:348 | 批量回测完成
2025-05-27 22:00:08 | INFO | __main__:optimize_strategy:396 | 开始策略参数优化: grid 方法
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=4, 卖出信号=4
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=9
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=9
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=12
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=12
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=3
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=9
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=9
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=12
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=15, 卖出信号=15
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=12
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=19
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=3
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=9
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=9
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=12
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=15
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=12
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=12
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=12
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=2, 卖出信号=1
2025-05-27 22:00:08 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:08 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:08 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=5
2025-05-27 22:00:08 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:08 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=17
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=5
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=14, 卖出信号=14
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=5
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=5
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=9
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=15, 卖出信号=15
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=9
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=14
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=5
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=18
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=5
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=9
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=14, 卖出信号=13
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=9
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=12, 卖出信号=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=5
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=16
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=5
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=9
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=13
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=9
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=12, 卖出信号=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=12
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:09 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:09 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=5
2025-05-27 22:00:09 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:09 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=15
2025-05-27 22:00:09 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=5
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=2
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=9
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=13, 卖出信号=14
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=9
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=10, 卖出信号=10
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=3, 卖出信号=1
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.02秒
2025-05-27 22:00:10 | INFO | __main__:optimize_strategy:448 | 策略参数优化完成
2025-05-27 22:00:10 | INFO | __main__:compare_optimization_methods:497 | 开始比较优化方法: ['grid', 'random']
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=5
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=24, 卖出信号=24
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=9
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=20, 卖出信号=19
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=21, signal=12
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=21, 卖出信号=20
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.08秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=5
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=23, 卖出信号=23
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=9
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=21, 卖出信号=20
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=12
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=18
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.11秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=5
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=23, 卖出信号=23
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=9
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=20, 卖出信号=19
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=12
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=20, 卖出信号=17
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.08秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=21, 卖出信号=20
2025-05-27 22:00:10 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.08秒
2025-05-27 22:00:10 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:10 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 22:00:10 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:10 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=16
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.08秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=12
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=15
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=5
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=21, 卖出信号=20
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=9
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=17
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=26, signal=12
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=15
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=5
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=20, 卖出信号=19
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=9
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=17
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.08秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=12
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=16
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.09秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=5
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=22, 卖出信号=21
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.08秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=9
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=18, 卖出信号=16
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=14
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.05秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=5
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=20, 卖出信号=19
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.08秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=9
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=15
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=12
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=14
2025-05-27 22:00:11 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:11 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:11 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=5
2025-05-27 22:00:11 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:11 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=20, 卖出信号=18
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=9
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=16
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=14, 卖出信号=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=9
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=16
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=9
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=21, 卖出信号=20
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=21, 卖出信号=20
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=14
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=16
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=21, signal=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=14
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=26, signal=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=18
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.08秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=5
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=21, 卖出信号=20
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.07秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=21, signal=9
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=16
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=17, 卖出信号=16
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=26, signal=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=16, 卖出信号=14
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=14, 卖出信号=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:12 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:12 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=8, slow=35, signal=12
2025-05-27 22:00:12 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:12 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=20, 卖出信号=17
2025-05-27 22:00:13 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:13 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:13 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=16, slow=35, signal=5
2025-05-27 22:00:13 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:13 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=20, 卖出信号=18
2025-05-27 22:00:13 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:13 | INFO | strategies.base.strategy:__init__:188 | 策略初始化: MACD (None)
2025-05-27 22:00:13 | INFO | strategies.single.macd:__init__:103 | MACD策略初始化完成: fast=12, slow=35, signal=9
2025-05-27 22:00:13 | INFO | strategies.base.strategy:initialize:230 | 策略初始化完成: MACD
2025-05-27 22:00:13 | INFO | strategies.single.macd:generate_signals:173 | MACD信号生成完成: 买入信号=19, 卖出信号=17
2025-05-27 22:00:13 | INFO | strategies.base.strategy:run:265 | 策略运行完成: MACD, 耗时: 0.06秒
2025-05-27 22:00:13 | INFO | __main__:compare_optimization_methods:532 | 优化方法比较完成
