{"symbol": "688455", "strategy": "ma_cross", "report_date": "2025-05-26 18:20:30", "metrics": {"total_return": -0.1761184774964543, "annual_return": -0.25337623086801997, "max_drawdown": -0.19615496698540213, "sharpe_ratio": -1.4856549491576703, "total_trades": 7, "win_rate": 0.14285714285714285, "avg_trade_return": -0.027127579034003146, "best_trade": 0.002348715095103453, "worst_trade": -0.04706864240410748, "avg_trade_duration": 10.857142857142858, "volatility": 0.15376706999751255, "var_95": -0.01857511628970505, "var_99": -0.03549505468270398, "skewness": -0.6009286883102811, "kurtosis": 13.299921632011241, "calmar_ratio": -1.2917145803749963, "sortino_ratio": -1.244474504815027}, "trades": [{"entry_date": "2023-02-06", "exit_date": "2023-02-21", "entry_price": 14.934919999999998, "exit_price": 14.71527, "size": 6693.709038769597, "pnl": -1529.814113806992, "return_pct": -0.015302730580411341, "duration": 15}, {"entry_date": "2023-03-09", "exit_date": "2023-03-17", "entry_price": 15.495479999999999, "exit_price": 14.77521, "size": 6352.862492164553, "pnl": -4633.46792656524, "return_pct": -0.04706864240410748, "duration": 8}, {"entry_date": "2023-04-07", "exit_date": "2023-04-13", "entry_price": 15.795779999999995, "exit_price": 15.14484, "size": 5938.837802692346, "pnl": -3920.952476392943, "return_pct": -0.04179737790726348, "duration": 6}, {"entry_date": "2023-05-23", "exit_date": "2023-06-07", "entry_price": 14.714699999999997, "exit_price": 14.05593, "size": 6108.775499574017, "pnl": -4077.004031749738, "return_pct": -0.045356085343227874, "duration": 15}, {"entry_date": "2023-06-26", "exit_date": "2023-07-10", "entry_price": 14.134119999999998, "exit_price": 14.17581, "size": 6071.337836824737, "pnl": 201.55032966718727, "return_pct": 0.002348715095103453, "duration": 14}, {"entry_date": "2023-09-04", "exit_date": "2023-09-15", "entry_price": 13.673659999999998, "exit_price": 13.19679, "size": 6290.525538072734, "pnl": -3050.4716889240817, "return_pct": -0.03546461847084085, "duration": 11}, {"entry_date": "2023-11-03", "exit_date": "2023-12-08", "entry_price": 12.872859999999998, "exit_price": 12.7872, "size": 6444.950897240094, "pnl": -601.6878418736325, "return_pct": -0.007252313627274455, "duration": 35}], "chart_data": {"portfolio_value": {"dates": ["2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01", "2023-03-02", "2023-03-03", "2023-03-06", "2023-03-07", "2023-03-08", "2023-03-09", "2023-03-10", "2023-03-13", "2023-03-14", "2023-03-15", "2023-03-16", "2023-03-17", "2023-03-20", "2023-03-21", "2023-03-22", "2023-03-23", "2023-03-24", "2023-03-27", "2023-03-28", "2023-03-29", "2023-03-30", "2023-03-31", "2023-04-03", "2023-04-04", "2023-04-06", "2023-04-07", "2023-04-10", "2023-04-11", "2023-04-12", "2023-04-13", "2023-04-14", "2023-04-17", "2023-04-18", "2023-04-19", "2023-04-20", "2023-04-21", "2023-04-24", "2023-04-25", "2023-04-26", "2023-04-27", "2023-04-28", "2023-05-04", "2023-05-05", "2023-05-08", "2023-05-09", "2023-05-10", "2023-05-11", "2023-05-12", "2023-05-15", "2023-05-16", "2023-05-17", "2023-05-18", "2023-05-19", "2023-05-22", "2023-05-23", "2023-05-24", "2023-05-25", "2023-05-26", "2023-05-29", "2023-05-30", "2023-05-31", "2023-06-01", "2023-06-02", "2023-06-05", "2023-06-06", "2023-06-07", "2023-06-08", "2023-06-09", "2023-06-12", "2023-06-13", "2023-06-14", "2023-06-15", "2023-06-16", "2023-06-19", "2023-06-20", "2023-06-21", "2023-06-26", "2023-06-27", "2023-06-28", "2023-06-29", "2023-06-30", "2023-07-03", "2023-07-04", "2023-07-05", "2023-07-06", "2023-07-07", "2023-07-10", "2023-07-11", "2023-07-12", "2023-07-13", "2023-07-14", "2023-07-17", "2023-07-18", "2023-07-19", "2023-07-20", "2023-07-21", "2023-07-24", "2023-07-25", "2023-07-26", "2023-07-27", "2023-07-28", "2023-07-31", "2023-08-01", "2023-08-02", "2023-08-03", "2023-08-04", "2023-08-07", "2023-08-08", "2023-08-09", "2023-08-10", "2023-08-11", "2023-08-14", "2023-08-15", "2023-08-16", "2023-08-17", "2023-08-18", "2023-08-21", "2023-08-22", "2023-08-23", "2023-08-24", "2023-08-25", "2023-08-28", "2023-08-29", "2023-08-30", "2023-08-31", "2023-09-01", "2023-09-04", "2023-09-05", "2023-09-06", "2023-09-07", "2023-09-08", "2023-09-11", "2023-09-12", "2023-09-13", "2023-09-14", "2023-09-15", "2023-09-18", "2023-09-19", "2023-09-20", "2023-09-21", "2023-09-22", "2023-09-25", "2023-09-26", "2023-09-27", "2023-09-28", "2023-10-09", "2023-10-10", "2023-10-11", "2023-10-12", "2023-10-13", "2023-10-16", "2023-10-17", "2023-10-18", "2023-10-19", "2023-10-20", "2023-10-23", "2023-10-24", "2023-10-25", "2023-10-26", "2023-10-27", "2023-10-30", "2023-10-31", "2023-11-01", "2023-11-02", "2023-11-03", "2023-11-06", "2023-11-07", "2023-11-08", "2023-11-09", "2023-11-10", "2023-11-13", "2023-11-14", "2023-11-15", "2023-11-16", "2023-11-17", "2023-11-20", "2023-11-21", "2023-11-22", "2023-11-23", "2023-11-24", "2023-11-27", "2023-11-28", "2023-11-29", "2023-11-30", "2023-12-01", "2023-12-04", "2023-12-05", "2023-12-06", "2023-12-07", "2023-12-08", "2023-12-11", "2023-12-12", "2023-12-13", "2023-12-14", "2023-12-15", "2023-12-18", "2023-12-19", "2023-12-20", "2023-12-21", "2023-12-22", "2023-12-25", "2023-12-26", "2023-12-27", "2023-12-28", "2023-12-29"], "values": [100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 100000.0, 99870.13885844238, 100740.32103348244, 101008.06939503322, 101543.56611813478, 101409.6919373594, 101744.37738929786, 102279.87411239944, 102079.06284123636, 98397.52286991307, 98062.8374179746, 98799.14541223925, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98470.18588619301, 98342.3113787073, 96436.45263105791, 94022.3648840354, 93514.13588466223, 96245.86675629299, 92942.37826036742, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93836.71795962777, 93714.86052648521, 92170.7626977852, 90448.49973500443, 91814.43242962367, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89915.76548323483, 89798.99984373804, 92242.51004356766, 97618.2324831928, 92242.51004356766, 91020.75494365285, 89676.82433374657, 88821.5957638062, 88882.68351880195, 87844.19168387436, 88088.54270385733, 85034.15495407031, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85838.7614514851, 85727.29025596527, 88398.67890416818, 88580.81903927292, 89430.80633642837, 88823.6725527459, 89309.37957969189, 89552.23309316486, 87852.25849885395, 87184.41133680321, 86152.28390454302, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 86040.31178115228, 85928.57885007355, 85614.0525731699, 86557.63140388082, 85362.43155164701, 86368.91563773864, 86557.63140388082, 86494.72614850009, 84167.23169941318, 82217.16878261064, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82989.8400922282, 82882.0685385076, 83719.91215514882, 83977.71019103841, 84751.10429870723, 84106.60920898322, 85008.90233459683, 84944.45282562444, 84944.45282562444, 85395.59938843123, 85073.35184356922, 85137.80135254165, 85911.19546021045, 85202.25086151404, 84557.75577179002, 85073.35184356922, 84171.05871795563, 84493.30626281763, 85846.74595123804, 85588.94791534844, 85137.80135254165, 85846.74595123804, 87973.57974732728, 84042.15970001082, 83913.26068206601, 84042.15970001082, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457, 82388.15225035457]}, "drawdown": {"dates": ["2023-01-03", "2023-01-04", "2023-01-05", "2023-01-06", "2023-01-09", "2023-01-10", "2023-01-11", "2023-01-12", "2023-01-13", "2023-01-16", "2023-01-17", "2023-01-18", "2023-01-19", "2023-01-20", "2023-01-30", "2023-01-31", "2023-02-01", "2023-02-02", "2023-02-03", "2023-02-06", "2023-02-07", "2023-02-08", "2023-02-09", "2023-02-10", "2023-02-13", "2023-02-14", "2023-02-15", "2023-02-16", "2023-02-17", "2023-02-20", "2023-02-21", "2023-02-22", "2023-02-23", "2023-02-24", "2023-02-27", "2023-02-28", "2023-03-01", "2023-03-02", "2023-03-03", "2023-03-06", "2023-03-07", "2023-03-08", "2023-03-09", "2023-03-10", "2023-03-13", "2023-03-14", "2023-03-15", "2023-03-16", "2023-03-17", "2023-03-20", "2023-03-21", "2023-03-22", "2023-03-23", "2023-03-24", "2023-03-27", "2023-03-28", "2023-03-29", "2023-03-30", "2023-03-31", "2023-04-03", "2023-04-04", "2023-04-06", "2023-04-07", "2023-04-10", "2023-04-11", "2023-04-12", "2023-04-13", "2023-04-14", "2023-04-17", "2023-04-18", "2023-04-19", "2023-04-20", "2023-04-21", "2023-04-24", "2023-04-25", "2023-04-26", "2023-04-27", "2023-04-28", "2023-05-04", "2023-05-05", "2023-05-08", "2023-05-09", "2023-05-10", "2023-05-11", "2023-05-12", "2023-05-15", "2023-05-16", "2023-05-17", "2023-05-18", "2023-05-19", "2023-05-22", "2023-05-23", "2023-05-24", "2023-05-25", "2023-05-26", "2023-05-29", "2023-05-30", "2023-05-31", "2023-06-01", "2023-06-02", "2023-06-05", "2023-06-06", "2023-06-07", "2023-06-08", "2023-06-09", "2023-06-12", "2023-06-13", "2023-06-14", "2023-06-15", "2023-06-16", "2023-06-19", "2023-06-20", "2023-06-21", "2023-06-26", "2023-06-27", "2023-06-28", "2023-06-29", "2023-06-30", "2023-07-03", "2023-07-04", "2023-07-05", "2023-07-06", "2023-07-07", "2023-07-10", "2023-07-11", "2023-07-12", "2023-07-13", "2023-07-14", "2023-07-17", "2023-07-18", "2023-07-19", "2023-07-20", "2023-07-21", "2023-07-24", "2023-07-25", "2023-07-26", "2023-07-27", "2023-07-28", "2023-07-31", "2023-08-01", "2023-08-02", "2023-08-03", "2023-08-04", "2023-08-07", "2023-08-08", "2023-08-09", "2023-08-10", "2023-08-11", "2023-08-14", "2023-08-15", "2023-08-16", "2023-08-17", "2023-08-18", "2023-08-21", "2023-08-22", "2023-08-23", "2023-08-24", "2023-08-25", "2023-08-28", "2023-08-29", "2023-08-30", "2023-08-31", "2023-09-01", "2023-09-04", "2023-09-05", "2023-09-06", "2023-09-07", "2023-09-08", "2023-09-11", "2023-09-12", "2023-09-13", "2023-09-14", "2023-09-15", "2023-09-18", "2023-09-19", "2023-09-20", "2023-09-21", "2023-09-22", "2023-09-25", "2023-09-26", "2023-09-27", "2023-09-28", "2023-10-09", "2023-10-10", "2023-10-11", "2023-10-12", "2023-10-13", "2023-10-16", "2023-10-17", "2023-10-18", "2023-10-19", "2023-10-20", "2023-10-23", "2023-10-24", "2023-10-25", "2023-10-26", "2023-10-27", "2023-10-30", "2023-10-31", "2023-11-01", "2023-11-02", "2023-11-03", "2023-11-06", "2023-11-07", "2023-11-08", "2023-11-09", "2023-11-10", "2023-11-13", "2023-11-14", "2023-11-15", "2023-11-16", "2023-11-17", "2023-11-20", "2023-11-21", "2023-11-22", "2023-11-23", "2023-11-24", "2023-11-27", "2023-11-28", "2023-11-29", "2023-11-30", "2023-12-01", "2023-12-04", "2023-12-05", "2023-12-06", "2023-12-07", "2023-12-08", "2023-12-11", "2023-12-12", "2023-12-13", "2023-12-14", "2023-12-15", "2023-12-18", "2023-12-19", "2023-12-20", "2023-12-21", "2023-12-22", "2023-12-25", "2023-12-26", "2023-12-27", "2023-12-28", "2023-12-29"], "values": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0012986114155763007, 0.0, 0.0, 0.0, -0.0013183915622939635, 0.0, 0.0, -0.0019633507853403787, -0.03795811518324621, -0.04123036649214662, -0.034031413612565675, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03724768200261808, -0.03849792315314182, -0.05713168433234461, -0.08073444849266787, -0.08570345147378866, -0.058995060450264813, -0.09129357982754938, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08254953602595527, -0.08374094767169749, -0.09883773814098518, -0.11567646597211367, -0.10232161286466679, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12088505912294367, -0.12202668782076997, -0.09813625755738953, -0.04557731097795259, -0.09813625755738953, -0.11008147268907986, -0.1232212093339391, -0.1315828599261223, -0.13098559916953767, -0.14113903203147415, -0.13874998900513613, -0.16861302683436175, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16074631303169729, -0.16183617745015888, -0.1357177580505886, -0.13393695672789074, -0.12562655055530014, -0.13156255496429337, -0.12681375143709872, -0.12443934967350156, -0.14106016201868243, -0.14758976686857506, -0.1576809743638634, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.15877573640148268, -0.1598681598332513, -0.1629433129817388, -0.15371785353627665, -0.1654034355005286, -0.15556294542536886, -0.15371785353627654, -0.15433288416597402, -0.17708901746478034, -0.19615496698540213, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18860048653337846, -0.18965417920415906, -0.18146250294417, -0.1789419871718657, -0.17138043985495266, -0.17768172928571346, -0.1688599240826485, -0.1694900530257244, -0.1694900530257244, -0.16507915042419186, -0.16822979513957237, -0.16759966619649613, -0.1600381188795832, -0.16696953725342023, -0.17327082668418103, -0.16822979513957237, -0.17705160034263734, -0.17390095562725694, -0.16066824782265932, -0.16318876359496348, -0.16759966619649602, -0.16066824782265932, -0.13987399270114842, -0.17831185822878948, -0.17957211611494173, -0.17831185822878948, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799, -0.1944832454543799]}, "returns_distribution": {"returns": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0012986114155761606, 0.008713136729222604, 0.002657807308970069, 0.005301524188204048, -0.0013183915622939147, 0.003300330033003155, 0.005263157894736925, -0.0019633507853402564, -0.036065573770491924, -0.0034013605442175378, 0.007508532423208068, -0.0033295786585365132, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0012986114155761352, -0.0193798449612405, -0.025032938076416125, -0.005405405405405448, 0.02921195652173915, -0.034323432343234365, 0.009622517908407313, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0012986114155760472, -0.016476552598225534, -0.018685567010309236, 0.01510177281680899, -0.020679395342820196, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.001298611415576104, 0.0272108843537416, 0.0582781456953643, -0.05506883604505637, -0.01324503311258283, -0.014765100671140915, -0.00953678474114453, 0.0006877579092160815, -0.011683848797250861, 0.002781641168289301, -0.03467406380027744, 0.009462156681034607, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.001298611415576296, 0.03116147308781894, 0.0020604395604395236, 0.009595613433858747, -0.006788866259334625, 0.005468215994531796, 0.00271923861318815, -0.018983050847457508, -0.007601935038009816, -0.011838440111420516, -0.0012996999999999822, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0012986114155761072, -0.003660322108345677, 0.011021307861866366, -0.01380813953488362, 0.011790714812085396, 0.002184996358339387, -0.0007267441860465064, -0.026909090909090883, -0.023168908819133038, 0.009397931369548574, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0012986114155760075, 0.010108864696734079, 0.0030792917628943398, 0.009209516500383843, -0.007604562737642535, 0.010727969348658932, -0.0007581501137224265, 0.0, 0.005311077389984704, -0.0037735849056604377, 0.0007575757575760092, 0.009084027252081696, -0.008252063015753968, -0.007564296520423721, 0.006097560975609716, -0.010606060606060366, 0.003828483920367422, 0.016018306636155586, -0.003003003003002983, -0.0052710843373492775, 0.008327024981074802, 0.024774774774774865, -0.0446886446886448, -0.0015337423312883334, 0.0015360983102918485, -0.01968068711656433, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "bins": 50}, "monthly_returns": {"dates": ["2023-01", "2023-02", "2023-03", "2023-04", "2023-05", "2023-06", "2023-07", "2023-08", "2023-09", "2023-10", "2023-11", "2023-12"], "returns": [0.0, -0.015298141138070709, -0.047054526046293565, -0.04178484245452707, -0.012168830611053116, 2.3381576539582838e-05, -0.031335799248120244, 0.0, -0.0354539822761577, 0.0, 0.025882219533456263, -0.032296454201362734]}}, "summary": {"performance": "策略产生了 17.61% 的总亏损", "risk": "中等风险", "frequency": "低频交易", "win_rate_level": "低胜率"}}