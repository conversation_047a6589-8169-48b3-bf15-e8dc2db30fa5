"""
市场数据结构模块

定义量化交易系统中使用的市场数据结构。
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np


@dataclass
class OHLCV:
    """K线数据基础结构
    
    优化特性：
    - 使用Decimal提高精度
    - 添加数据验证
    - 支持扩展字段
    - 提供便捷计算方法
    """
    open: float
    high: float
    low: float
    close: float
    volume: float
    timestamp: datetime
    
    # 扩展字段
    amount: Optional[float] = None  # 成交额
    vwap: Optional[float] = None    # 成交量加权平均价
    adj_close: Optional[float] = None  # 复权收盘价
    
    # 技术指标缓存
    indicators: Dict[str, Any] = field(default_factory=dict)
    
    # 元数据
    symbol: Optional[str] = None
    exchange: Optional[str] = None
    frequency: Optional[str] = None  # 1m, 5m, 1h, 1d等
    
    def __post_init__(self):
        """数据验证和初始化"""
        self._validate_data()
        self._calculate_derived_fields()
    
    def _validate_data(self):
        """数据验证"""
        if self.high < max(self.open, self.close):
            raise ValueError(f"最高价({self.high})不能小于开盘价({self.open})或收盘价({self.close})")
        
        if self.low > min(self.open, self.close):
            raise ValueError(f"最低价({self.low})不能大于开盘价({self.open})或收盘价({self.close})")
        
        if self.volume < 0:
            raise ValueError(f"成交量({self.volume})不能为负数")
        
        if any(price <= 0 for price in [self.open, self.high, self.low, self.close]):
            raise ValueError("价格不能小于等于0")
    
    def _calculate_derived_fields(self):
        """计算衍生字段"""
        # 计算VWAP（如果没有提供）
        if self.vwap is None and self.amount is not None and self.volume > 0:
            self.vwap = self.amount / self.volume
        
        # 如果没有复权价格，使用收盘价
        if self.adj_close is None:
            self.adj_close = self.close
    
    @property
    def is_up(self) -> bool:
        """是否上涨"""
        return self.close > self.open
    
    @property
    def is_down(self) -> bool:
        """是否下跌"""
        return self.close < self.open
    
    @property
    def is_doji(self) -> bool:
        """是否十字星（开盘价等于收盘价）"""
        return abs(self.close - self.open) < 1e-6
    
    @property
    def change(self) -> float:
        """价格变化"""
        return self.close - self.open
    
    @property
    def change_ratio(self) -> float:
        """涨跌幅"""
        if self.open == 0:
            return 0.0
        return (self.close - self.open) / self.open
    
    @property
    def amplitude(self) -> float:
        """振幅"""
        if self.open == 0:
            return 0.0
        return (self.high - self.low) / self.open
    
    @property
    def upper_shadow(self) -> float:
        """上影线长度"""
        return self.high - max(self.open, self.close)
    
    @property
    def lower_shadow(self) -> float:
        """下影线长度"""
        return min(self.open, self.close) - self.low
    
    @property
    def body_size(self) -> float:
        """实体大小"""
        return abs(self.close - self.open)
    
    @property
    def typical_price(self) -> float:
        """典型价格 (H+L+C)/3"""
        return (self.high + self.low + self.close) / 3
    
    @property
    def weighted_price(self) -> float:
        """加权价格 (H+L+C+C)/4"""
        return (self.high + self.low + 2 * self.close) / 4
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp,
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'amount': self.amount,
            'vwap': self.vwap,
            'adj_close': self.adj_close,
            'symbol': self.symbol,
            'exchange': self.exchange,
            'frequency': self.frequency,
            'change': self.change,
            'change_ratio': self.change_ratio,
            'amplitude': self.amplitude,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OHLCV':
        """从字典创建OHLCV对象"""
        return cls(
            open=data['open'],
            high=data['high'],
            low=data['low'],
            close=data['close'],
            volume=data['volume'],
            timestamp=data['timestamp'],
            amount=data.get('amount'),
            vwap=data.get('vwap'),
            adj_close=data.get('adj_close'),
            symbol=data.get('symbol'),
            exchange=data.get('exchange'),
            frequency=data.get('frequency'),
        )
    
    @classmethod
    def from_series(cls, series: pd.Series, symbol: str = None) -> 'OHLCV':
        """从pandas Series创建OHLCV对象"""
        return cls(
            open=series['open'],
            high=series['high'],
            low=series['low'],
            close=series['close'],
            volume=series['volume'],
            timestamp=series.name if hasattr(series, 'name') else datetime.now(),
            amount=series.get('amount'),
            vwap=series.get('vwap'),
            adj_close=series.get('adj_close'),
            symbol=symbol,
        )


@dataclass
class MarketData:
    """市场数据容器
    
    用于存储和管理多个标的的市场数据
    """
    data: Dict[str, pd.DataFrame] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_symbol_data(self, symbol: str, df: pd.DataFrame):
        """添加标的数据"""
        self.data[symbol] = df
    
    def get_symbol_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取标的数据"""
        return self.data.get(symbol)
    
    def get_symbols(self) -> List[str]:
        """获取所有标的列表"""
        return list(self.data.keys())
    
    def get_date_range(self, symbol: str = None) -> tuple:
        """获取日期范围"""
        if symbol:
            df = self.get_symbol_data(symbol)
            if df is not None and not df.empty:
                return df.index.min(), df.index.max()
        else:
            # 获取所有标的的日期范围
            all_dates = []
            for df in self.data.values():
                if not df.empty:
                    all_dates.extend([df.index.min(), df.index.max()])
            if all_dates:
                return min(all_dates), max(all_dates)
        return None, None
    
    def is_empty(self) -> bool:
        """是否为空"""
        return len(self.data) == 0
    
    def size(self) -> int:
        """数据大小"""
        return sum(len(df) for df in self.data.values())


@dataclass
class Tick:
    """Tick数据结构"""
    symbol: str
    timestamp: datetime
    price: float
    volume: float
    
    # 买卖盘信息
    bid_price: Optional[float] = None
    ask_price: Optional[float] = None
    bid_volume: Optional[float] = None
    ask_volume: Optional[float] = None
    
    # 元数据
    exchange: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def spread(self) -> Optional[float]:
        """买卖价差"""
        if self.bid_price is not None and self.ask_price is not None:
            return self.ask_price - self.bid_price
        return None
    
    @property
    def mid_price(self) -> Optional[float]:
        """中间价"""
        if self.bid_price is not None and self.ask_price is not None:
            return (self.bid_price + self.ask_price) / 2
        return None


@dataclass
class Quote:
    """报价数据结构"""
    symbol: str
    timestamp: datetime
    
    # 五档买卖盘
    bid_prices: List[float] = field(default_factory=list)
    bid_volumes: List[float] = field(default_factory=list)
    ask_prices: List[float] = field(default_factory=list)
    ask_volumes: List[float] = field(default_factory=list)
    
    # 元数据
    exchange: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def best_bid(self) -> Optional[float]:
        """最优买价"""
        return self.bid_prices[0] if self.bid_prices else None
    
    @property
    def best_ask(self) -> Optional[float]:
        """最优卖价"""
        return self.ask_prices[0] if self.ask_prices else None
    
    @property
    def spread(self) -> Optional[float]:
        """买卖价差"""
        if self.best_bid is not None and self.best_ask is not None:
            return self.best_ask - self.best_bid
        return None
