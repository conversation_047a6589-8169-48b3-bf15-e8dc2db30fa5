"""
Quant_01 - 高性能量化回测引擎

基于quant_new优化的下一代量化回测系统，专注于高性能、可扩展性和易管理性。

主要特性：
- 模块化设计，高度解耦
- 插件化系统，支持动态加载
- 高性能向量化计算
- 智能缓存和内存优化
- 完整的风险管理
- 丰富的可视化报告
"""

__version__ = "2.0.0"
__author__ = "Quant Team"
__email__ = "<EMAIL>"

# 导入核心模块
from .core.engine.base import QuantEngine
from .core.config.global_config import GlobalConfig
from .core.data_structures.market import OHLCV
from .core.data_structures.trading import Order, Position, Trade
from .core.data_structures.portfolio import Portfolio

# 导入数据源
from .dataseed.base import DataSource
from .dataseed.mock_source import MockDataSource
from .dataseed.akshare_source import AkShareDataSource

# 导入策略基类
from .strategies.base.strategy import BaseStrategy

# 导入常用策略
from .strategies.single.macd import MACDStrategy
from .strategies.single.rsi import RSIStrategy

# 导入技术指标
from .indicators.trend.moving_average import SMA, EMA
from .indicators.momentum.rsi import RSI
from .indicators.trend.macd import MACD

# 导入风险管理
from .risk.manager import RiskManager

# 导入优化器
from .optimizer import (
    GridSearchOptimizer,
    RandomSearchOptimizer,
    BayesianOptimizer,
    GeneticOptimizer,
    MultiObjectiveOptimizer,
    OptimizationManager,
    optimization_manager
)

# 导入报告生成器
from .reports.generators.html_report import HTMLReportGenerator

__all__ = [
    # 核心引擎
    "QuantEngine",
    "GlobalConfig",

    # 数据结构
    "OHLCV",
    "Order",
    "Position",
    "Trade",
    "Portfolio",

    # 数据源
    "DataSource",
    "MockDataSource",
    "AkShareDataSource",

    # 策略
    "BaseStrategy",
    "MACDStrategy",
    "RSIStrategy",

    # 技术指标
    "SMA",
    "EMA",
    "RSI",
    "MACD",

    # 风险管理
    "RiskManager",

    # 优化器
    "GridSearchOptimizer",
    "RandomSearchOptimizer",
    "BayesianOptimizer",
    "GeneticOptimizer",
    "MultiObjectiveOptimizer",
    "OptimizationManager",
    "optimization_manager",

    # 报告生成
    "HTMLReportGenerator",
]

# 版本信息
def get_version():
    """获取版本信息"""
    return __version__

def get_info():
    """获取项目信息"""
    return {
        "name": "Quant_01",
        "version": __version__,
        "author": __author__,
        "email": __email__,
        "description": "高性能量化回测引擎"
    }
