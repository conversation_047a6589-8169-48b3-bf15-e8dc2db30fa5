"""
异常定义模块

定义量化交易系统中使用的自定义异常。
"""


class QuantBaseException(Exception):
    """量化系统基础异常"""
    pass


class DataSourceException(QuantBaseException):
    """数据源异常"""
    pass


class DataNotAvailableException(DataSourceException):
    """数据不可用异常"""
    pass


class DataValidationException(DataSourceException):
    """数据验证异常"""
    pass


class StrategyException(QuantBaseException):
    """策略异常"""
    pass


class StrategyNotRegisteredException(StrategyException):
    """策略未注册异常"""
    pass


class StrategyConfigException(StrategyException):
    """策略配置异常"""
    pass


class EngineException(QuantBaseException):
    """引擎异常"""
    pass


class BacktestException(EngineException):
    """回测异常"""
    pass


class OptimizationException(EngineException):
    """优化异常"""
    pass


class RiskManagementException(QuantBaseException):
    """风险管理异常"""
    pass


class PositionLimitException(RiskManagementException):
    """持仓限制异常"""
    pass


class DrawdownLimitException(RiskManagementException):
    """回撤限制异常"""
    pass


class OrderException(QuantBaseException):
    """订单异常"""
    pass


class InsufficientFundsException(OrderException):
    """资金不足异常"""
    pass


class InvalidOrderException(OrderException):
    """无效订单异常"""
    pass


class IndicatorException(QuantBaseException):
    """技术指标异常"""
    pass


class IndicatorCalculationException(IndicatorException):
    """指标计算异常"""
    pass


class ConfigException(QuantBaseException):
    """配置异常"""
    pass


class InvalidConfigException(ConfigException):
    """无效配置异常"""
    pass


class CacheException(QuantBaseException):
    """缓存异常"""
    pass


class ReportException(QuantBaseException):
    """报告异常"""
    pass


class ReportGenerationException(ReportException):
    """报告生成异常"""
    pass


__all__ = [
    # 基础异常
    "QuantBaseException",
    
    # 数据源异常
    "DataSourceException",
    "DataNotAvailableException", 
    "DataValidationException",
    
    # 策略异常
    "StrategyException",
    "StrategyNotRegisteredException",
    "StrategyConfigException",
    
    # 引擎异常
    "EngineException",
    "BacktestException",
    "OptimizationException",
    
    # 风险管理异常
    "RiskManagementException",
    "PositionLimitException",
    "DrawdownLimitException",
    
    # 订单异常
    "OrderException",
    "InsufficientFundsException",
    "InvalidOrderException",
    
    # 技术指标异常
    "IndicatorException",
    "IndicatorCalculationException",
    
    # 配置异常
    "ConfigException",
    "InvalidConfigException",
    
    # 缓存异常
    "CacheException",
    
    # 报告异常
    "ReportException",
    "ReportGenerationException",
]
