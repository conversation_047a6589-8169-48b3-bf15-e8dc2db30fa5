"""
接口定义模块

定义量化交易系统中使用的核心接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import pandas as pd

from ..structures import OHLCV, Order, Position, Trade, Portfolio, StrategyResult


class IDataSource(ABC):
    """数据源接口"""
    
    @abstractmethod
    def get_data(self, symbol: str, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """获取数据"""
        pass
    
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        """获取可用标的列表"""
        pass
    
    @abstractmethod
    def is_available(self, symbol: str) -> bool:
        """检查标的是否可用"""
        pass


class IStrategy(ABC):
    """策略接口"""
    
    @abstractmethod
    def initialize(self, **kwargs):
        """初始化策略"""
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取策略名称"""
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数"""
        pass


class IIndicator(ABC):
    """技术指标接口"""
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **kwargs) -> Union[pd.Series, pd.DataFrame]:
        """计算指标"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取指标名称"""
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """获取指标参数"""
        pass


class IEngine(ABC):
    """回测引擎接口"""
    
    @abstractmethod
    def run_backtest(self, strategy: IStrategy, data: pd.DataFrame, **kwargs) -> StrategyResult:
        """运行回测"""
        pass
    
    @abstractmethod
    def set_initial_capital(self, capital: float):
        """设置初始资金"""
        pass
    
    @abstractmethod
    def set_commission(self, commission: float):
        """设置手续费"""
        pass


class IRiskManager(ABC):
    """风险管理接口"""
    
    @abstractmethod
    def check_order(self, order: Order, portfolio: Portfolio) -> bool:
        """检查订单是否符合风险要求"""
        pass
    
    @abstractmethod
    def check_position(self, position: Position, current_price: float) -> bool:
        """检查持仓是否符合风险要求"""
        pass
    
    @abstractmethod
    def get_position_size(self, symbol: str, signal_strength: float, portfolio: Portfolio) -> float:
        """计算仓位大小"""
        pass


class IOptimizer(ABC):
    """优化器接口"""
    
    @abstractmethod
    def optimize(self, strategy_class, param_space: Dict[str, Any], data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """执行优化"""
        pass
    
    @abstractmethod
    def get_best_params(self) -> Dict[str, Any]:
        """获取最优参数"""
        pass
    
    @abstractmethod
    def get_optimization_results(self) -> pd.DataFrame:
        """获取优化结果"""
        pass


class IReportGenerator(ABC):
    """报告生成器接口"""
    
    @abstractmethod
    def generate_report(self, result: StrategyResult, **kwargs) -> str:
        """生成报告"""
        pass
    
    @abstractmethod
    def save_report(self, report: str, filepath: str):
        """保存报告"""
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式"""
        pass


class ICacheManager(ABC):
    """缓存管理器接口"""
    
    @abstractmethod
    def get(self, key: str) -> Any:
        """获取缓存"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存"""
        pass
    
    @abstractmethod
    def delete(self, key: str):
        """删除缓存"""
        pass
    
    @abstractmethod
    def clear(self):
        """清空缓存"""
        pass


class ILogger(ABC):
    """日志接口"""
    
    @abstractmethod
    def debug(self, message: str, **kwargs):
        """调试日志"""
        pass
    
    @abstractmethod
    def info(self, message: str, **kwargs):
        """信息日志"""
        pass
    
    @abstractmethod
    def warning(self, message: str, **kwargs):
        """警告日志"""
        pass
    
    @abstractmethod
    def error(self, message: str, **kwargs):
        """错误日志"""
        pass


class IConfigManager(ABC):
    """配置管理器接口"""
    
    @abstractmethod
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any):
        """设置配置"""
        pass
    
    @abstractmethod
    def load_from_file(self, filepath: str):
        """从文件加载配置"""
        pass
    
    @abstractmethod
    def save_to_file(self, filepath: str):
        """保存配置到文件"""
        pass


__all__ = [
    "IDataSource",
    "IStrategy", 
    "IIndicator",
    "IEngine",
    "IRiskManager",
    "IOptimizer",
    "IReportGenerator",
    "ICacheManager",
    "ILogger",
    "IConfigManager",
]
