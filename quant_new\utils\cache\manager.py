"""
缓存管理器

提供统一的缓存管理接口，支持内存缓存和磁盘缓存。
"""

import hashlib
import pickle
import time
from pathlib import Path
from typing import Any, Optional, Dict, Callable, Union
from functools import lru_cache
import threading

try:
    import diskcache as dc
    DISKCACHE_AVAILABLE = True
except ImportError:
    DISKCACHE_AVAILABLE = False
    dc = None

from ...core.config import config
from ..logger import get_logger

logger = get_logger(__name__)


class CacheManager:
    """缓存管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._disk_cache = None
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        
        # 初始化磁盘缓存
        if DISKCACHE_AVAILABLE and config.cache.enabled:
            try:
                cache_dir = config.get_cache_dir()
                self._disk_cache = dc.Cache(
                    directory=str(cache_dir),
                    size_limit=config.cache.disk_cache_size * 1024 * 1024  # MB to bytes
                )
                logger.info(f"磁盘缓存初始化成功: {cache_dir}")
            except Exception as e:
                logger.warning(f"磁盘缓存初始化失败: {e}")
                self._disk_cache = None
        
        logger.info("缓存管理器初始化完成")
    
    def _generate_key(self, func: Callable, args: tuple, kwargs: dict, prefix: str = "") -> str:
        """生成缓存键"""
        # 创建唯一标识
        func_name = f"{func.__module__}.{func.__name__}"
        
        # 序列化参数
        try:
            args_str = str(args)
            kwargs_str = str(sorted(kwargs.items()))
            key_data = f"{func_name}:{args_str}:{kwargs_str}"
        except Exception:
            # 如果参数无法序列化，使用hash
            key_data = f"{func_name}:{hash(args)}:{hash(tuple(sorted(kwargs.items())))}"
        
        # 生成MD5哈希
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        
        if prefix:
            return f"{prefix}:{key_hash}"
        return key_hash
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        # 先检查内存缓存
        if key in self._memory_cache:
            cache_item = self._memory_cache[key]
            if self._is_expired(cache_item):
                del self._memory_cache[key]
            else:
                self._cache_stats['hits'] += 1
                return cache_item['value']
        
        # 检查磁盘缓存
        if self._disk_cache:
            try:
                value = self._disk_cache.get(key, default)
                if value != default:
                    self._cache_stats['hits'] += 1
                    return value
            except Exception as e:
                logger.warning(f"磁盘缓存读取失败: {e}")
        
        self._cache_stats['misses'] += 1
        return default
    
    def set(
        self, 
        key: str, 
        value: Any, 
        ttl: int = None, 
        memory_only: bool = False
    ) -> bool:
        """设置缓存值"""
        try:
            if ttl is None:
                ttl = config.cache.default_ttl
            
            expire_time = time.time() + ttl
            
            # 设置内存缓存
            self._memory_cache[key] = {
                'value': value,
                'expire_time': expire_time,
                'created_time': time.time()
            }
            
            # 设置磁盘缓存
            if not memory_only and self._disk_cache:
                try:
                    self._disk_cache.set(key, value, expire=ttl)
                except Exception as e:
                    logger.warning(f"磁盘缓存写入失败: {e}")
            
            self._cache_stats['sets'] += 1
            return True
            
        except Exception as e:
            logger.error(f"缓存设置失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            # 删除内存缓存
            if key in self._memory_cache:
                del self._memory_cache[key]
            
            # 删除磁盘缓存
            if self._disk_cache:
                try:
                    self._disk_cache.delete(key)
                except Exception as e:
                    logger.warning(f"磁盘缓存删除失败: {e}")
            
            self._cache_stats['deletes'] += 1
            return True
            
        except Exception as e:
            logger.error(f"缓存删除失败: {e}")
            return False
    
    def clear(self, pattern: Optional[str] = None) -> bool:
        """清空缓存"""
        try:
            if pattern:
                # 按模式删除
                keys_to_delete = [k for k in self._memory_cache.keys() if pattern in k]
                for key in keys_to_delete:
                    del self._memory_cache[key]
                
                if self._disk_cache:
                    # 磁盘缓存不支持模式删除，需要遍历
                    pass
            else:
                # 清空所有缓存
                self._memory_cache.clear()
                
                if self._disk_cache:
                    self._disk_cache.clear()
            
            logger.info(f"缓存清理完成: {pattern or 'all'}")
            return True
            
        except Exception as e:
            logger.error(f"缓存清理失败: {e}")
            return False
    
    def _is_expired(self, cache_item: Dict[str, Any]) -> bool:
        """检查缓存是否过期"""
        return time.time() > cache_item['expire_time']
    
    def cleanup_expired(self):
        """清理过期缓存"""
        expired_keys = []
        for key, item in self._memory_cache.items():
            if self._is_expired(item):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._memory_cache[key]
        
        if expired_keys:
            logger.debug(f"清理过期缓存: {len(expired_keys)} 个")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = self._cache_stats.copy()
        stats['memory_cache_size'] = len(self._memory_cache)
        
        if self._disk_cache:
            try:
                stats['disk_cache_size'] = len(self._disk_cache)
                stats['disk_cache_volume'] = self._disk_cache.volume()
            except Exception:
                stats['disk_cache_size'] = 0
                stats['disk_cache_volume'] = 0
        else:
            stats['disk_cache_size'] = 0
            stats['disk_cache_volume'] = 0
        
        # 计算命中率
        total_requests = stats['hits'] + stats['misses']
        stats['hit_rate'] = stats['hits'] / total_requests if total_requests > 0 else 0
        
        return stats
    
    def get_or_set(
        self,
        func: Callable,
        args: tuple,
        kwargs: dict,
        key: Optional[str] = None,
        ttl: int = None,
        version: int = 1
    ) -> Any:
        """获取或设置缓存"""
        # 生成缓存键
        if key is None:
            key = self._generate_key(func, args, kwargs, f"v{version}")
        
        # 尝试获取缓存
        cached_value = self.get(key)
        if cached_value is not None:
            return cached_value
        
        # 执行函数并缓存结果
        try:
            result = func(*args, **kwargs)
            self.set(key, result, ttl)
            return result
        except Exception as e:
            logger.error(f"函数执行失败: {func.__name__}, {e}")
            raise


# 全局缓存管理器实例
cache_manager = CacheManager()


# 便捷函数
def get_cache(key: str, default: Any = None) -> Any:
    """获取缓存"""
    return cache_manager.get(key, default)


def set_cache(key: str, value: Any, ttl: int = None) -> bool:
    """设置缓存"""
    return cache_manager.set(key, value, ttl)


def delete_cache(key: str) -> bool:
    """删除缓存"""
    return cache_manager.delete(key)


def clear_cache(pattern: Optional[str] = None) -> bool:
    """清空缓存"""
    return cache_manager.clear(pattern)


def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计"""
    return cache_manager.get_stats()


__all__ = [
    "CacheManager",
    "cache_manager",
    "get_cache",
    "set_cache", 
    "delete_cache",
    "clear_cache",
    "get_cache_stats"
]
