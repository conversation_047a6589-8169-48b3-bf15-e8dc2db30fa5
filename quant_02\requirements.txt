# Quant_02 依赖文件
# 下一代高性能量化回测引擎

# ============================================================================
# 核心依赖
# ============================================================================
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.0.0
loguru>=0.7.0

# ============================================================================
# 回测引擎
# ============================================================================
vectorbt>=0.25.0

# ============================================================================
# 数据源
# ============================================================================
akshare>=1.11.0
sqlalchemy>=2.0.0
yfinance>=0.2.0

# ============================================================================
# 性能优化
# ============================================================================
numba>=0.57.0
redis>=4.5.0
joblib>=1.3.0

# ============================================================================
# 缓存系统
# ============================================================================
diskcache>=5.6.0
cachetools>=5.3.0

# ============================================================================
# 可视化和报告
# ============================================================================
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0
jinja2>=3.1.0

# ============================================================================
# 异步支持
# ============================================================================
aiofiles>=23.0.0
asyncio-mqtt>=0.13.0
aiohttp>=3.8.0

# ============================================================================
# 配置管理
# ============================================================================
python-dotenv>=1.0.0
pyyaml>=6.0
toml>=0.10.0

# ============================================================================
# 优化算法
# ============================================================================
optuna>=3.3.0
hyperopt>=0.2.7
scikit-optimize>=0.9.0

# ============================================================================
# 测试框架
# ============================================================================
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# ============================================================================
# 代码质量
# ============================================================================
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
isort>=5.12.0

# ============================================================================
# 文档生成
# ============================================================================
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# ============================================================================
# 可选依赖 - Web界面
# ============================================================================
fastapi>=0.100.0
streamlit>=1.25.0
uvicorn>=0.23.0
dash>=2.14.0

# ============================================================================
# 可选依赖 - 机器学习
# ============================================================================
scikit-learn>=1.3.0
scipy>=1.11.0
xgboost>=1.7.0
lightgbm>=4.0.0

# ============================================================================
# 可选依赖 - 数据库
# ============================================================================
psycopg2-binary>=2.9.0
pymongo>=4.4.0
clickhouse-driver>=0.2.6

# ============================================================================
# 可选依赖 - 消息队列
# ============================================================================
celery>=5.3.0
rabbitmq>=0.2.0

# ============================================================================
# 开发工具
# ============================================================================
jupyter>=1.0.0
ipython>=8.14.0
pre-commit>=3.4.0

# ============================================================================
# 风险管理
# ============================================================================
empyrical>=0.5.5
pyfolio>=0.9.2

# ============================================================================
# 时间序列分析
# ============================================================================
statsmodels>=0.14.0
arch>=5.6.0
