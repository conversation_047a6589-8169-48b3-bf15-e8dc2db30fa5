"""
策略模块

提供量化交易策略的开发框架和常用策略实现。
"""

from .base.strategy import BaseStrategy, StrategyConfig, StrategyResult
from .single.macd import MACDStrategy, MACDConfig
from .single.rsi import RSIStrategy, RSIConfig
from .factory import StrategyFactory
from .registry import StrategyRegistry

__all__ = [
    # 基础类
    "BaseStrategy",
    "StrategyConfig", 
    "StrategyResult",
    
    # 具体策略
    "MACDStrategy",
    "MACDConfig",
    "RSIStrategy", 
    "RSIConfig",
    
    # 工厂和注册器
    "StrategyFactory",
    "StrategyRegistry",
]
