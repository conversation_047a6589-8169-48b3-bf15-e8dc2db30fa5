"""
多资产回测引擎模块

支持多个标的同时回测，提供投资组合级别的回测功能。
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import pandas as pd
import numpy as np
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from .base import QuantEngine
from ..config.global_config import GlobalConfig
from ..config.backtest import BacktestConfig
from ..structures.portfolio import Portfolio, StrategyResult
from ..structures.trade import Trade, Order, OrderSide, OrderType
from ..interfaces import IDataSource, IStrategy
from ..exceptions import EngineException, BacktestException
from risk import RiskManager
from utils.logger import get_logger

logger = get_logger(__name__)


class MultiAssetEngine(QuantEngine):
    """多资产回测引擎
    
    支持多个标的同时回测：
    - 投资组合级别的风险管理
    - 资产间相关性分析
    - 动态权重调整
    - 并行数据处理
    - 复杂策略支持
    """
    
    def __init__(
        self,
        data_source: Optional[IDataSource] = None,
        config: Optional[GlobalConfig] = None,
        backtest_config: Optional[BacktestConfig] = None,
        risk_manager: Optional[RiskManager] = None
    ):
        """
        初始化多资产回测引擎
        
        Args:
            data_source: 数据源
            config: 全局配置
            backtest_config: 回测配置
            risk_manager: 风险管理器
        """
        super().__init__(data_source, config)
        
        self.backtest_config = backtest_config or BacktestConfig.create_default()
        self.risk_manager = risk_manager
        
        # 多资产特定状态
        self._current_symbols = []
        self._current_data = {}
        self._current_signals = {}
        self._weights = {}
        
        # 并行处理配置
        self._max_workers = 4
        
        logger.info("多资产回测引擎初始化完成")
    
    def run_multi_backtest(
        self,
        strategy: Union[str, IStrategy],
        symbols: List[str],
        start_date: str,
        end_date: str,
        weights: Optional[Dict[str, float]] = None,
        initial_capital: Optional[float] = None,
        rebalance_frequency: str = 'monthly',
        **kwargs
    ) -> StrategyResult:
        """
        运行多资产回测
        
        Args:
            strategy: 策略名称或策略对象
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            weights: 资产权重字典
            initial_capital: 初始资金
            rebalance_frequency: 再平衡频率
            **kwargs: 其他参数
            
        Returns:
            回测结果
        """
        start_time = time.time()
        
        try:
            # 设置当前标的
            self._current_symbols = symbols
            
            # 获取策略对象
            strategy_obj = self._get_strategy_object(strategy)
            
            # 设置权重
            self._weights = self._setup_weights(symbols, weights)
            
            # 获取数据
            logger.info(f"获取多资产数据: {len(symbols)}个标的")
            data_dict = self._get_multi_asset_data(symbols, start_date, end_date)
            self._current_data = data_dict
            
            # 对齐数据
            aligned_data = self._align_data(data_dict)
            
            # 初始化投资组合
            capital = initial_capital or self.backtest_config.initial_capital
            portfolio = self._initialize_multi_portfolio(strategy_obj, symbols, capital)
            
            # 初始化策略
            strategy_obj.initialize()
            
            # 生成交易信号
            logger.info("生成多资产交易信号")
            signals_dict = self._generate_multi_signals(strategy_obj, data_dict)
            self._current_signals = signals_dict
            
            # 执行回测
            logger.info("执行多资产回测")
            self._execute_multi_backtest(
                portfolio, aligned_data, signals_dict, strategy_obj, rebalance_frequency
            )
            
            # 计算结果
            result = self._calculate_multi_result(
                portfolio, aligned_data, signals_dict, strategy_obj, symbols
            )
            
            # 更新统计信息
            execution_time = time.time() - start_time
            self._update_stats(execution_time)
            
            logger.info(f"多资产回测完成: {len(symbols)}个标的, 耗时: {execution_time:.2f}秒")
            logger.info(f"总收益率: {result.total_return:.2%}, 夏普比率: {result.sharpe_ratio:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"多资产回测失败: {e}")
            raise BacktestException(f"多资产回测执行失败: {e}")
    
    def _get_strategy_object(self, strategy: Union[str, IStrategy]) -> IStrategy:
        """获取策略对象"""
        if isinstance(strategy, str):
            strategy_obj = self.get_strategy(strategy)
            if strategy_obj is None:
                raise EngineException(f"策略未注册: {strategy}")
        else:
            strategy_obj = strategy
        
        return strategy_obj
    
    def _setup_weights(self, symbols: List[str], weights: Optional[Dict[str, float]]) -> Dict[str, float]:
        """设置资产权重"""
        if weights is None:
            # 等权重
            weight = 1.0 / len(symbols)
            return {symbol: weight for symbol in symbols}
        
        # 验证权重
        total_weight = sum(weights.values())
        if abs(total_weight - 1.0) > 0.01:
            logger.warning(f"权重总和({total_weight:.3f})不等于1，将进行归一化")
            return {symbol: w / total_weight for symbol, w in weights.items()}
        
        return weights.copy()
    
    def _get_multi_asset_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """获取多资产数据"""
        if self.data_source is None:
            raise EngineException("未设置数据源")
        
        data_dict = {}
        
        # 并行获取数据
        with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
            future_to_symbol = {
                executor.submit(self.data_source.get_data, symbol, start_date, end_date): symbol
                for symbol in symbols
            }
            
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    data = future.result()
                    self._validate_data(data)
                    data_dict[symbol] = data
                    logger.debug(f"数据获取完成: {symbol}")
                except Exception as e:
                    logger.error(f"数据获取失败: {symbol} - {e}")
                    raise
        
        return data_dict
    
    def _align_data(self, data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """对齐多资产数据"""
        # 获取所有日期的交集
        all_dates = None
        for symbol, data in data_dict.items():
            if all_dates is None:
                all_dates = set(data.index)
            else:
                all_dates = all_dates.intersection(set(data.index))
        
        if not all_dates:
            raise BacktestException("没有共同的交易日期")
        
        # 创建对齐的数据结构
        common_dates = sorted(all_dates)
        aligned_data = pd.DataFrame(index=common_dates)
        
        for symbol, data in data_dict.items():
            aligned_data[f'{symbol}_close'] = data.loc[common_dates, 'close']
            aligned_data[f'{symbol}_volume'] = data.loc[common_dates, 'volume']
        
        return aligned_data
    
    def _initialize_multi_portfolio(self, strategy: IStrategy, symbols: List[str], capital: float) -> Portfolio:
        """初始化多资产投资组合"""
        portfolio = Portfolio(
            portfolio_id=f"multi_{len(symbols)}assets_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            name=f"{strategy.get_name()}_multi",
            initial_capital=capital,
            timestamp=datetime.now()
        )
        
        return portfolio
    
    def _generate_multi_signals(
        self,
        strategy: IStrategy,
        data_dict: Dict[str, pd.DataFrame]
    ) -> Dict[str, pd.DataFrame]:
        """生成多资产交易信号"""
        signals_dict = {}
        
        # 并行生成信号
        with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
            future_to_symbol = {
                executor.submit(strategy.generate_signals, data): symbol
                for symbol, data in data_dict.items()
            }
            
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    signals = future.result()
                    signals_dict[symbol] = signals
                    logger.debug(f"信号生成完成: {symbol}")
                except Exception as e:
                    logger.error(f"信号生成失败: {symbol} - {e}")
                    # 创建空信号
                    data = data_dict[symbol]
                    empty_signals = pd.DataFrame(index=data.index)
                    empty_signals['signal'] = 0
                    empty_signals['strength'] = 0.0
                    signals_dict[symbol] = empty_signals
        
        return signals_dict
    
    def _execute_multi_backtest(
        self,
        portfolio: Portfolio,
        aligned_data: pd.DataFrame,
        signals_dict: Dict[str, pd.DataFrame],
        strategy: IStrategy,
        rebalance_frequency: str
    ):
        """执行多资产回测"""
        rebalance_dates = self._get_rebalance_dates(aligned_data.index, rebalance_frequency)
        
        for i, (timestamp, row) in enumerate(aligned_data.iterrows()):
            if i == 0:
                continue  # 跳过第一行
            
            # 更新所有持仓价值
            current_prices = {}
            for symbol in self._current_symbols:
                current_prices[symbol] = row[f'{symbol}_close']
            
            portfolio.update_positions_value(current_prices)
            
            # 处理交易信号
            for symbol in self._current_symbols:
                if symbol in signals_dict and timestamp in signals_dict[symbol].index:
                    signal_row = signals_dict[symbol].loc[timestamp]
                    
                    if signal_row.get('signal', 0) != 0:
                        self._process_multi_signal(
                            portfolio, symbol, signal_row, current_prices[symbol], timestamp, strategy
                        )
            
            # 再平衡检查
            if timestamp in rebalance_dates:
                self._rebalance_portfolio(portfolio, current_prices, timestamp)
            
            # 更新价值历史
            portfolio.update_value_history(timestamp, portfolio.total_value)
            
            # 风险检查
            if self.risk_manager:
                self.risk_manager.update_risk_metrics(portfolio, current_prices)
    
    def _process_multi_signal(
        self,
        portfolio: Portfolio,
        symbol: str,
        signal_row: pd.Series,
        current_price: float,
        timestamp: datetime,
        strategy: IStrategy
    ):
        """处理多资产交易信号"""
        signal = signal_row.get('signal', 0)
        strength = signal_row.get('strength', 1.0)
        
        # 考虑资产权重
        weight = self._weights.get(symbol, 0)
        adjusted_strength = strength * weight
        
        if signal == 1:  # 买入信号
            self._execute_multi_buy(portfolio, symbol, current_price, timestamp, adjusted_strength)
        elif signal == -1:  # 卖出信号
            self._execute_multi_sell(portfolio, symbol, current_price, timestamp, adjusted_strength)
    
    def _execute_multi_buy(
        self,
        portfolio: Portfolio,
        symbol: str,
        price: float,
        timestamp: datetime,
        strength: float
    ):
        """执行多资产买入"""
        # 计算仓位大小（考虑权重）
        target_weight = self._weights.get(symbol, 0)
        target_value = portfolio.total_value * target_weight * strength
        
        current_position = portfolio.get_position(symbol)
        current_value = current_position.market_value if current_position else 0
        
        additional_value = target_value - current_value
        
        if additional_value < self.backtest_config.min_trade_amount:
            return
        
        # 计算股数
        shares = int(additional_value / price)
        if shares <= 0:
            return
        
        # 创建订单
        order = Order(
            order_id=f"multi_buy_{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=shares,
            price=price,
            timestamp=timestamp
        )
        
        # 风险检查
        if self.risk_manager and not self.risk_manager.check_order(order, portfolio):
            return
        
        # 执行交易
        self._execute_order(portfolio, order)
    
    def _execute_multi_sell(
        self,
        portfolio: Portfolio,
        symbol: str,
        price: float,
        timestamp: datetime,
        strength: float
    ):
        """执行多资产卖出"""
        position = portfolio.get_position(symbol)
        if position is None or position.is_flat:
            return
        
        # 计算卖出数量
        sell_quantity = int(position.quantity * strength)
        if sell_quantity <= 0:
            return
        
        # 创建订单
        order = Order(
            order_id=f"multi_sell_{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=sell_quantity,
            price=price,
            timestamp=timestamp
        )
        
        # 执行交易
        self._execute_order(portfolio, order)
    
    def _execute_order(self, portfolio: Portfolio, order: Order):
        """执行订单"""
        trade_value = order.quantity * order.price
        commission = self.backtest_config.calculate_commission(trade_value)
        slippage = self.backtest_config.calculate_slippage(trade_value)
        
        if order.side == OrderSide.BUY:
            total_cost = trade_value + commission + slippage
            if portfolio.cash < total_cost:
                return  # 资金不足
        
        # 创建交易记录
        trade = Trade(
            trade_id=f"trade_{order.order_id}",
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            price=order.price,
            timestamp=order.timestamp,
            commission=commission,
            slippage=slippage
        )
        
        # 更新投资组合
        portfolio.add_trade(trade)
    
    def _get_rebalance_dates(self, date_index: pd.DatetimeIndex, frequency: str) -> List[datetime]:
        """获取再平衡日期"""
        if frequency == 'daily':
            return date_index.tolist()
        elif frequency == 'weekly':
            return [date for date in date_index if date.weekday() == 0]  # 每周一
        elif frequency == 'monthly':
            return [date for date in date_index if date.day <= 7 and date.weekday() == 0]  # 每月第一个周一
        elif frequency == 'quarterly':
            return [date for date in date_index if date.month % 3 == 1 and date.day <= 7 and date.weekday() == 0]
        else:
            return []  # 不再平衡
    
    def _rebalance_portfolio(self, portfolio: Portfolio, current_prices: Dict[str, float], timestamp: datetime):
        """再平衡投资组合"""
        logger.debug(f"执行再平衡: {timestamp}")
        
        # 计算目标权重
        total_value = portfolio.total_value
        
        for symbol, target_weight in self._weights.items():
            target_value = total_value * target_weight
            current_position = portfolio.get_position(symbol)
            current_value = current_position.market_value if current_position else 0
            
            difference = target_value - current_value
            
            if abs(difference) > self.backtest_config.min_trade_amount:
                current_price = current_prices[symbol]
                
                if difference > 0:  # 需要买入
                    shares = int(difference / current_price)
                    if shares > 0:
                        order = Order(
                            order_id=f"rebalance_buy_{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
                            symbol=symbol,
                            side=OrderSide.BUY,
                            order_type=OrderType.MARKET,
                            quantity=shares,
                            price=current_price,
                            timestamp=timestamp
                        )
                        self._execute_order(portfolio, order)
                
                else:  # 需要卖出
                    shares = int(abs(difference) / current_price)
                    if shares > 0 and current_position and shares <= current_position.quantity:
                        order = Order(
                            order_id=f"rebalance_sell_{symbol}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
                            symbol=symbol,
                            side=OrderSide.SELL,
                            order_type=OrderType.MARKET,
                            quantity=shares,
                            price=current_price,
                            timestamp=timestamp
                        )
                        self._execute_order(portfolio, order)
    
    def _calculate_multi_result(
        self,
        portfolio: Portfolio,
        aligned_data: pd.DataFrame,
        signals_dict: Dict[str, pd.DataFrame],
        strategy: IStrategy,
        symbols: List[str]
    ) -> StrategyResult:
        """计算多资产回测结果"""
        # 基本指标
        total_return = portfolio.total_return
        
        # 计算年化收益率
        days = len(aligned_data)
        annual_return = (1 + total_return) ** (252 / days) - 1 if days > 0 else 0
        
        # 计算其他指标
        metrics = portfolio.calculate_metrics()
        
        # 创建结果对象
        result = StrategyResult(
            strategy_id=strategy.get_name(),
            symbol=','.join(symbols),
            start_date=aligned_data.index[0],
            end_date=aligned_data.index[-1],
            total_return=total_return,
            annual_return=annual_return,
            volatility=metrics.get('volatility', 0),
            sharpe_ratio=metrics.get('sharpe_ratio', 0),
            max_drawdown=metrics.get('max_drawdown', 0),
            total_trades=len(portfolio.trades),
            win_rate=metrics.get('win_rate', 0),
            portfolio=portfolio,
            equity_curve=portfolio.value_history,
            trades=portfolio.trades,
            signals=signals_dict  # 多资产信号字典
        )
        
        return result
    
    def get_current_symbols(self) -> List[str]:
        """获取当前回测标的列表"""
        return self._current_symbols.copy()
    
    def get_current_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        return self._weights.copy()
    
    def set_max_workers(self, max_workers: int):
        """设置最大并行工作线程数"""
        self._max_workers = max_workers
        logger.info(f"最大并行线程数设置为: {max_workers}")
