"""
Akshare数据源

基于Akshare库的数据源实现，提供A股市场数据获取功能。
"""

import akshare as ak
import pandas as pd
import numpy as np
import logging
from datetime import datetime
from dataseed.base import BaseDataSeed
from utils.logger import get_default_logger


class Akshare(BaseDataSeed):
    """基于Akshare的数据源实现"""
    
    def __init__(self):
        """初始化Akshare数据源"""
        self.logger = get_default_logger()

    def get_data(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d') -> pd.DataFrame:
        """
        从Akshare获取数据

        参数:
            symbol (str): 证券代码，例如 'sh000001', 'sz000001', 'sh510050'
            start_date (str): 开始日期，格式：YYYY-MM-DD
            end_date (str): 结束日期，格式：YYYY-MM-DD
            timeframe (str): 时间周期，支持 '1d'

        返回:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        try:
            self.logger.info(f"正在从Akshare获取数据: {symbol}, {start_date} 到 {end_date}")
            
            # 处理证券代码格式
            symbol_clean = self._clean_symbol(symbol)
            
            if timeframe == '1d':
                # 获取日线数据
                data = ak.stock_zh_a_hist(
                    symbol=symbol_clean,
                    period="daily",
                    start_date=start_date.replace('-', ''),
                    end_date=end_date.replace('-', ''),
                    adjust="qfq"  # 前复权
                )
            else:
                raise ValueError(f"不支持的时间周期: {timeframe}")
            
            if data is None or data.empty:
                self.logger.warning(f"未获取到数据: {symbol}")
                return pd.DataFrame()
            
            # 标准化数据格式
            data = self._standardize_akshare_data(data)
            
            # 验证数据
            if not self.validate_data(data):
                self.logger.error(f"数据验证失败: {symbol}")
                return pd.DataFrame()
            
            self.logger.info(f"成功获取数据: {len(data)} 条记录")
            return data
            
        except Exception as e:
            self.logger.error(f"获取数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _clean_symbol(self, symbol: str) -> str:
        """
        清理证券代码格式
        
        参数:
            symbol (str): 原始证券代码
            
        返回:
            str: 清理后的证券代码
        """
        # 移除前缀
        if symbol.startswith(('sh', 'sz')):
            return symbol[2:]
        return symbol
    
    def _standardize_akshare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        标准化Akshare数据格式
        
        参数:
            data (pd.DataFrame): Akshare原始数据
            
        返回:
            pd.DataFrame: 标准化后的数据
        """
        # Akshare数据列名映射
        column_mapping = {
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close', 
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'pct_change',
            '涨跌额': 'change',
            '换手率': 'turnover'
        }
        
        # 重命名列
        data = data.rename(columns=column_mapping)
        
        # 设置日期为索引
        if 'date' in data.columns:
            data['date'] = pd.to_datetime(data['date'])
            data.set_index('date', inplace=True)
        
        # 确保必需的列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in data.columns:
                self.logger.warning(f"缺少必需的列: {col}")
        
        # 只保留需要的列
        available_columns = [col for col in required_columns if col in data.columns]
        data = data[available_columns]
        
        # 转换数据类型
        for col in available_columns:
            data[col] = pd.to_numeric(data[col], errors='coerce')
        
        # 删除包含NaN的行
        data = data.dropna()
        
        return data
