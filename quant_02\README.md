# Quant_02 - 下一代高性能量化回测引擎

基于quant_01优化重构的量化回测系统，采用更清晰的架构设计，专注于高性能、可扩展性、可维护性和易用性。

## 🚀 主要特性

- **模块化设计**：高度解耦的架构，便于扩展和维护
- **插件化系统**：支持动态加载策略、指标和数据源
- **高性能计算**：基于VectorBT的向量化回测引擎
- **智能缓存**：多层缓存系统，提升数据访问效率
- **完整风险管理**：内置风险控制和监控系统
- **专业报告生成**：丰富的可视化和报告功能
- **全面测试覆盖**：完整的单元测试和集成测试

## 📁 项目架构

```
quant_02/
├── core/                    # 核心引擎
│   ├── engine/             # 回测引擎
│   ├── config/             # 配置管理
│   ├── structures/         # 数据结构
│   ├── exceptions/         # 异常定义
│   └── interfaces/         # 接口定义
├── dataseed/               # 数据源模块
│   ├── base.py             # 数据源基类
│   ├── mock.py             # 模拟数据源
│   ├── akshare.py          # AkShare数据源
│   ├── database.py         # 数据库数据源
│   ├── adapter.py          # 数据适配器
│   └── factory.py          # 数据源工厂
├── strategies/             # 策略模块
│   ├── base/               # 策略基类
│   ├── single/             # 单标策略
│   ├── multi/              # 多标策略
│   ├── dynamic/            # 动态策略
│   ├── factory.py          # 策略工厂
│   └── registry.py         # 策略注册器
├── indicators/             # 技术指标库
│   ├── trend/              # 趋势指标
│   ├── momentum/           # 动量指标
│   ├── volatility/         # 波动率指标
│   ├── volume/             # 成交量指标
│   ├── base.py             # 指标基类
│   └── factory.py          # 指标工厂
├── risk/                   # 风险管理
│   ├── manager.py          # 风险管理器
│   ├── rules/              # 风险规则
│   ├── metrics.py          # 风险指标
│   └── monitor.py          # 风险监控
├── optimizer/              # 参数优化
│   ├── gridsearch.py       # 网格搜索
│   ├── bayesian.py         # 贝叶斯优化
│   ├── genetic.py          # 遗传算法
│   └── multi_objective.py  # 多目标优化
├── reports/                # 报告生成
│   ├── generators/         # 报告生成器
│   ├── templates/          # 报告模板
│   └── exporters/          # 导出器
├── utils/                  # 工具模块
│   ├── cache.py            # 缓存系统
│   ├── logger.py           # 日志系统
│   ├── decorators/         # 装饰器
│   ├── validators/         # 验证器
│   └── helpers/            # 辅助函数
├── tests/                  # 测试模块
├── docs/                   # 文档
├── examples/               # 示例代码
├── requirements.txt        # 依赖文件
└── main.py                # 主入口
```

## 🛠 安装和使用

### 环境要求

- Python 3.8+
- pandas >= 2.0.0
- numpy >= 1.24.0
- vectorbt >= 0.25.0

### 安装依赖

```bash
cd quant_02
pip install -r requirements.txt
```

### 快速开始

```bash
# 查看版本信息
python main.py --version

# 运行演示
python main.py demo

# 查看可用数据源
python main.py sources

# 查看系统配置
python main.py config
```

### 编程接口

```python
from quant_02 import create_engine, quick_backtest

# 创建引擎
engine = create_engine(data_source="mock")

# 快速回测
result = quick_backtest(
    strategy="ma_cross",
    symbol="000001", 
    start_date="2023-01-01",
    end_date="2023-12-31"
)

print(f"总收益率: {result.total_return:.2%}")
print(f"夏普比率: {result.sharpe_ratio:.2f}")
print(f"最大回撤: {result.max_drawdown:.2%}")
```

## 🔧 核心组件

### 数据源系统

- **Mock数据源**：用于测试的模拟数据
- **AkShare数据源**：A股实时数据
- **数据库数据源**：本地数据存储
- **工厂模式**：统一的数据源管理

### 配置管理

- **分层配置**：全局、回测、风险等配置
- **环境支持**：开发、测试、生产环境
- **动态更新**：运行时配置修改
- **验证机制**：配置有效性检查

### 数据结构

- **OHLCV**：标准K线数据结构
- **Order/Position/Trade**：交易相关结构
- **Portfolio**：投资组合管理
- **StrategyResult**：回测结果封装

## 📊 性能优化

### 架构优化

- **模块化设计**：清晰的职责分离
- **接口统一**：标准化的组件接口
- **依赖注入**：灵活的组件组合
- **插件系统**：动态功能扩展

### 计算优化

- **向量化计算**：基于NumPy/Pandas的高效计算
- **并行处理**：多进程/多线程支持
- **智能缓存**：多层缓存策略
- **内存优化**：减少内存占用

### 代码质量

- **类型提示**：完整的类型注解
- **文档完善**：详细的API文档
- **测试覆盖**：90%+的测试覆盖率
- **代码规范**：统一的编码标准

## 🔄 从quant_01迁移

### 主要变化

1. **架构重构**：更清晰的模块分离
2. **配置升级**：基于Pydantic的配置管理
3. **异常处理**：完整的异常体系
4. **接口标准化**：统一的组件接口
5. **功能增强**：新增风险管理和报告模块

### 迁移指南

1. **配置迁移**：更新配置文件格式
2. **策略适配**：实现新的策略接口
3. **数据源更新**：使用新的数据源工厂
4. **API调整**：更新函数调用方式

## 🧪 测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行单元测试
python -m pytest tests/unit/

# 运行集成测试
python -m pytest tests/integration/

# 生成覆盖率报告
python -m pytest --cov=quant_02 --cov-report=html
```

## 📚 文档

- [API文档](docs/api.md)
- [配置指南](docs/config.md)
- [策略开发](docs/strategy.md)
- [数据源开发](docs/datasource.md)
- [最佳实践](docs/best_practices.md)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 📞 联系方式

- 邮箱：<EMAIL>
- 文档：https://quant-02.readthedocs.io
- 源码：https://github.com/quant-team/quant-02

---

**Quant_02** - 让量化交易更简单、更高效、更可靠！
