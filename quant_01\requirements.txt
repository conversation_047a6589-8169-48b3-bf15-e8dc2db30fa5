# 核心依赖
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.0.0
loguru>=0.7.0

# 回测引擎
vectorbt>=0.25.0

# 数据源
akshare>=1.11.0
sqlalchemy>=2.0.0

# 性能优化
numba>=0.57.0
redis>=4.5.0

# 缓存系统
diskcache>=5.6.0

# 可视化
plotly>=5.15.0
matplotlib>=3.7.0

# 异步支持
aiofiles>=23.0.0
asyncio-mqtt>=0.13.0

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# 代码质量
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# 文档生成
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# 可选依赖 - Web界面
fastapi>=0.100.0
streamlit>=1.25.0
uvicorn>=0.23.0

# 可选依赖 - 机器学习
scikit-learn>=1.3.0
scipy>=1.11.0

# 可选依赖 - 数据库
psycopg2-binary>=2.9.0
pymongo>=4.4.0

# 可选依赖 - 消息队列
celery>=5.3.0
redis>=4.5.0

# 开发工具
jupyter>=1.0.0
ipython>=8.14.0
