"""
模拟数据源

用于测试和开发的模拟数据源，生成符合真实市场特征的随机数据。
"""

import pandas as pd
import numpy as np
from dataseed.base import BaseDataSeed


class Mock(BaseDataSeed):
    """模拟数据源，用于测试和开发"""
    
    def get_data(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d') -> pd.DataFrame:
        """
        生成模拟数据
        
        参数:
            symbol (str): 证券代码（用于随机种子）
            start_date (str): 开始日期，格式：YYYY-MM-DD
            end_date (str): 结束日期，格式：YYYY-MM-DD
            timeframe (str): 时间周期，支持 '1d', '1h', '1m'
            
        返回:
            pd.DataFrame: 包含OHLCV数据的DataFrame
        """
        # 创建日期范围
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        # 根据时间周期生成不同的日期范围
        dates = self._generate_dates(start, end, timeframe)
        
        if len(dates) == 0:
            return pd.DataFrame()
        
        # 生成随机价格数据
        data = self._generate_price_data(dates, symbol)
        
        return data
    
    def _generate_dates(self, start: pd.Timestamp, end: pd.Timestamp, timeframe: str) -> pd.DatetimeIndex:
        """
        生成日期序列
        
        参数:
            start (pd.Timestamp): 开始日期
            end (pd.Timestamp): 结束日期
            timeframe (str): 时间周期
            
        返回:
            pd.DatetimeIndex: 日期序列
        """
        if timeframe == '1d':
            # 工作日
            dates = pd.date_range(start=start, end=end, freq='B')
        elif timeframe == '1h':
            # 每小时，只保留交易时间 (9:30-11:30, 13:00-15:00)
            dates = pd.date_range(start=start, end=end, freq='H')
            trading_hours = (
                ((dates.hour >= 9) & (dates.hour <= 11)) | 
                ((dates.hour >= 13) & (dates.hour <= 15))
            )
            dates = dates[trading_hours]
        elif timeframe == '1m':
            # 每分钟，只保留交易时间
            dates = pd.date_range(start=start, end=end, freq='min')
            trading_hours = (
                ((dates.hour >= 9) & (dates.hour <= 11)) | 
                ((dates.hour >= 13) & (dates.hour <= 15))
            )
            dates = dates[trading_hours]
        else:
            raise ValueError(f"不支持的时间周期: {timeframe}")
        
        return dates
    
    def _generate_price_data(self, dates: pd.DatetimeIndex, symbol: str) -> pd.DataFrame:
        """
        生成价格数据
        
        参数:
            dates (pd.DatetimeIndex): 日期序列
            symbol (str): 证券代码（用于随机种子）
            
        返回:
            pd.DataFrame: 价格数据
        """
        n = len(dates)
        
        # 使用symbol作为随机种子的一部分，确保相同symbol生成相同数据
        seed = hash(symbol) % (2**32)
        np.random.seed(seed)
        
        # 生成随机走势 - 模拟真实股价波动
        # 基础趋势
        trend = np.random.choice([-1, 0, 1], p=[0.3, 0.4, 0.3])  # 下跌、横盘、上涨
        base_return = trend * 0.0002  # 基础日收益率
        
        # 随机波动
        volatility = np.random.uniform(0.01, 0.03)  # 波动率
        returns = np.random.normal(base_return, volatility, n)
        
        # 添加一些趋势性和均值回归
        for i in range(1, n):
            # 趋势延续
            if abs(returns[i-1]) > volatility:
                returns[i] += 0.3 * returns[i-1]
            
            # 均值回归
            cumulative_return = np.sum(returns[:i])
            if abs(cumulative_return) > 0.1:  # 如果累计涨跌幅超过10%
                returns[i] -= 0.1 * cumulative_return
        
        # 生成价格序列
        initial_price = np.random.uniform(10, 100)  # 初始价格
        price = initial_price * np.exp(np.cumsum(returns))
        
        # 生成OHLCV数据
        data = self._generate_ohlcv(price, dates)
        
        return data
    
    def _generate_ohlcv(self, close_prices: np.ndarray, dates: pd.DatetimeIndex) -> pd.DataFrame:
        """
        基于收盘价生成OHLCV数据
        
        参数:
            close_prices (np.ndarray): 收盘价序列
            dates (pd.DatetimeIndex): 日期序列
            
        返回:
            pd.DataFrame: OHLCV数据
        """
        n = len(close_prices)
        
        # 生成开盘价（基于前一日收盘价加上跳空）
        open_prices = np.zeros(n)
        open_prices[0] = close_prices[0]
        for i in range(1, n):
            gap = np.random.normal(0, 0.005)  # 跳空幅度
            open_prices[i] = close_prices[i-1] * (1 + gap)
        
        # 生成最高价和最低价
        high_prices = np.zeros(n)
        low_prices = np.zeros(n)
        
        for i in range(n):
            # 当日波动范围
            daily_range = np.random.uniform(0.01, 0.05)
            
            # 最高价和最低价
            mid_price = (open_prices[i] + close_prices[i]) / 2
            high_prices[i] = mid_price * (1 + daily_range/2)
            low_prices[i] = mid_price * (1 - daily_range/2)
            
            # 确保价格关系正确
            high_prices[i] = max(high_prices[i], open_prices[i], close_prices[i])
            low_prices[i] = min(low_prices[i], open_prices[i], close_prices[i])
        
        # 生成成交量
        base_volume = np.random.randint(100000, 1000000)
        volume_volatility = 0.5
        volume = np.random.lognormal(
            np.log(base_volume), 
            volume_volatility, 
            n
        ).astype(int)
        
        # 创建DataFrame
        data = pd.DataFrame({
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volume
        }, index=dates)
        
        return data
