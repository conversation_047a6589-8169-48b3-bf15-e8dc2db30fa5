"""
基础功能测试

测试Quant_01系统的基础功能。
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_data_structures():
    """测试数据结构"""
    print("测试数据结构...")
    
    from core.data_structures.market import OHLCV
    from core.data_structures.trading import Order, OrderSide, OrderType, OrderStatus
    from datetime import datetime
    
    # 测试OHLCV
    ohlcv = OHLCV(
        open=100.0,
        high=105.0,
        low=98.0,
        close=103.0,
        volume=1000000,
        timestamp=datetime.now()
    )
    
    print(f"OHLCV: {ohlcv}")
    print(f"涨跌幅: {ohlcv.change_ratio:.2%}")
    print(f"振幅: {ohlcv.amplitude:.2%}")
    
    # 测试Order
    order = Order(
        order_id="test_001",
        symbol="000001",
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=1000,
        timestamp=datetime.now()
    )
    
    print(f"订单: {order}")
    print(f"是否买单: {order.is_buy}")
    
    print("✓ 数据结构测试通过\n")


def test_config():
    """测试配置系统"""
    print("测试配置系统...")
    
    from core.config.global_config import GlobalConfig
    
    # 创建默认配置
    config = GlobalConfig.create_default()
    
    print(f"应用名称: {config.app_name}")
    print(f"环境: {config.environment}")
    print(f"日志级别: {config.logging.level}")
    print(f"缓存启用: {config.cache.enabled}")
    
    # 测试配置保存和加载
    config_path = current_dir / "test_config.json"
    config.save_to_file(config_path)
    
    loaded_config = GlobalConfig.load_from_file(config_path)
    print(f"配置加载成功: {loaded_config.app_name}")
    
    # 清理测试文件
    config_path.unlink(missing_ok=True)
    
    print("✓ 配置系统测试通过\n")


def test_mock_data_source():
    """测试Mock数据源"""
    print("测试Mock数据源...")
    
    from dataseed.mock_source import MockDataSource
    
    # 创建Mock数据源
    mock_source = MockDataSource(
        name="test_mock",
        base_price=100.0,
        volatility=0.02,
        market_state="normal"
    )
    
    # 获取数据
    data = mock_source.get_data(
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-01-31"
    )
    
    print(f"数据形状: {data.shape}")
    print(f"数据列: {list(data.columns)}")
    print(f"数据范围: {data.index[0]} 到 {data.index[-1]}")
    print(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
    
    # 测试数据质量
    assert not data.empty, "数据不能为空"
    assert all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume']), "缺少必要列"
    assert (data['high'] >= data[['open', 'close']].max(axis=1)).all(), "最高价逻辑错误"
    assert (data['low'] <= data[['open', 'close']].min(axis=1)).all(), "最低价逻辑错误"
    
    print("✓ Mock数据源测试通过\n")


def test_indicators():
    """测试技术指标"""
    print("测试技术指标...")
    
    from indicators.trend.macd import MACD
    from dataseed.mock_source import MockDataSource
    
    # 获取测试数据
    mock_source = MockDataSource()
    data = mock_source.get_data("000001", "2023-01-01", "2023-12-31")
    
    # 测试MACD指标
    macd = MACD(fast_period=12, slow_period=26, signal_period=9)
    result = macd.calculate(data['close'])
    
    print(f"MACD结果类型: {type(result)}")
    print(f"MACD组件: {list(result.keys())}")
    print(f"MACD数据长度: {len(result['macd'])}")
    
    # 检查结果
    assert 'macd' in result, "缺少MACD线"
    assert 'signal' in result, "缺少信号线"
    assert 'histogram' in result, "缺少柱状图"
    assert len(result['macd']) == len(data), "数据长度不匹配"
    
    # 测试信号生成
    signals = macd.get_signals(data['close'])
    buy_signals = (signals['signal'] == 1).sum()
    sell_signals = (signals['signal'] == -1).sum()
    
    print(f"买入信号: {buy_signals}个")
    print(f"卖出信号: {sell_signals}个")
    
    print("✓ 技术指标测试通过\n")


def test_strategy():
    """测试策略"""
    print("测试策略...")
    
    from strategies.single.macd import MACDStrategy, MACDConfig
    from dataseed.mock_source import MockDataSource
    
    # 创建策略
    config = MACDConfig(
        strategy_name="test_macd",
        fast_period=12,
        slow_period=26,
        signal_period=9
    )
    
    strategy = MACDStrategy(config)
    
    # 获取测试数据
    mock_source = MockDataSource()
    data = mock_source.get_data("000001", "2023-01-01", "2023-12-31")
    
    # 运行策略
    result = strategy.run(data)
    
    print(f"策略名称: {result.strategy_id}")
    print(f"总收益率: {result.total_return:.2%}")
    print(f"年化收益率: {result.annual_return:.2%}")
    print(f"夏普比率: {result.sharpe_ratio:.2f}")
    print(f"最大回撤: {result.max_drawdown:.2%}")
    print(f"交易次数: {result.total_trades}")
    print(f"胜率: {result.win_rate:.2%}")
    
    # 检查结果
    assert result.strategy_id == strategy.strategy_id, "策略ID不匹配"
    assert isinstance(result.total_return, float), "总收益率类型错误"
    assert isinstance(result.total_trades, int), "交易次数类型错误"
    
    print("✓ 策略测试通过\n")


def test_engine():
    """测试回测引擎"""
    print("测试回测引擎...")
    
    from core.engine.base import QuantEngine
    from dataseed.mock_source import MockDataSource
    from strategies.single.macd import MACDStrategy
    
    # 创建引擎
    engine = QuantEngine()
    
    # 设置数据源
    mock_source = MockDataSource()
    engine.set_data_source(mock_source)
    
    # 注册策略
    strategy = MACDStrategy.create_default()
    engine.register_strategy(strategy, "macd")
    
    # 运行回测
    result = engine.run_strategy(
        strategy="macd",
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-12-31"
    )
    
    print(f"引擎回测结果:")
    print(f"  总收益率: {result.total_return:.2%}")
    print(f"  夏普比率: {result.sharpe_ratio:.2f}")
    print(f"  交易次数: {result.total_trades}")
    
    # 测试批量回测
    batch_results = engine.run_batch_backtest(
        strategy="macd",
        symbols=["000001", "000002"],
        start_date="2023-01-01",
        end_date="2023-12-31",
        parallel=False  # 串行测试
    )
    
    print(f"批量回测结果: {len(batch_results)}个")
    
    # 获取统计信息
    stats = engine.get_stats()
    print(f"引擎统计: {stats}")
    
    print("✓ 回测引擎测试通过\n")


def test_quick_start():
    """测试快速开始"""
    print("测试快速开始...")
    
    from main import QuickStart
    
    # 创建快速开始实例
    qs = QuickStart(data_source="mock")
    
    # 运行简单回测
    result = qs.run_simple_backtest(
        strategy="macd",
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-06-30"
    )
    
    print("简单回测结果:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    # 测试策略对比
    comparison = qs.compare_strategies(
        strategies=["macd", "macd_fast"],
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-06-30"
    )
    
    print(f"\n策略对比结果:")
    print(comparison.to_string(index=False))
    
    print("\n✓ 快速开始测试通过\n")


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("Quant_01 基础功能测试")
    print("=" * 60)
    
    try:
        test_data_structures()
        test_config()
        test_mock_data_source()
        test_indicators()
        test_strategy()
        test_engine()
        test_quick_start()
        
        print("=" * 60)
        print("✅ 所有测试通过！")
        print("Quant_01 系统基础功能正常")
        print("=" * 60)
        
    except Exception as e:
        print("=" * 60)
        print(f"❌ 测试失败: {e}")
        print("=" * 60)
        raise


if __name__ == "__main__":
    run_all_tests()
