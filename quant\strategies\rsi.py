"""
RSI策略

基于相对强弱指数(RSI)的交易策略。
当RSI低于超卖阈值时买入，当RSI高于超买阈值时卖出。
"""

import pandas as pd
import vectorbt as vbt
from typing import Dict
from strategies.base import BaseStrategy


class RSI(BaseStrategy):
    """相对强弱指数(RSI)策略"""

    def __init__(self, params=None):
        """
        初始化RSI策略

        参数:
            params (dict): 策略参数，包含：
                - window (int): RSI计算周期，默认14
                - overbought (int): 超买阈值，默认70
                - oversold (int): 超卖阈值，默认30
        """
        super().__init__(params)
        self.window = self.get_param('window', 14)
        self.overbought = self.get_param('overbought', 70)
        self.oversold = self.get_param('oversold', 30)

    @staticmethod
    def get_params_schema():
        """获取参数验证模式"""
        return {
            "window": {
                "type": "integer",
                "title": "RSI周期",
                "description": "RSI指标的计算周期",
                "default": 14,
                "minimum": 2,
                "maximum": 50
            },
            "overbought": {
                "type": "integer",
                "title": "超买阈值",
                "description": "RSI超买阈值，高于此值时卖出",
                "default": 70,
                "minimum": 50,
                "maximum": 100
            },
            "oversold": {
                "type": "integer",
                "title": "超卖阈值",
                "description": "RSI超卖阈值，低于此值时买入",
                "default": 30,
                "minimum": 0,
                "maximum": 50
            }
        }

    def run(self, data: pd.DataFrame) -> tuple:
        """
        运行RSI策略

        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame

        返回:
            tuple: (entries, exits) 买入和卖出信号
        """
        # 验证数据
        if not self.validate_data(data):
            raise ValueError("输入数据无效")

        # 验证参数
        if self.oversold >= self.overbought:
            raise ValueError("超卖阈值必须小于超买阈值")

        # 计算RSI指标
        rsi = vbt.RSI.run(data['close'], window=self.window).rsi

        # 生成交易信号
        # RSI低于超卖阈值：买入信号
        entries = (rsi < self.oversold).astype(bool)
        # RSI高于超买阈值：卖出信号
        exits = (rsi > self.overbought).astype(bool)

        # 处理NaN值
        entries = entries.fillna(False)
        exits = exits.fillna(False)

        return entries, exits

    def get_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        获取策略使用的技术指标

        参数:
            data (pd.DataFrame): 输入数据

        返回:
            dict: 技术指标字典
        """
        rsi = vbt.RSI.run(data['close'], window=self.window).rsi

        return {
            'rsi': rsi,
            'price': data['close'],
            'overbought_line': pd.Series(self.overbought, index=data.index),
            'oversold_line': pd.Series(self.oversold, index=data.index)
        }
