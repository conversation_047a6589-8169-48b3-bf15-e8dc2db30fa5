"""
数据源抽象基类

定义统一的数据访问接口，所有数据源实现都必须继承此基类。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date
import pandas as pd

from ..core.data_structures import OHLCV
from ..core.config.backtest import FreqType, AssetType
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DataSeed(ABC):
    """数据源抽象基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化数据源
        
        Args:
            config: 数据源配置
        """
        self.config = config or {}
        self._cache = {}
        self._setup()
    
    def _setup(self):
        """数据源初始化设置"""
        pass
    
    @abstractmethod
    def get_daily_data(
        self, 
        symbol: str, 
        start_date: Union[str, date, datetime], 
        end_date: Union[str, date, datetime],
        adjust: Optional[str] = None
    ) -> pd.DataFrame:
        """
        获取日线数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            adjust: 复权类型 ('qfq', 'hfq', None)
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        pass
    
    @abstractmethod
    def get_minute_data(
        self,
        symbol: str,
        date: Union[str, date, datetime],
        freq: FreqType = FreqType.MINUTE_1
    ) -> pd.DataFrame:
        """
        获取分钟级数据
        
        Args:
            symbol: 股票代码
            date: 日期
            freq: 数据频率
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        pass
    
    @abstractmethod
    def get_universe(
        self, 
        asset_type: AssetType = AssetType.STOCK,
        market: Optional[str] = None
    ) -> List[str]:
        """
        获取标的列表
        
        Args:
            asset_type: 资产类型
            market: 市场代码
            
        Returns:
            股票代码列表
        """
        pass
    
    def get_basic_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取标的基本信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            基本信息字典
        """
        return {}
    
    def get_financial_data(
        self, 
        symbol: str,
        report_type: str = "annual"
    ) -> pd.DataFrame:
        """
        获取财务数据
        
        Args:
            symbol: 股票代码
            report_type: 报告类型 ('annual', 'quarterly')
            
        Returns:
            财务数据DataFrame
        """
        return pd.DataFrame()
    
    def get_index_data(
        self,
        index_code: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime]
    ) -> pd.DataFrame:
        """
        获取指数数据
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            指数数据DataFrame
        """
        return pd.DataFrame()
    
    def get_industry_data(self) -> pd.DataFrame:
        """
        获取行业分类数据
        
        Returns:
            行业分类DataFrame
        """
        return pd.DataFrame()
    
    def is_trading_day(self, date: Union[str, date, datetime]) -> bool:
        """
        判断是否为交易日
        
        Args:
            date: 日期
            
        Returns:
            是否为交易日
        """
        return True
    
    def get_trading_calendar(
        self,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime]
    ) -> List[date]:
        """
        获取交易日历
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            交易日列表
        """
        return []
    
    def validate_symbol(self, symbol: str) -> bool:
        """
        验证股票代码是否有效
        
        Args:
            symbol: 股票代码
            
        Returns:
            是否有效
        """
        return True
    
    def normalize_symbol(self, symbol: str) -> str:
        """
        标准化股票代码格式
        
        Args:
            symbol: 原始股票代码
            
        Returns:
            标准化后的股票代码
        """
        return symbol.upper()
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """
        获取最新价格
        
        Args:
            symbol: 股票代码
            
        Returns:
            最新价格
        """
        return None
    
    def batch_get_data(
        self,
        symbols: List[str],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        freq: FreqType = FreqType.DAILY
    ) -> Dict[str, pd.DataFrame]:
        """
        批量获取数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            freq: 数据频率
            
        Returns:
            {symbol: DataFrame} 字典
        """
        result = {}
        for symbol in symbols:
            try:
                if freq == FreqType.DAILY:
                    data = self.get_daily_data(symbol, start_date, end_date)
                else:
                    # 对于非日线数据，需要逐日获取
                    data = pd.DataFrame()
                result[symbol] = data
            except Exception as e:
                logger.warning(f"获取 {symbol} 数据失败: {e}")
                result[symbol] = pd.DataFrame()
        return result


class DataAdapter(ABC):
    """数据适配器抽象基类"""
    
    @abstractmethod
    def to_standard_format(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        转换为标准格式
        
        Args:
            data: 原始数据
            
        Returns:
            标准格式数据
        """
        pass
    
    @abstractmethod
    def to_ohlcv_list(self, data: pd.DataFrame) -> List[OHLCV]:
        """
        转换为OHLCV对象列表
        
        Args:
            data: DataFrame数据
            
        Returns:
            OHLCV对象列表
        """
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证数据完整性
        
        Args:
            data: 数据DataFrame
            
        Returns:
            是否有效
        """
        if data.empty:
            return False
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        return all(col in data.columns for col in required_columns)
    
    def clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        清理数据
        
        Args:
            data: 原始数据
            
        Returns:
            清理后的数据
        """
        # 移除空值
        data = data.dropna()
        
        # 移除异常值（价格为0或负数）
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in data.columns:
                data = data[data[col] > 0]
        
        # 移除成交量为负数的记录
        if 'volume' in data.columns:
            data = data[data['volume'] >= 0]
        
        return data


__all__ = [
    "DataSeed",
    "DataAdapter"
]
