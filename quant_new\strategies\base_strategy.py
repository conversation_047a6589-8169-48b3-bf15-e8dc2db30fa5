from abc import ABC, abstractmethod
from typing import List
from datetime import datetime
from ..core.data_structures import OHLCV, Order

class BaseStrategy(ABC):
    """策略基类，所有交易策略都应继承此类"""
    
    def __init__(self, name: str):
        self.name = name
        self.initialized = False
        
    @abstractmethod
    def initialize(self, **kwargs):
        """策略初始化"""
        self.initialized = True
        
    @abstractmethod
    def on_bar(self, bar: OHLCV, account_info) -> List[Order]:
        """处理新的K线数据"""
        pass
        
    @abstractmethod
    def on_tick(self, tick_data, account_info) -> List[Order]:
        """处理tick数据"""
        pass
        
    @abstractmethod
    def on_order(self, order: Order):
        """订单状态更新回调"""
        pass
        
    @abstractmethod
    def on_trade(self, trade_info):
        """成交回报回调"""
        pass
        
    def get_name(self) -> str:
        """获取策略名称"""
        return self.name
        
    def is_initialized(self) -> bool:
        """检查策略是否已初始化"""
        return self.initialized