"""
AkShare数据源实现

基于AkShare库的A股数据获取实现。
"""

import re
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date, timedelta
import pandas as pd

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    ak = None

from .base import DataSeed
from ..core.config.backtest import FreqType, AssetType
from ..utils.logger import get_logger
from ..utils.cache.decorators import cached

logger = get_logger(__name__)


class AkShareDataSeed(DataSeed):
    """AkShare数据源实现"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化AkShare数据源
        
        Args:
            config: 配置参数
        """
        if not AKSHARE_AVAILABLE:
            raise ImportError("AkShare库未安装，请运行: pip install akshare")
        
        super().__init__(config)
        
        # 配置参数
        self.timeout = self.config.get('timeout', 30)
        self.retry_count = self.config.get('retry_count', 3)
        self.cache_enabled = self.config.get('cache_enabled', True)
        
        logger.info("AkShare数据源初始化完成")
    
    def _setup(self):
        """初始化设置"""
        # 设置AkShare参数
        if hasattr(ak, 'set_token'):
            token = self.config.get('token')
            if token:
                ak.set_token(token)
    
    def _normalize_date(self, date_input: Union[str, date, datetime]) -> str:
        """标准化日期格式"""
        if isinstance(date_input, str):
            # 移除分隔符
            date_str = re.sub(r'[-/]', '', date_input)
            if len(date_str) == 8:
                return date_str
            else:
                # 尝试解析
                try:
                    dt = datetime.strptime(date_input, '%Y-%m-%d')
                    return dt.strftime('%Y%m%d')
                except ValueError:
                    try:
                        dt = datetime.strptime(date_input, '%Y/%m/%d')
                        return dt.strftime('%Y%m%d')
                    except ValueError:
                        raise ValueError(f"无效的日期格式: {date_input}")
        elif isinstance(date_input, (date, datetime)):
            return date_input.strftime('%Y%m%d')
        else:
            raise ValueError(f"不支持的日期类型: {type(date_input)}")
    
    def _retry_request(self, func, *args, **kwargs):
        """重试请求"""
        for i in range(self.retry_count):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if i == self.retry_count - 1:
                    raise e
                logger.warning(f"请求失败，第{i+1}次重试: {e}")
                import time
                time.sleep(1)
    
    @cached(ttl=3600)  # 缓存1小时
    def get_daily_data(
        self, 
        symbol: str, 
        start_date: Union[str, date, datetime], 
        end_date: Union[str, date, datetime],
        adjust: Optional[str] = None
    ) -> pd.DataFrame:
        """
        获取日线数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            adjust: 复权类型 ('qfq', 'hfq', None)
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # 标准化日期
            start_str = self._normalize_date(start_date)
            end_str = self._normalize_date(end_date)
            
            # 标准化股票代码
            symbol = self.normalize_symbol(symbol)
            
            # 获取数据
            if adjust == 'qfq':
                data = self._retry_request(
                    ak.stock_zh_a_hist, 
                    symbol=symbol, 
                    period="daily", 
                    start_date=start_str, 
                    end_date=end_str, 
                    adjust="qfq"
                )
            elif adjust == 'hfq':
                data = self._retry_request(
                    ak.stock_zh_a_hist, 
                    symbol=symbol, 
                    period="daily", 
                    start_date=start_str, 
                    end_date=end_str, 
                    adjust="hfq"
                )
            else:
                data = self._retry_request(
                    ak.stock_zh_a_hist, 
                    symbol=symbol, 
                    period="daily", 
                    start_date=start_str, 
                    end_date=end_str, 
                    adjust=""
                )
            
            if data.empty:
                logger.warning(f"未获取到 {symbol} 的数据")
                return pd.DataFrame()
            
            # 标准化列名
            data = data.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close', 
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_chg',
                '涨跌额': 'change',
                '换手率': 'turnover'
            })
            
            # 设置日期索引
            if 'date' in data.columns:
                data['date'] = pd.to_datetime(data['date'])
                data.set_index('date', inplace=True)
            
            # 确保数据类型正确
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            # 移除空值
            data = data.dropna()
            
            logger.debug(f"获取 {symbol} 日线数据成功，共 {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_minute_data(
        self,
        symbol: str,
        date: Union[str, date, datetime],
        freq: FreqType = FreqType.MINUTE_1
    ) -> pd.DataFrame:
        """
        获取分钟级数据
        
        Args:
            symbol: 股票代码
            date: 日期
            freq: 数据频率
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # AkShare的分钟数据接口
            date_str = self._normalize_date(date)
            symbol = self.normalize_symbol(symbol)
            
            # 映射频率
            period_map = {
                FreqType.MINUTE_1: "1",
                FreqType.MINUTE_5: "5", 
                FreqType.MINUTE_15: "15",
                FreqType.MINUTE_30: "30",
                FreqType.HOURLY: "60"
            }
            
            period = period_map.get(freq, "1")
            
            data = self._retry_request(
                ak.stock_zh_a_hist_min_em,
                symbol=symbol,
                start_date=date_str + " 09:30:00",
                end_date=date_str + " 15:00:00",
                period=period,
                adjust=""
            )
            
            if data.empty:
                return pd.DataFrame()
            
            # 标准化列名
            data = data.rename(columns={
                '时间': 'datetime',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high', 
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            })
            
            # 设置时间索引
            if 'datetime' in data.columns:
                data['datetime'] = pd.to_datetime(data['datetime'])
                data.set_index('datetime', inplace=True)
            
            # 确保数据类型正确
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            data = data.dropna()
            
            logger.debug(f"获取 {symbol} 分钟数据成功，共 {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取 {symbol} 分钟数据失败: {e}")
            return pd.DataFrame()
    
    @cached(ttl=86400)  # 缓存24小时
    def get_universe(
        self, 
        asset_type: AssetType = AssetType.STOCK,
        market: Optional[str] = None
    ) -> List[str]:
        """
        获取标的列表
        
        Args:
            asset_type: 资产类型
            market: 市场代码 ('sh', 'sz', None)
            
        Returns:
            股票代码列表
        """
        try:
            if asset_type == AssetType.STOCK:
                # 获取A股列表
                data = self._retry_request(ak.stock_zh_a_spot_em)
                
                if data.empty:
                    return []
                
                # 提取股票代码
                symbols = data['代码'].tolist()
                
                # 按市场过滤
                if market:
                    if market.lower() == 'sh':
                        symbols = [s for s in symbols if s.startswith('6')]
                    elif market.lower() == 'sz':
                        symbols = [s for s in symbols if s.startswith(('0', '3'))]
                
                logger.info(f"获取股票列表成功，共 {len(symbols)} 只股票")
                return symbols
                
            elif asset_type == AssetType.FUND:
                # 获取基金列表
                data = self._retry_request(ak.fund_etf_spot_em)
                if not data.empty:
                    return data['代码'].tolist()
                    
            elif asset_type == AssetType.INDEX:
                # 获取指数列表
                data = self._retry_request(ak.index_stock_info)
                if not data.empty:
                    return data['index_code'].tolist()
            
            return []
            
        except Exception as e:
            logger.error(f"获取标的列表失败: {e}")
            return []
    
    def normalize_symbol(self, symbol: str) -> str:
        """
        标准化股票代码格式
        
        Args:
            symbol: 原始股票代码
            
        Returns:
            标准化后的股票代码
        """
        # 移除前缀和后缀
        symbol = symbol.upper()
        symbol = re.sub(r'[.SH|.SZ|.BJ]', '', symbol)
        
        # 确保6位数字
        if symbol.isdigit() and len(symbol) == 6:
            return symbol
        
        return symbol
    
    def get_basic_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取股票基本信息
        
        Args:
            symbol: 股票代码
            
        Returns:
            基本信息字典
        """
        try:
            symbol = self.normalize_symbol(symbol)
            data = self._retry_request(ak.stock_individual_info_em, symbol=symbol)
            
            if data.empty:
                return {}
            
            # 转换为字典
            info = {}
            for _, row in data.iterrows():
                info[row['item']] = row['value']
            
            return info
            
        except Exception as e:
            logger.error(f"获取 {symbol} 基本信息失败: {e}")
            return {}
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """
        获取最新价格
        
        Args:
            symbol: 股票代码
            
        Returns:
            最新价格
        """
        try:
            symbol = self.normalize_symbol(symbol)
            data = self._retry_request(ak.stock_zh_a_spot_em)
            
            if data.empty:
                return None
            
            # 查找对应股票
            stock_data = data[data['代码'] == symbol]
            if stock_data.empty:
                return None
            
            return float(stock_data.iloc[0]['最新价'])
            
        except Exception as e:
            logger.error(f"获取 {symbol} 最新价格失败: {e}")
            return None


__all__ = [
    "AkShareDataSeed"
]
