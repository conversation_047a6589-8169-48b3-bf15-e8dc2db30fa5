"""
量化回测引擎主入口

提供简单易用的接口来运行量化策略回测。
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
import pandas as pd

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置环境变量
os.environ['PYTHONPATH'] = str(current_dir)

try:
    from core.config.backtest import BacktestConfig
    from dataseed.mock import MockDataSeed
    from dataseed.akshare import AkShareDataSeed
    from strategies.single.macd import MACDStrategy, MACDConfig
    from strategies.single.rsi import RSIStrategy, RSIConfig
    from utils.logger import init_logger, get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在quant_new目录下运行此脚本")
    sys.exit(1)

# 初始化日志系统
init_logger()
logger = get_logger(__name__)


class QuantEngine:
    """量化回测引擎主类"""

    def __init__(self, data_source: str = "mock", config: Optional[Dict[str, Any]] = None):
        """
        初始化量化引擎

        Args:
            data_source: 数据源类型 ("mock", "akshare", "database")
            config: 引擎配置参数
        """
        self.data_source_type = data_source
        self.data_source = self._create_data_source(data_source, config)
        self.config = config or {}

        # 缓存管理
        self._results_cache = {}
        self._data_cache = {}

        # 性能统计
        self._stats = {
            'total_backtests': 0,
            'successful_backtests': 0,
            'failed_backtests': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }

        logger.info(f"量化引擎初始化完成，数据源: {data_source}")

    def _create_data_source(self, source_type: str, config: Optional[Dict[str, Any]] = None):
        """创建数据源"""
        config = config or {}

        if source_type == "mock":
            return MockDataSeed(config.get('mock_config', {}))
        elif source_type == "akshare":
            try:
                return AkShareDataSeed(config.get('akshare_config', {}))
            except ImportError:
                logger.warning("AkShare未安装，使用Mock数据源")
                return MockDataSeed(config.get('mock_config', {}))
        elif source_type == "database":
            try:
                from dataseed.database import DatabaseDataSeed
                return DatabaseDataSeed(config.get('database_config', {}))
            except ImportError:
                logger.warning("数据库依赖未安装，使用Mock数据源")
                return MockDataSeed(config.get('mock_config', {}))
        else:
            raise ValueError(f"不支持的数据源类型: {source_type}")

    def run_strategy(
        self,
        strategy_name: str,
        symbol: str,
        start_date: str,
        end_date: str,
        strategy_params: Optional[Dict[str, Any]] = None,
        backtest_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        运行策略回测

        Args:
            strategy_name: 策略名称 ("macd", "rsi")
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            strategy_params: 策略参数
            backtest_params: 回测参数

        Returns:
            回测结果字典
        """
        try:
            # 获取数据
            logger.info(f"获取 {symbol} 数据: {start_date} 到 {end_date}")
            data = self.data_source.get_daily_data(symbol, start_date, end_date)

            if data.empty:
                raise ValueError(f"未获取到 {symbol} 的数据")

            logger.info(f"数据获取成功，共 {len(data)} 条记录")

            # 创建策略
            strategy = self._create_strategy(strategy_name, strategy_params or {})

            # 创建回测配置
            backtest_config = self._create_backtest_config(
                start_date, end_date, backtest_params or {}
            )

            # 运行策略
            logger.info(f"运行策略: {strategy_name}")
            result = strategy.run(data, backtest_config)

            # 转换结果为字典
            result_dict = result.to_dict()
            result_dict['data_points'] = len(data)
            result_dict['strategy_params'] = strategy_params
            result_dict['backtest_params'] = backtest_params

            logger.info(f"策略运行完成，总收益: {result.total_return:.2%}")

            return result_dict

        except Exception as e:
            logger.error(f"策略运行失败: {e}")
            raise

    def _create_strategy(self, strategy_name: str, params: Dict[str, Any]):
        """创建策略实例"""
        strategy_name = strategy_name.lower()

        if strategy_name == "macd":
            config = MACDConfig(**params)
            return MACDStrategy(config)
        elif strategy_name == "rsi":
            config = RSIConfig(**params)
            return RSIStrategy(config)
        else:
            raise ValueError(f"不支持的策略: {strategy_name}")

    def _create_backtest_config(
        self,
        start_date: str,
        end_date: str,
        params: Dict[str, Any]
    ) -> BacktestConfig:
        """创建回测配置"""
        default_params = {
            'start_date': start_date,
            'end_date': end_date,
            'initial_cash': 100000.0,
            'commission': 0.0005,
            'slippage': 0.001
        }

        # 合并参数
        config_params = {**default_params, **params}

        return BacktestConfig(**config_params)

    def get_available_symbols(self, market: Optional[str] = None) -> List[str]:
        """
        获取可用的股票列表

        Args:
            market: 市场代码 ("sh", "sz")

        Returns:
            股票代码列表
        """
        try:
            symbols = self.data_source.get_universe(market=market)
            logger.info(f"获取到 {len(symbols)} 个股票代码")
            return symbols
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []

    def get_strategy_params(self, strategy_name: str) -> Dict[str, Any]:
        """
        获取策略参数说明

        Args:
            strategy_name: 策略名称

        Returns:
            参数说明字典
        """
        strategy_name = strategy_name.lower()

        if strategy_name == "macd":
            return {
                'description': MACDStrategy.get_param_description(),
                'constraints': MACDStrategy.get_param_constraints()
            }
        elif strategy_name == "rsi":
            return {
                'description': RSIStrategy.get_param_description(),
                'constraints': RSIStrategy.get_param_constraints()
            }
        else:
            raise ValueError(f"不支持的策略: {strategy_name}")

    def batch_test(
        self,
        strategy_name: str,
        symbols: List[str],
        start_date: str,
        end_date: str,
        strategy_params: Optional[Dict[str, Any]] = None,
        backtest_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        批量回测多个股票

        Args:
            strategy_name: 策略名称
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            strategy_params: 策略参数
            backtest_params: 回测参数

        Returns:
            {symbol: result} 字典
        """
        results = {}

        for symbol in symbols:
            try:
                logger.info(f"回测股票: {symbol}")
                result = self.run_strategy(
                    strategy_name=strategy_name,
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    strategy_params=strategy_params,
                    backtest_params=backtest_params
                )
                results[symbol] = result

            except Exception as e:
                logger.error(f"股票 {symbol} 回测失败: {e}")
                results[symbol] = {'error': str(e)}

        return results

    def optimize_strategy(
        self,
        strategy_name: str,
        symbol: str,
        start_date: str,
        end_date: str,
        param_space: Dict[str, List],
        objective: str = 'sharpe_ratio',
        method: str = 'grid',
        n_trials: int = 100,
        validation_split: float = 0.2
    ) -> Dict[str, Any]:
        """
        策略参数优化

        Args:
            strategy_name: 策略名称
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            param_space: 参数空间
            objective: 优化目标
            method: 优化方法 ('grid', 'random', 'bayesian')
            n_trials: 试验次数
            validation_split: 验证集比例

        Returns:
            优化结果
        """
        try:
            logger.info(f"开始参数优化: {strategy_name} on {symbol}")

            # 获取数据
            data = self.data_source.get_daily_data(symbol, start_date, end_date)
            if data.empty:
                raise ValueError(f"未获取到 {symbol} 的数据")

            # 数据分割
            if validation_split > 0:
                split_idx = int(len(data) * (1 - validation_split))
                train_data = data.iloc[:split_idx]
                val_data = data.iloc[split_idx:]
            else:
                train_data = data
                val_data = None

            # 选择优化器
            if method == 'grid':
                from core.optimizer.grid import GridSearchOptimizer
                strategy_class = self._get_strategy_class(strategy_name)

                optimizer = GridSearchOptimizer(
                    strategy_class=strategy_class,
                    param_space=param_space,
                    objective=objective,
                    direction='maximize' if objective in ['sharpe_ratio', 'total_return'] else 'minimize'
                )

                # 限制试验次数
                if len(optimizer.param_combinations) > n_trials:
                    import random
                    optimizer.param_combinations = random.sample(
                        optimizer.param_combinations, n_trials
                    )

                result = optimizer.optimize(train_data, validation_split=0)

            elif method == 'random':
                result = self._random_search_optimize(
                    strategy_name, train_data, param_space, objective, n_trials
                )
            elif method == 'bayesian':
                result = self._bayesian_optimize(
                    strategy_name, train_data, param_space, objective, n_trials
                )
            else:
                raise ValueError(f"不支持的优化方法: {method}")

            # 在验证集上测试最佳参数
            if val_data is not None and result.get('best_params'):
                val_result = self.run_strategy(
                    strategy_name=strategy_name,
                    symbol=symbol,
                    start_date=val_data.index[0].strftime('%Y-%m-%d'),
                    end_date=val_data.index[-1].strftime('%Y-%m-%d'),
                    strategy_params=result['best_params']
                )
                result['validation_result'] = val_result

            logger.info(f"参数优化完成: 最佳{objective} = {result.get('best_score', 0):.4f}")
            return result

        except Exception as e:
            logger.error(f"参数优化失败: {e}")
            raise

    def _get_strategy_class(self, strategy_name: str):
        """获取策略类"""
        strategy_name = strategy_name.lower()
        if strategy_name == "macd":
            return MACDStrategy
        elif strategy_name == "rsi":
            return RSIStrategy
        else:
            raise ValueError(f"不支持的策略: {strategy_name}")

    def _random_search_optimize(
        self,
        strategy_name: str,
        data,
        param_space: Dict[str, List],
        objective: str,
        n_trials: int
    ) -> Dict[str, Any]:
        """随机搜索优化"""
        import random

        best_params = None
        best_score = float('-inf') if objective in ['sharpe_ratio', 'total_return'] else float('inf')
        results = []

        for i in range(n_trials):
            # 随机选择参数
            params = {}
            for param_name, param_values in param_space.items():
                params[param_name] = random.choice(param_values)

            try:
                # 创建策略并运行
                strategy = self._create_strategy(strategy_name, params)
                result = strategy.run(data)

                score = getattr(result, objective, 0)
                results.append({
                    'params': params,
                    'score': score,
                    'result': result
                })

                # 更新最佳结果
                is_better = (
                    (objective in ['sharpe_ratio', 'total_return'] and score > best_score) or
                    (objective in ['max_drawdown', 'volatility'] and score < best_score)
                )

                if is_better:
                    best_score = score
                    best_params = params

            except Exception as e:
                logger.warning(f"随机搜索试验失败: {e}")
                continue

        return {
            'best_params': best_params,
            'best_score': best_score,
            'method': 'random_search',
            'n_trials': len(results),
            'all_results': results
        }

    def _bayesian_optimize(
        self,
        strategy_name: str,
        data,
        param_space: Dict[str, List],
        objective: str,
        n_trials: int
    ) -> Dict[str, Any]:
        """贝叶斯优化（简化实现）"""
        # 这里可以集成scikit-optimize或optuna
        logger.warning("贝叶斯优化暂未完全实现，使用随机搜索替代")
        return self._random_search_optimize(strategy_name, data, param_space, objective, n_trials)

    def analyze_strategy_performance(
        self,
        strategy_name: str,
        symbol: str,
        start_date: str,
        end_date: str,
        strategy_params: Optional[Dict[str, Any]] = None,
        benchmark_symbol: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        深度策略绩效分析

        Args:
            strategy_name: 策略名称
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            strategy_params: 策略参数
            benchmark_symbol: 基准股票代码

        Returns:
            详细分析结果
        """
        try:
            logger.info(f"开始策略绩效分析: {strategy_name} on {symbol}")

            # 运行策略
            result = self.run_strategy(
                strategy_name=strategy_name,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                strategy_params=strategy_params
            )

            # 获取原始数据
            data = self.data_source.get_daily_data(symbol, start_date, end_date)

            # 基准分析
            benchmark_analysis = None
            if benchmark_symbol:
                benchmark_data = self.data_source.get_daily_data(benchmark_symbol, start_date, end_date)
                if not benchmark_data.empty:
                    benchmark_returns = benchmark_data['close'].pct_change().dropna()
                    benchmark_total_return = (1 + benchmark_returns).prod() - 1
                    benchmark_volatility = benchmark_returns.std() * (252 ** 0.5)

                    benchmark_analysis = {
                        'symbol': benchmark_symbol,
                        'total_return': benchmark_total_return,
                        'volatility': benchmark_volatility,
                        'sharpe_ratio': benchmark_total_return / benchmark_volatility if benchmark_volatility > 0 else 0
                    }

            # 月度分析
            monthly_analysis = self._calculate_monthly_performance(data, result)

            # 年度分析
            yearly_analysis = self._calculate_yearly_performance(data, result)

            # 回撤分析
            drawdown_analysis = self._calculate_drawdown_analysis(data, result)

            # 交易分析
            trade_analysis = self._calculate_trade_analysis(result)

            analysis_result = {
                'basic_metrics': result,
                'benchmark_analysis': benchmark_analysis,
                'monthly_analysis': monthly_analysis,
                'yearly_analysis': yearly_analysis,
                'drawdown_analysis': drawdown_analysis,
                'trade_analysis': trade_analysis,
                'data_quality': {
                    'total_days': len(data),
                    'trading_days': len(data.dropna()),
                    'data_completeness': len(data.dropna()) / len(data) if len(data) > 0 else 0
                }
            }

            logger.info("策略绩效分析完成")
            return analysis_result

        except Exception as e:
            logger.error(f"策略绩效分析失败: {e}")
            raise

    def _calculate_monthly_performance(self, data, result) -> Dict[str, Any]:
        """计算月度绩效"""
        try:
            monthly_returns = data['close'].resample('M').last().pct_change().dropna()

            return {
                'monthly_returns': monthly_returns.to_dict(),
                'best_month': monthly_returns.max(),
                'worst_month': monthly_returns.min(),
                'positive_months': (monthly_returns > 0).sum(),
                'total_months': len(monthly_returns),
                'monthly_win_rate': (monthly_returns > 0).mean()
            }
        except Exception:
            return {}

    def _calculate_yearly_performance(self, data, result) -> Dict[str, Any]:
        """计算年度绩效"""
        try:
            yearly_returns = data['close'].resample('Y').last().pct_change().dropna()

            return {
                'yearly_returns': yearly_returns.to_dict(),
                'best_year': yearly_returns.max(),
                'worst_year': yearly_returns.min(),
                'positive_years': (yearly_returns > 0).sum(),
                'total_years': len(yearly_returns)
            }
        except Exception:
            return {}

    def _calculate_drawdown_analysis(self, data, result) -> Dict[str, Any]:
        """计算回撤分析"""
        try:
            returns = data['close'].pct_change().dropna()
            cumulative = (1 + returns).cumprod()
            peak = cumulative.expanding().max()
            drawdown = (cumulative - peak) / peak

            # 回撤期分析
            drawdown_periods = []
            in_drawdown = False
            start_date = None

            for date, dd in drawdown.items():
                if dd < 0 and not in_drawdown:
                    in_drawdown = True
                    start_date = date
                elif dd >= 0 and in_drawdown:
                    in_drawdown = False
                    if start_date:
                        drawdown_periods.append({
                            'start': start_date,
                            'end': date,
                            'duration': (date - start_date).days,
                            'max_drawdown': drawdown[start_date:date].min()
                        })

            return {
                'max_drawdown': drawdown.min(),
                'avg_drawdown': drawdown[drawdown < 0].mean() if (drawdown < 0).any() else 0,
                'drawdown_periods': len(drawdown_periods),
                'longest_drawdown_days': max([p['duration'] for p in drawdown_periods]) if drawdown_periods else 0,
                'recovery_factor': abs(result['total_return'] / drawdown.min()) if drawdown.min() < 0 else float('inf')
            }
        except Exception:
            return {}

    def _calculate_trade_analysis(self, result) -> Dict[str, Any]:
        """计算交易分析"""
        try:
            if not hasattr(result, 'signals') or result.signals.empty:
                return {}

            signals = result.signals
            trades = signals[signals['signal'] != 0]

            if trades.empty:
                return {'total_trades': 0}

            # 交易频率分析
            trade_intervals = trades.index.to_series().diff().dt.days.dropna()

            return {
                'total_trades': len(trades),
                'buy_signals': (trades['signal'] > 0).sum(),
                'sell_signals': (trades['signal'] < 0).sum(),
                'avg_trade_interval': trade_intervals.mean() if len(trade_intervals) > 0 else 0,
                'min_trade_interval': trade_intervals.min() if len(trade_intervals) > 0 else 0,
                'max_trade_interval': trade_intervals.max() if len(trade_intervals) > 0 else 0,
                'avg_signal_strength': trades.get('strength', pd.Series()).mean()
            }
        except Exception:
            return {}

    def get_engine_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            'engine_stats': self._stats.copy(),
            'data_source_type': self.data_source_type,
            'cache_size': {
                'results_cache': len(self._results_cache),
                'data_cache': len(self._data_cache)
            }
        }

    def clear_cache(self):
        """清空缓存"""
        self._results_cache.clear()
        self._data_cache.clear()
        logger.info("引擎缓存已清空")


def demo_single_strategy():
    """演示单个策略回测"""
    print("=== 单策略回测演示 ===")

    # 创建引擎
    engine = QuantEngine(data_source="mock")

    # MACD策略参数
    macd_params = {
        'fast_period': 12,
        'slow_period': 26,
        'signal_period': 9,
        'signal_threshold': 0.3
    }

    # 回测参数
    backtest_params = {
        'initial_cash': 100000,
        'commission': 0.0005
    }

    # 运行回测
    result = engine.run_strategy(
        strategy_name="macd",
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-12-31",
        strategy_params=macd_params,
        backtest_params=backtest_params
    )

    # 打印结果
    print(f"策略: {result['strategy_name']}")
    print(f"股票: {result['symbol']}")
    print(f"总收益: {result['total_return']:.2%}")
    print(f"年化收益: {result['annual_return']:.2%}")
    print(f"夏普比率: {result['sharpe_ratio']:.2f}")
    print(f"最大回撤: {result['max_drawdown']:.2%}")
    print(f"胜率: {result['win_rate']:.2%}")


def demo_batch_test():
    """演示批量回测"""
    print("\n=== 批量回测演示 ===")

    # 创建引擎
    engine = QuantEngine(data_source="mock")

    # 获取股票列表
    symbols = engine.get_available_symbols()[:3]  # 只测试前3个

    # RSI策略参数
    rsi_params = {
        'rsi_period': 14,
        'oversold_threshold': 30,
        'overbought_threshold': 70,
        'signal_threshold': 0.4
    }

    # 批量回测
    results = engine.batch_test(
        strategy_name="rsi",
        symbols=symbols,
        start_date="2023-01-01",
        end_date="2023-12-31",
        strategy_params=rsi_params
    )

    # 打印结果
    print(f"回测股票数量: {len(symbols)}")
    for symbol, result in results.items():
        if 'error' in result:
            print(f"{symbol}: 失败 - {result['error']}")
        else:
            print(f"{symbol}: 收益 {result['total_return']:.2%}, 夏普 {result['sharpe_ratio']:.2f}")


def demo_strategy_params():
    """演示策略参数查询"""
    print("\n=== 策略参数查询演示 ===")

    engine = QuantEngine()

    # 查询MACD策略参数
    macd_info = engine.get_strategy_params("macd")
    print("MACD策略参数:")
    for param, desc in macd_info['description'].items():
        constraints = macd_info['constraints'].get(param, {})
        print(f"  {param}: {desc} {constraints}")

    print("\nRSI策略参数:")
    rsi_info = engine.get_strategy_params("rsi")
    for param, desc in rsi_info['description'].items():
        constraints = rsi_info['constraints'].get(param, {})
        print(f"  {param}: {desc} {constraints}")


if __name__ == "__main__":
    try:
        # 运行演示
        demo_single_strategy()
        demo_batch_test()
        demo_strategy_params()

        print("\n=== 演示完成 ===")

    except Exception as e:
        logger.error(f"演示运行失败: {e}")
        print(f"错误: {e}")
