"""
核心数据结构模块

定义量化系统中使用的所有核心数据结构，包括K线数据、订单、持仓、交易记录等。
"""

from dataclasses import dataclass, field
from typing import Dict, Optional, Any
from datetime import datetime
from enum import Enum


class OrderSide(str, Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(str, Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    PARTIAL_FILLED = "partial_filled"


class PositionSide(str, Enum):
    """持仓方向"""
    LONG = "long"
    SHORT = "short"
    FLAT = "flat"


@dataclass
class OHLCV:
    """K线数据基础结构"""
    open: float
    high: float
    low: float
    close: float
    volume: float
    timestamp: datetime

    # 可选字段
    amount: Optional[float] = None  # 成交额
    vwap: Optional[float] = None    # 成交量加权平均价

    def __post_init__(self):
        """数据验证"""
        if self.high < max(self.open, self.close):
            raise ValueError("最高价不能小于开盘价或收盘价")
        if self.low > min(self.open, self.close):
            raise ValueError("最低价不能大于开盘价或收盘价")
        if self.volume < 0:
            raise ValueError("成交量不能为负数")

    @property
    def is_up(self) -> bool:
        """是否上涨"""
        return self.close > self.open

    @property
    def change_ratio(self) -> float:
        """涨跌幅"""
        if self.open == 0:
            return 0.0
        return (self.close - self.open) / self.open

    @property
    def amplitude(self) -> float:
        """振幅"""
        if self.open == 0:
            return 0.0
        return (self.high - self.low) / self.open


@dataclass
class Order:
    """订单数据结构"""
    order_id: str
    symbol: str
    side: OrderSide
    price: float
    quantity: float
    timestamp: datetime

    # 可选字段
    order_type: str = "market"
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    commission: float = 0.0
    slippage: float = 0.0

    # 策略相关
    strategy_id: Optional[str] = None
    signal_id: Optional[str] = None

    # 风控相关
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

    def __post_init__(self):
        """数据验证"""
        if self.quantity <= 0:
            raise ValueError("订单数量必须大于0")
        if self.price <= 0:
            raise ValueError("订单价格必须大于0")

    @property
    def is_filled(self) -> bool:
        """是否完全成交"""
        return self.status == OrderStatus.FILLED

    @property
    def remaining_quantity(self) -> float:
        """剩余数量"""
        return self.quantity - self.filled_quantity

    @property
    def fill_ratio(self) -> float:
        """成交比例"""
        return self.filled_quantity / self.quantity if self.quantity > 0 else 0.0

    @property
    def total_amount(self) -> float:
        """订单总金额"""
        return self.price * self.quantity

    @property
    def filled_amount(self) -> float:
        """已成交金额"""
        return self.avg_fill_price * self.filled_quantity


@dataclass
class Position:
    """持仓数据结构"""
    symbol: str
    side: PositionSide
    quantity: float
    avg_price: float
    timestamp: datetime

    # 可选字段
    market_price: Optional[float] = None
    unrealized_pnl: Optional[float] = None
    realized_pnl: float = 0.0

    # 成本信息
    total_cost: float = 0.0
    commission: float = 0.0

    # 策略相关
    strategy_id: Optional[str] = None

    def __post_init__(self):
        """数据验证和计算"""
        if self.avg_price <= 0:
            raise ValueError("持仓均价必须大于0")

        if self.total_cost == 0:
            self.total_cost = abs(self.quantity) * self.avg_price

    @property
    def market_value(self) -> float:
        """市值"""
        if self.market_price is None:
            return self.total_cost
        return abs(self.quantity) * self.market_price

    @property
    def is_long(self) -> bool:
        """是否多头"""
        return self.side == PositionSide.LONG

    @property
    def is_short(self) -> bool:
        """是否空头"""
        return self.side == PositionSide.SHORT

    @property
    def is_flat(self) -> bool:
        """是否空仓"""
        return self.side == PositionSide.FLAT or self.quantity == 0

    def update_market_price(self, price: float):
        """更新市价和未实现盈亏"""
        self.market_price = price
        if self.is_long:
            self.unrealized_pnl = (price - self.avg_price) * self.quantity
        elif self.is_short:
            self.unrealized_pnl = (self.avg_price - price) * abs(self.quantity)
        else:
            self.unrealized_pnl = 0.0


@dataclass
class Trade:
    """交易记录数据结构"""
    trade_id: str
    symbol: str
    side: OrderSide
    price: float
    quantity: float
    timestamp: datetime

    # 关联信息
    order_id: str
    strategy_id: Optional[str] = None

    # 成本信息
    commission: float = 0.0
    slippage: float = 0.0

    # 盈亏信息
    pnl: Optional[float] = None

    @property
    def amount(self) -> float:
        """成交金额"""
        return self.price * self.quantity

    @property
    def total_cost(self) -> float:
        """总成本（含手续费）"""
        return self.amount + self.commission


@dataclass
class Signal:
    """交易信号数据结构"""
    signal_id: str
    symbol: str
    side: OrderSide
    strength: float  # 信号强度 0-1
    timestamp: datetime

    # 价格信息
    price: Optional[float] = None
    target_price: Optional[float] = None

    # 风控信息
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

    # 策略信息
    strategy_id: Optional[str] = None
    indicator_values: Dict[str, float] = field(default_factory=dict)

    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """数据验证"""
        if not 0 <= self.strength <= 1:
            raise ValueError("信号强度必须在0-1之间")


@dataclass
class Portfolio:
    """投资组合数据结构"""
    portfolio_id: str
    timestamp: datetime

    # 资金信息
    total_value: float
    cash: float
    positions_value: float

    # 持仓信息
    positions: Dict[str, Position] = field(default_factory=dict)

    # 绩效信息
    total_return: float = 0.0
    daily_return: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0

    # 风险指标
    max_drawdown: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0

    @property
    def position_count(self) -> int:
        """持仓数量"""
        return len([p for p in self.positions.values() if not p.is_flat])

    @property
    def leverage(self) -> float:
        """杠杆率"""
        if self.total_value == 0:
            return 0.0
        return self.positions_value / self.total_value

    def get_position(self, symbol: str) -> Optional[Position]:
        """获取持仓"""
        return self.positions.get(symbol)

    def add_position(self, position: Position):
        """添加持仓"""
        self.positions[position.symbol] = position

    def remove_position(self, symbol: str):
        """移除持仓"""
        if symbol in self.positions:
            del self.positions[symbol]


__all__ = [
    "OHLCV",
    "Order",
    "Position",
    "Trade",
    "Signal",
    "Portfolio",
    "OrderSide",
    "OrderStatus",
    "PositionSide"
]