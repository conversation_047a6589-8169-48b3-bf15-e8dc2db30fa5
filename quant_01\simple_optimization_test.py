"""
简单的参数优化测试

直接测试优化器功能，不依赖复杂的导入结构。
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 创建简单的测试数据
def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)
    
    # 生成模拟价格数据
    returns = np.random.normal(0.001, 0.02, len(dates))
    prices = 100 * np.exp(np.cumsum(returns))
    
    data = pd.DataFrame({
        'date': dates,
        'open': prices * (1 + np.random.normal(0, 0.001, len(dates))),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, len(dates)))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, len(dates)))),
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, len(dates))
    })
    
    data.set_index('date', inplace=True)
    return data

# 简单的MACD策略实现
class SimpleMACDStrategy:
    """简化的MACD策略"""
    
    def __init__(self, fast_period=12, slow_period=26, signal_period=9):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
    
    def calculate_macd(self, prices):
        """计算MACD指标"""
        ema_fast = prices.ewm(span=self.fast_period).mean()
        ema_slow = prices.ewm(span=self.slow_period).mean()
        macd = ema_fast - ema_slow
        signal = macd.ewm(span=self.signal_period).mean()
        histogram = macd - signal
        return macd, signal, histogram
    
    def generate_signals(self, data):
        """生成交易信号"""
        macd, signal, histogram = self.calculate_macd(data['close'])
        
        # 生成买卖信号
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['macd'] = macd
        signals['signal_line'] = signal
        
        # MACD上穿信号线为买入，下穿为卖出
        signals['signal'][1:] = np.where(
            (macd[1:] > signal[1:]) & (macd[:-1].values <= signal[:-1].values), 1, 0
        )
        signals['signal'][1:] = np.where(
            (macd[1:] < signal[1:]) & (macd[:-1].values >= signal[:-1].values), -1, 
            signals['signal'][1:]
        )
        
        return signals
    
    def backtest(self, data):
        """简单回测"""
        signals = self.generate_signals(data)
        
        # 计算收益
        returns = data['close'].pct_change()
        strategy_returns = signals['signal'].shift(1) * returns
        
        # 计算指标
        total_return = (1 + strategy_returns).prod() - 1
        annual_return = (1 + total_return) ** (252 / len(data)) - 1
        volatility = strategy_returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        cumulative = (1 + strategy_returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown
        }

# 简单的网格搜索优化器
class SimpleGridOptimizer:
    """简化的网格搜索优化器"""
    
    def __init__(self, strategy_class, param_space, objective='sharpe_ratio'):
        self.strategy_class = strategy_class
        self.param_space = param_space
        self.objective = objective
    
    def optimize(self, data):
        """执行优化"""
        import itertools
        
        # 生成所有参数组合
        keys = list(self.param_space.keys())
        values = list(self.param_space.values())
        combinations = list(itertools.product(*values))
        
        best_score = float('-inf')
        best_params = None
        results = []
        
        print(f"开始网格搜索: {len(combinations)} 个参数组合")
        
        for i, combination in enumerate(combinations):
            params = dict(zip(keys, combination))
            
            try:
                # 创建策略并回测
                strategy = self.strategy_class(**params)
                result = strategy.backtest(data)
                score = result[self.objective]
                
                results.append({
                    'params': params,
                    'score': score,
                    'result': result
                })
                
                if score > best_score:
                    best_score = score
                    best_params = params
                
                if (i + 1) % 10 == 0:
                    print(f"进度: {i + 1}/{len(combinations)}")
                    
            except Exception as e:
                print(f"参数组合 {params} 失败: {e}")
                continue
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': results
        }

def test_simple_optimization():
    """测试简单优化"""
    print("=" * 60)
    print("简单参数优化测试")
    print("=" * 60)
    
    # 创建测试数据
    print("创建测试数据...")
    data = create_test_data()
    print(f"数据创建完成: {len(data)} 条记录")
    
    # 定义参数空间
    param_space = {
        'fast_period': [8, 12, 16],
        'slow_period': [21, 26, 35],
        'signal_period': [5, 9, 12]
    }
    
    # 创建优化器
    optimizer = SimpleGridOptimizer(
        strategy_class=SimpleMACDStrategy,
        param_space=param_space,
        objective='sharpe_ratio'
    )
    
    # 执行优化
    print("\n开始参数优化...")
    start_time = datetime.now()
    
    result = optimizer.optimize(data)
    
    end_time = datetime.now()
    optimization_time = (end_time - start_time).total_seconds()
    
    # 输出结果
    print(f"\n优化完成! 耗时: {optimization_time:.2f}秒")
    print(f"最佳参数: {result['best_params']}")
    print(f"最佳夏普比率: {result['best_score']:.4f}")
    print(f"总共测试了 {len(result['all_results'])} 个参数组合")
    
    # 显示前5个最佳结果
    print("\n前5个最佳结果:")
    sorted_results = sorted(result['all_results'], key=lambda x: x['score'], reverse=True)
    for i, res in enumerate(sorted_results[:5]):
        print(f"{i+1}. 参数: {res['params']}, 夏普比率: {res['score']:.4f}")
    
    # 测试最佳参数
    print(f"\n使用最佳参数进行详细回测:")
    best_strategy = SimpleMACDStrategy(**result['best_params'])
    best_result = best_strategy.backtest(data)
    
    for key, value in best_result.items():
        if isinstance(value, float):
            print(f"{key}: {value:.4f}")
        else:
            print(f"{key}: {value}")
    
    print("\n=" * 60)
    print("测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    test_simple_optimization()
