"""
策略工厂模块

负责策略的注册、创建和管理。
"""

from typing import Dict, Type, Any, Optional, List
import importlib
import inspect

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from .base.strategy import BaseStrategy, StrategyConfig
from core.exceptions import StrategyException, StrategyNotRegisteredException
from utils.logger import get_logger

logger = get_logger(__name__)


class StrategyFactory:
    """策略工厂
    
    负责策略的注册、创建和管理。
    """
    
    def __init__(self):
        self._strategies: Dict[str, Type[BaseStrategy]] = {}
        self._configs: Dict[str, Type[StrategyConfig]] = {}
        
        # 初始化日志
        logger.info("策略工厂初始化完成")
    
    def register(
        self,
        name: str,
        strategy_class: Type[BaseStrategy],
        config_class: Optional[Type[StrategyConfig]] = None
    ):
        """注册策略
        
        Args:
            name: 策略名称
            strategy_class: 策略类
            config_class: 配置类，如果为None则使用StrategyConfig
        """
        if not issubclass(strategy_class, BaseStrategy):
            raise StrategyException(f"策略类必须继承自BaseStrategy: {strategy_class}")
        
        if config_class is None:
            config_class = StrategyConfig
        elif not issubclass(config_class, StrategyConfig):
            raise StrategyException(f"配置类必须继承自StrategyConfig: {config_class}")
        
        self._strategies[name] = strategy_class
        self._configs[name] = config_class
        
        logger.info(f"策略已注册: {name}")
    
    def unregister(self, name: str):
        """注销策略
        
        Args:
            name: 策略名称
        """
        if name in self._strategies:
            del self._strategies[name]
            del self._configs[name]
            logger.info(f"策略已注销: {name}")
        else:
            logger.warning(f"策略不存在: {name}")
    
    def create(self, name: str, config: Optional[Dict[str, Any]] = None, **kwargs) -> BaseStrategy:
        """创建策略实例
        
        Args:
            name: 策略名称
            config: 策略配置字典
            **kwargs: 额外的配置参数
            
        Returns:
            策略实例
        """
        if name not in self._strategies:
            raise StrategyNotRegisteredException(f"未注册的策略: {name}")
        
        strategy_class = self._strategies[name]
        config_class = self._configs[name]
        
        try:
            # 创建配置对象
            if config is None:
                config = {}
            
            # 合并kwargs到config
            config.update(kwargs)
            
            # 确保strategy_name被设置
            if 'strategy_name' not in config:
                config['strategy_name'] = name
            
            config_obj = config_class(**config)
            
            # 创建策略实例
            strategy = strategy_class(config_obj)
            
            logger.info(f"策略实例已创建: {name}")
            return strategy
            
        except Exception as e:
            logger.error(f"策略创建失败: {name}, 错误: {e}")
            raise StrategyException(f"策略创建失败: {e}")
    
    def list_available(self) -> Dict[str, str]:
        """列出可用的策略
        
        Returns:
            策略名称到类名的映射
        """
        return {name: cls.__name__ for name, cls in self._strategies.items()}
    
    def is_available(self, name: str) -> bool:
        """检查策略是否可用
        
        Args:
            name: 策略名称
            
        Returns:
            是否可用
        """
        return name in self._strategies
    
    def get_strategy_info(self, name: str) -> Dict[str, Any]:
        """获取策略信息
        
        Args:
            name: 策略名称
            
        Returns:
            策略信息
        """
        if name not in self._strategies:
            return {'error': f'策略不存在: {name}'}
        
        strategy_class = self._strategies[name]
        config_class = self._configs[name]
        
        # 获取配置字段信息
        config_fields = {}
        if hasattr(config_class, '__fields__'):
            for field_name, field_info in config_class.__fields__.items():
                config_fields[field_name] = {
                    'type': str(field_info.type_),
                    'default': field_info.default,
                    'description': field_info.field_info.description or '',
                }
        
        return {
            'name': name,
            'strategy_class': strategy_class.__name__,
            'config_class': config_class.__name__,
            'module': strategy_class.__module__,
            'doc': strategy_class.__doc__ or '无描述',
            'config_fields': config_fields,
        }
    
    def create_from_config(self, config: Dict[str, Any]) -> BaseStrategy:
        """从配置创建策略
        
        Args:
            config: 配置字典，必须包含'strategy_type'字段
            
        Returns:
            策略实例
        """
        if 'strategy_type' not in config:
            raise StrategyException("配置中缺少'strategy_type'字段")
        
        strategy_type = config['strategy_type']
        strategy_config = config.copy()
        del strategy_config['strategy_type']
        
        return self.create(strategy_type, strategy_config)
    
    def register_from_module(
        self,
        module_name: str,
        strategy_class_name: str,
        config_class_name: Optional[str] = None,
        strategy_name: Optional[str] = None
    ):
        """从模块注册策略
        
        Args:
            module_name: 模块名
            strategy_class_name: 策略类名
            config_class_name: 配置类名，可选
            strategy_name: 策略名称，默认使用类名的小写
        """
        try:
            module = importlib.import_module(module_name)
            strategy_class = getattr(module, strategy_class_name)
            
            config_class = None
            if config_class_name:
                config_class = getattr(module, config_class_name)
            
            if strategy_name is None:
                strategy_name = strategy_class_name.lower().replace('strategy', '')
            
            self.register(strategy_name, strategy_class, config_class)
            
        except ImportError as e:
            logger.error(f"模块导入失败: {module_name}, 错误: {e}")
            raise StrategyException(f"模块导入失败: {e}")
        except AttributeError as e:
            logger.error(f"类不存在: {strategy_class_name}, 错误: {e}")
            raise StrategyException(f"类不存在: {e}")
    
    def auto_register_from_package(self, package_name: str):
        """自动从包注册策略
        
        Args:
            package_name: 包名
        """
        try:
            package = importlib.import_module(package_name)
            
            # 遍历包中的所有模块
            for name in dir(package):
                obj = getattr(package, name)
                
                # 检查是否是策略类
                if (inspect.isclass(obj) and 
                    issubclass(obj, BaseStrategy) and 
                    obj != BaseStrategy):
                    
                    strategy_name = name.lower().replace('strategy', '')
                    
                    # 查找对应的配置类
                    config_class = None
                    config_name = name.replace('Strategy', 'Config')
                    if hasattr(package, config_name):
                        config_class = getattr(package, config_name)
                    
                    self.register(strategy_name, obj, config_class)
                    
        except ImportError as e:
            logger.error(f"包导入失败: {package_name}, 错误: {e}")
            raise StrategyException(f"包导入失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取工厂统计信息
        
        Returns:
            统计信息
        """
        return {
            'registered_strategies': len(self._strategies),
            'available_strategies': list(self._strategies.keys()),
        }
    
    def clear(self):
        """清空所有注册的策略"""
        self._strategies.clear()
        self._configs.clear()
        logger.info("所有策略已清空")
    
    def __len__(self) -> int:
        """返回注册策略数量"""
        return len(self._strategies)
    
    def __contains__(self, name: str) -> bool:
        """检查策略是否存在"""
        return name in self._strategies


# 全局策略工厂实例
strategy_factory = StrategyFactory()


# 便捷函数
def create_strategy(name: str, config: Optional[Dict[str, Any]] = None, **kwargs) -> BaseStrategy:
    """创建策略实例的便捷函数
    
    Args:
        name: 策略名称
        config: 策略配置
        **kwargs: 额外参数
        
    Returns:
        策略实例
    """
    return strategy_factory.create(name, config, **kwargs)


def list_strategies() -> Dict[str, str]:
    """列出可用策略的便捷函数
    
    Returns:
        策略名称到类名的映射
    """
    return strategy_factory.list_available()


def register_strategy(
    name: str,
    strategy_class: Type[BaseStrategy],
    config_class: Optional[Type[StrategyConfig]] = None
):
    """注册策略的便捷函数
    
    Args:
        name: 策略名称
        strategy_class: 策略类
        config_class: 配置类
    """
    strategy_factory.register(name, strategy_class, config_class)
