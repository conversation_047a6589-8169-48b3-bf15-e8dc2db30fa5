"""
交易数据结构

定义订单、持仓、交易等交易相关的数据结构。
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
import uuid


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"      # 市价单
    LIMIT = "limit"        # 限价单
    STOP = "stop"          # 止损单
    STOP_LIMIT = "stop_limit"  # 止损限价单


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"        # 待处理
    SUBMITTED = "submitted"    # 已提交
    PARTIAL_FILLED = "partial_filled"  # 部分成交
    FILLED = "filled"          # 完全成交
    CANCELLED = "cancelled"    # 已取消
    REJECTED = "rejected"      # 已拒绝
    EXPIRED = "expired"        # 已过期


class PositionSide(Enum):
    """持仓方向"""
    LONG = "long"
    SHORT = "short"
    FLAT = "flat"


@dataclass
class Order:
    """订单数据结构
    
    优化特性：
    - 完整的订单生命周期管理
    - 支持多种订单类型
    - 详细的成交信息记录
    - 风险控制字段
    """
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    timestamp: datetime
    
    # 价格信息
    price: Optional[float] = None  # 限价单价格
    stop_price: Optional[float] = None  # 止损价格
    
    # 执行信息
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    remaining_quantity: Optional[float] = None
    
    # 费用信息
    commission: float = 0.0
    slippage: float = 0.0
    total_cost: float = 0.0
    
    # 策略信息
    strategy_id: Optional[str] = None
    signal_id: Optional[str] = None
    
    # 风控信息
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # 时间信息
    submit_time: Optional[datetime] = None
    fill_time: Optional[datetime] = None
    cancel_time: Optional[datetime] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if self.remaining_quantity is None:
            self.remaining_quantity = self.quantity
        
        if not self.order_id:
            self.order_id = str(uuid.uuid4())
        
        self._validate_order()
    
    def _validate_order(self):
        """订单验证"""
        if self.quantity <= 0:
            raise ValueError("订单数量必须大于0")
        
        if self.order_type == OrderType.LIMIT and self.price is None:
            raise ValueError("限价单必须指定价格")
        
        if self.order_type in [OrderType.STOP, OrderType.STOP_LIMIT] and self.stop_price is None:
            raise ValueError("止损单必须指定止损价格")
    
    @property
    def is_buy(self) -> bool:
        """是否买单"""
        return self.side == OrderSide.BUY
    
    @property
    def is_sell(self) -> bool:
        """是否卖单"""
        return self.side == OrderSide.SELL
    
    @property
    def is_filled(self) -> bool:
        """是否完全成交"""
        return self.status == OrderStatus.FILLED
    
    @property
    def is_partial_filled(self) -> bool:
        """是否部分成交"""
        return self.status == OrderStatus.PARTIAL_FILLED
    
    @property
    def is_active(self) -> bool:
        """是否活跃状态"""
        return self.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIAL_FILLED]
    
    @property
    def fill_ratio(self) -> float:
        """成交比例"""
        return self.filled_quantity / self.quantity if self.quantity > 0 else 0.0
    
    def fill(self, quantity: float, price: float, timestamp: datetime):
        """订单成交"""
        if quantity <= 0:
            raise ValueError("成交数量必须大于0")
        
        if self.filled_quantity + quantity > self.quantity:
            raise ValueError("成交数量超过订单数量")
        
        # 更新成交信息
        total_filled_value = self.filled_quantity * self.avg_fill_price + quantity * price
        self.filled_quantity += quantity
        self.avg_fill_price = total_filled_value / self.filled_quantity
        self.remaining_quantity = self.quantity - self.filled_quantity
        
        # 更新状态
        if self.remaining_quantity <= 1e-8:  # 考虑浮点精度
            self.status = OrderStatus.FILLED
            self.remaining_quantity = 0.0
        else:
            self.status = OrderStatus.PARTIAL_FILLED
        
        # 更新时间
        if self.fill_time is None:
            self.fill_time = timestamp
    
    def cancel(self, timestamp: datetime):
        """取消订单"""
        if not self.is_active:
            raise ValueError("只能取消活跃状态的订单")
        
        self.status = OrderStatus.CANCELLED
        self.cancel_time = timestamp
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'status': self.status.value,
            'filled_quantity': self.filled_quantity,
            'avg_fill_price': self.avg_fill_price,
            'remaining_quantity': self.remaining_quantity,
            'commission': self.commission,
            'slippage': self.slippage,
            'total_cost': self.total_cost,
            'timestamp': self.timestamp,
            'submit_time': self.submit_time,
            'fill_time': self.fill_time,
            'cancel_time': self.cancel_time,
            'strategy_id': self.strategy_id,
            'signal_id': self.signal_id,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'metadata': self.metadata,
        }


@dataclass
class Position:
    """持仓数据结构
    
    优化特性：
    - 实时盈亏计算
    - 持仓成本跟踪
    - 风险指标监控
    """
    symbol: str
    side: PositionSide
    quantity: float
    avg_price: float
    timestamp: datetime
    
    # 成本信息
    total_cost: float = 0.0
    commission: float = 0.0
    
    # 盈亏信息
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    
    # 风险信息
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # 策略信息
    strategy_id: Optional[str] = None
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_long(self) -> bool:
        """是否多头持仓"""
        return self.side == PositionSide.LONG
    
    @property
    def is_short(self) -> bool:
        """是否空头持仓"""
        return self.side == PositionSide.SHORT
    
    @property
    def is_flat(self) -> bool:
        """是否空仓"""
        return self.side == PositionSide.FLAT or abs(self.quantity) < 1e-8
    
    @property
    def market_value(self) -> float:
        """市值（需要当前价格）"""
        return self.quantity * self.avg_price
    
    @property
    def total_pnl(self) -> float:
        """总盈亏"""
        return self.realized_pnl + self.unrealized_pnl
    
    def update_price(self, current_price: float):
        """更新当前价格并计算未实现盈亏"""
        if self.is_flat:
            self.unrealized_pnl = 0.0
            return
        
        if self.is_long:
            self.unrealized_pnl = (current_price - self.avg_price) * self.quantity
        else:
            self.unrealized_pnl = (self.avg_price - current_price) * abs(self.quantity)
    
    def add_position(self, quantity: float, price: float, commission: float = 0.0):
        """增加持仓"""
        if self.is_flat:
            self.quantity = quantity
            self.avg_price = price
            self.side = PositionSide.LONG if quantity > 0 else PositionSide.SHORT
        else:
            # 计算新的平均成本
            total_cost = self.quantity * self.avg_price + quantity * price
            self.quantity += quantity
            
            if abs(self.quantity) > 1e-8:
                self.avg_price = total_cost / self.quantity
            else:
                self.side = PositionSide.FLAT
                self.avg_price = 0.0
        
        self.commission += commission
        self.total_cost += quantity * price + commission
    
    def close_position(self, quantity: float, price: float, commission: float = 0.0):
        """平仓"""
        if abs(quantity) > abs(self.quantity):
            raise ValueError("平仓数量不能超过持仓数量")
        
        # 计算实现盈亏
        if self.is_long:
            pnl = (price - self.avg_price) * quantity
        else:
            pnl = (self.avg_price - price) * quantity
        
        self.realized_pnl += pnl - commission
        self.quantity -= quantity
        self.commission += commission
        
        # 检查是否完全平仓
        if abs(self.quantity) < 1e-8:
            self.side = PositionSide.FLAT
            self.quantity = 0.0
            self.unrealized_pnl = 0.0


@dataclass
class Trade:
    """交易记录数据结构"""
    trade_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: datetime
    
    # 关联信息
    order_id: Optional[str] = None
    strategy_id: Optional[str] = None
    
    # 费用信息
    commission: float = 0.0
    slippage: float = 0.0
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.trade_id:
            self.trade_id = str(uuid.uuid4())
    
    @property
    def is_buy(self) -> bool:
        return self.side == OrderSide.BUY
    
    @property
    def is_sell(self) -> bool:
        return self.side == OrderSide.SELL
    
    @property
    def total_value(self) -> float:
        """交易总价值"""
        return self.quantity * self.price
    
    @property
    def total_cost(self) -> float:
        """交易总成本（包含费用）"""
        return self.total_value + self.commission + self.slippage
