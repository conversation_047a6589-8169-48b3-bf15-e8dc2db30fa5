"""
均线交叉策略

基于快速移动平均线和慢速移动平均线的交叉信号进行交易。
当快线上穿慢线时买入，当快线下穿慢线时卖出。
"""

import pandas as pd
import vectorbt as vbt
from typing import Dict
from strategies.base import BaseStrategy


class MACross(BaseStrategy):
    """均线交叉策略"""

    def __init__(self, params=None):
        """
        初始化均线交叉策略

        参数:
            params (dict): 策略参数，包含：
                - fast_window (int): 快线周期，默认5
                - slow_window (int): 慢线周期，默认20
        """
        super().__init__(params)
        self.fast_window = self.get_param('fast_window', 5)
        self.slow_window = self.get_param('slow_window', 20)

    @staticmethod
    def get_params_schema():
        """获取参数验证模式"""
        return {
            "fast_window": {
                "type": "integer",
                "title": "快线周期",
                "description": "快速移动平均线的计算周期",
                "default": 5,
                "minimum": 2,
                "maximum": 100
            },
            "slow_window": {
                "type": "integer",
                "title": "慢线周期",
                "description": "慢速移动平均线的计算周期",
                "default": 20,
                "minimum": 5,
                "maximum": 200
            }
        }

    def run(self, data: pd.DataFrame) -> tuple:
        """
        运行均线交叉策略

        参数:
            data (pd.DataFrame): 包含OHLCV数据的DataFrame

        返回:
            tuple: (entries, exits) 买入和卖出信号
        """
        # 验证数据
        if not self.validate_data(data):
            raise ValueError("输入数据无效")

        # 验证参数
        if self.fast_window >= self.slow_window:
            raise ValueError("快线周期必须小于慢线周期")

        # 计算快速和慢速移动平均线
        fast_ma = vbt.MA.run(data['close'], window=self.fast_window)
        slow_ma = vbt.MA.run(data['close'], window=self.slow_window)

        # 生成交叉信号
        # 快线上穿慢线：买入信号
        entries = fast_ma.ma_above(slow_ma)
        # 快线下穿慢线：卖出信号
        exits = fast_ma.ma_below(slow_ma)

        # 确保信号是布尔类型
        entries = entries.astype(bool)
        exits = exits.astype(bool)

        # 处理NaN值（前slow_window-1个值通常是NaN）
        entries = entries.fillna(False)
        exits = exits.fillna(False)

        return entries, exits

    def get_indicators(self, data: pd.DataFrame) -> Dict[str, pd.Series]:
        """
        获取策略使用的技术指标

        参数:
            data (pd.DataFrame): 输入数据

        返回:
            dict: 技术指标字典
        """
        fast_ma = vbt.MA.run(data['close'], window=self.fast_window)
        slow_ma = vbt.MA.run(data['close'], window=self.slow_window)

        return {
            'fast_ma': fast_ma.ma,
            'slow_ma': slow_ma.ma,
            'price': data['close']
        }
